// 订单相关常量定义

// 订单类型枚举
export const OrderType = {
  SAMPLE_SUBMISSION: 1, // 送样申请
  INSTRUMENT_RESERVATION_ENGINEER: 2, // 预约检测-工程师操作
  INSTRUMENT_RESERVATION_SELF: 3, // 预约检测-自主操作
  DEMAND_ORDER: 4, // 需求订单
}

// 订单类型文本映射
export const OrderTypeText = {
  [OrderType.SAMPLE_SUBMISSION]: '送样申请',
  [OrderType.INSTRUMENT_RESERVATION_ENGINEER]: '预约检测-工程师操作',
  [OrderType.INSTRUMENT_RESERVATION_SELF]: '预约检测-自主操作',
  [OrderType.DEMAND_ORDER]: '需求订单',
}

// 订单状态枚举
export const OrderStatus = {
  DRAFT: 0, // 草稿
  PENDING_REVIEW: 1, // 待审核
  PENDING_CONFIRM: 2, // 待确认
  PENDING_EXPERIMENT: 3, // 待实验
  EXPERIMENTING: 4, // 实验中
  PENDING_SETTLEMENT: 5, // 待结算
  SETTLED: 6, // 已结算
  COMPLETED: 7, // 已完成
  CANCELLED: 8, // 已取消
  CLOSED: 9, // 已关闭
  REJECTED: 10, // 已驳回
  REFUNDED: 11, // 已退款
  TRAINING_PASSED: 12, // 培训通过
  TRAINING_FAILED: 13, // 培训不通过
}

// 订单状态文本映射
export const OrderStatusText = {
  [OrderStatus.DRAFT]: '草稿',
  [OrderStatus.PENDING_REVIEW]: '待审核',
  [OrderStatus.PENDING_CONFIRM]: '待确认',
  [OrderStatus.PENDING_EXPERIMENT]: '待实验',
  [OrderStatus.EXPERIMENTING]: '实验中',
  [OrderStatus.PENDING_SETTLEMENT]: '待结算',
  [OrderStatus.SETTLED]: '已结算',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.CLOSED]: '已关闭',
  [OrderStatus.REJECTED]: '已驳回',
  [OrderStatus.REFUNDED]: '已退款',
  [OrderStatus.TRAINING_PASSED]: '培训通过',
  [OrderStatus.TRAINING_FAILED]: '培训不通过',
}

// 实验类型枚举
export const ExperimentType = {
  TRAINING: 1, // 培训
  TRIAL: 2, // 试测
  FORMAL: 3, // 正式
}

// 实验类型文本映射
export const ExperimentTypeText = {
  [ExperimentType.TRAINING]: '培训',
  [ExperimentType.TRIAL]: '试测',
  [ExperimentType.FORMAL]: '正式',
}

// 付款方式枚举
export const PayType = {
  PERSONAL_ACCOUNT: 1, // 个人账户
  TEAM_BALANCE: 2, // 团队余额
  TEAM_CREDIT: 3, // 团队额度
}

// 付款方式文本映射
export const PayTypeText = {
  [PayType.PERSONAL_ACCOUNT]: '个人账户',
  [PayType.TEAM_BALANCE]: '团队余额',
  [PayType.TEAM_CREDIT]: '团队额度',
}

// 样品寄回枚举
export const SampleReturnable = {
  NO: 0, // 不寄回
  YES: 1, // 寄回
}

// 样品寄回文本映射
export const SampleReturnableText = {
  [SampleReturnable.NO]: '不寄回',
  [SampleReturnable.YES]: '寄回',
}

// 邮寄状态枚举
export const ShipStatus = {
  SELF_DELIVERY: 0, // 自带
  PENDING_SHIP: 1, // 待寄出
  SHIPPED: 2, // 已寄出
  RECEIVED: 3, // 已签收
  RETURNED: 4, // 已寄回
}

// 邮寄状态文本映射
export const ShipStatusText = {
  [ShipStatus.SELF_DELIVERY]: '自带',
  [ShipStatus.PENDING_SHIP]: '待寄出',
  [ShipStatus.SHIPPED]: '已寄出',
  [ShipStatus.RECEIVED]: '已签收',
  [ShipStatus.RETURNED]: '已寄回',
}

// 订单提交状态枚举
export const SubmitStatus = {
  DRAFT: 0, // 草稿
  SUBMIT: 1, // 提交
}

// 订单提交状态文本映射
export const SubmitStatusText = {
  [SubmitStatus.DRAFT]: '草稿',
  [SubmitStatus.SUBMIT]: '提交',
}

// 节假日类型枚举
export const ScheduleType = {
  HOLIDAY: 1, // 节假日
  WORKDAY: 2, // 调休日
  WEEKEND: 3, // 正常周末
}

// 节假日类型文本映射
export const ScheduleTypeText = {
  [ScheduleType.HOLIDAY]: '节假日',
  [ScheduleType.WORKDAY]: '工作日',
  [ScheduleType.WEEKEND]: '正常周末',
}

// 节假日状态枚举
export const ScheduleStatus = {
  NORMAL: 1, // 正常
  DISABLED: 2, // 禁用
}

// 节假日状态文本映射
export const ScheduleStatusText = {
  [ScheduleStatus.NORMAL]: '正常',
  [ScheduleStatus.DISABLED]: '禁用',
}