// 仪器相关常量定义

// 仪器状态枚举
export const InstrumentStatus = {
  ONLINE: 1, // 已上线
  OFFLINE: 2, // 已下线
  MAINTENANCE: 3, // 维护中
}

// 仪器状态文本映射
export const InstrumentStatusText = {
  [InstrumentStatus.ONLINE]: '已上线',
  [InstrumentStatus.OFFLINE]: '已下线',
  [InstrumentStatus.MAINTENANCE]: '维护中',
}

// 收费方式枚举
export const FeeMethod = {
  BY_HOUR: 1, // 按机时
  BY_SAMPLE: 2, // 按样品
}

// 收费方式文本映射
export const FeeMethodText = {
  [FeeMethod.BY_HOUR]: '按机时',
  [FeeMethod.BY_SAMPLE]: '按样品',
}

// 培训收费方式枚举
export const TrainFeeMethod = {
  BY_HOUR: 1, // 按机时
  BY_TIME: 2, // 按次数
}

// 培训收费方式文本映射
export const TrainFeeMethodText = {
  [TrainFeeMethod.BY_HOUR]: '按机时',
  [TrainFeeMethod.BY_TIME]: '按次数',
}

// 角色类型枚举
export const RoleType = {
  SUPER_ADMIN: 1, // 超级管理员
  ADMIN: 2, // 管理员
  INSTRUMENT_MANAGER: 3, // 仪器负责人
  INSTRUMENT_OPERATOR: 4, // 仪器操作员
  FINANCE: 5, // 财务
  LEVEL_1_USER: 6, // 1级用户
  LEVEL_2_USER: 7, // 2级用户
  LEVEL_3_USER: 8, // 3级用户
}

// 角色类型文本映射
export const RoleTypeText = {
  [RoleType.SUPER_ADMIN]: '超级管理员',
  [RoleType.ADMIN]: '管理员',
  [RoleType.INSTRUMENT_MANAGER]: '仪器负责人',
  [RoleType.INSTRUMENT_OPERATOR]: '仪器操作员',
  [RoleType.FINANCE]: '财务',
  [RoleType.LEVEL_1_USER]: '1级用户',
  [RoleType.LEVEL_2_USER]: '2级用户',
  [RoleType.LEVEL_3_USER]: '3级用户',
}

// 日期类型枚举
export const DateType = {
  CLOSE_ALL_DAY: 1, // 关闭整天
  CUSTOM_TIME_SLOTS: 2, // 自定义时间段
}

// 日期类型文本映射
export const DateTypeText = {
  [DateType.CLOSE_ALL_DAY]: '关闭整天',
  [DateType.CUSTOM_TIME_SLOTS]: '自定义时间段',
}

// 是否必选枚举
export const IsRequired = {
  NO: 0, // 否
  YES: 1, // 是
}

// 是否必选文本映射
export const IsRequiredText = {
  [IsRequired.NO]: '否',
  [IsRequired.YES]: '是',
}

// 状态枚举
export const Status = {
  NORMAL: 1, // 正常
  DISABLED: 2, // 禁用
}

// 状态文本映射
export const StatusText = {
  [Status.NORMAL]: '正常',
  [Status.DISABLED]: '禁用',
}

// 是否工作日枚举
export const IsWorkday = {
  NO: 0, // 否 - 自定义星期
  YES: 1, // 是 - 法定工作日
}

// 是否工作日文本映射
export const IsWorkdayText = {
  [IsWorkday.NO]: '自定义星期',
  [IsWorkday.YES]: '法定工作日',
}

// 星期枚举
export const Weekday = {
  MONDAY: 1, // 周一
  TUESDAY: 2, // 周二
  WEDNESDAY: 3, // 周三
  THURSDAY: 4, // 周四
  FRIDAY: 5, // 周五
  SATURDAY: 6, // 周六
  SUNDAY: 7, // 周日
}

// 星期文本映射
export const WeekdayText = {
  [Weekday.MONDAY]: '周一',
  [Weekday.TUESDAY]: '周二',
  [Weekday.WEDNESDAY]: '周三',
  [Weekday.THURSDAY]: '周四',
  [Weekday.FRIDAY]: '周五',
  [Weekday.SATURDAY]: '周六',
  [Weekday.SUNDAY]: '周日',
}

// 订单类型枚举
export const OrderType = {
  SAMPLE_SUBMISSION: 1, // 送样申请
  INSTRUMENT_RESERVATION_ENGINEER: 2, // 仪器预约-工程师操作
  INSTRUMENT_RESERVATION_SELF: 3, // 仪器预约-自主操作
}

// 订单类型文本映射
export const OrderTypeText = {
  [OrderType.SAMPLE_SUBMISSION]: '送样申请',
  [OrderType.INSTRUMENT_RESERVATION_ENGINEER]: '仪器预约-工程师操作',
  [OrderType.INSTRUMENT_RESERVATION_SELF]: '仪器预约-自主操作',
}