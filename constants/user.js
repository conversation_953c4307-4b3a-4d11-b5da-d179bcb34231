// 用户相关常量定义

// 用户角色类型枚举
export const UserRoleType = {
  SUPER_ADMIN: 1, // 超级管理员
  ADMIN: 2, // 管理员
  INSTRUMENT_MANAGER: 3, // 仪器负责人
  INSTRUMENT_OPERATOR: 4, // 仪器操作员
  FINANCE: 5, // 财务
  LEVEL_1_USER: 6, // 1级用户
  LEVEL_2_USER: 7, // 2级用户
  LEVEL_3_USER: 8, // 3级用户
}

// 用户角色类型文本映射
export const UserRoleTypeText = {
  [UserRoleType.SUPER_ADMIN]: '超级管理员',
  [UserRoleType.ADMIN]: '管理员',
  [UserRoleType.INSTRUMENT_MANAGER]: '仪器负责人',
  [UserRoleType.INSTRUMENT_OPERATOR]: '仪器操作员',
  [UserRoleType.FINANCE]: '财务',
  [UserRoleType.LEVEL_1_USER]: '1级用户',
  [UserRoleType.LEVEL_2_USER]: '2级用户',
  [UserRoleType.LEVEL_3_USER]: '3级用户',
}

// 用户状态枚举
export const UserStatus = {
  INACTIVE: 0, // 未激活
  ACTIVE: 1, // 激活
  SUSPENDED: 2, // 暂停
  BANNED: 3, // 禁用
}

// 用户状态文本映射
export const UserStatusText = {
  [UserStatus.INACTIVE]: '未激活',
  [UserStatus.ACTIVE]: '激活',
  [UserStatus.SUSPENDED]: '暂停',
  [UserStatus.BANNED]: '禁用',
}

// 身份认证类型枚举
export const IdentityType = {
  STUDENT: 1, // 学生
  TEACHER: 2, // 教师
  RESEARCHER: 3, // 研究员
  ENTERPRISE: 4, // 企业用户
  OTHER: 5, // 其他
}

// 身份认证类型文本映射
export const IdentityTypeText = {
  [IdentityType.STUDENT]: '学生',
  [IdentityType.TEACHER]: '教师',
  [IdentityType.RESEARCHER]: '研究员',
  [IdentityType.ENTERPRISE]: '企业用户',
  [IdentityType.OTHER]: '其他',
}