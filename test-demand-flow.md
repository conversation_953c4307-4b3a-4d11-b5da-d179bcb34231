# 需求订单流程测试

## 问题描述
点击确定后，返回需求订单页面，没有看见新增仪器信息

## 修改内容

### 1. 预约页面 (pages/instrument/reserve.vue)
- 隐藏需求订单模式下的暂时保存按钮
- 修改提交按钮文本为"确定"
- 实现连续返回两页功能
- 将预约数据保存到本地存储作为备份
- 完善预约数据结构，确保包含需求页面显示所需的所有字段

### 2. 需求页面 (pages/request/detection.vue)
- 在 onShow 时检查本地存储中的预约数据
- 修复价格显示逻辑
- 添加详细的调试日志

## 测试步骤

1. 进入需求页面 (pages/request/detection.vue)
2. 点击"添加仪器"按钮
3. 选择一个仪器
4. 在预约页面填写预约信息
5. 点击"确定"按钮
6. 检查是否返回到需求页面
7. 检查需求页面是否显示新增的仪器信息

## 调试信息

查看控制台输出：
- "返回需求订单页面，预约数据:"
- "预约数据已保存到本地存储:"
- "检查本地存储的预约数据:"
- "从本地存储中发现预约数据:"
- "处理预约数据:"
- "当前仪器列表:"
- "处理完成后的仪器列表:"

## 预期结果

1. 点击确定后显示"预约信息已添加"提示
2. 连续返回两页，直接回到需求页面
3. 需求页面显示新增的仪器预约信息，包括：
   - 仪器名称
   - 实验名称
   - 预约时间段数量
   - 预估金额

## 数据结构对比

### 预约页面构建的数据结构：
```javascript
{
  id: instrumentId,
  instrumentId: instrumentId,
  name: instrumentName,
  instrumentName: instrumentName,
  experimentName: formData.name,
  experimentType: experimentType,
  type: orderType,
  remark: formData.remarks,
  description: "实验类型：xxx",
  appointmentTimes: [...],
  actualDuration: actualConsumptionTime,
  services: [...],
  materials: [...],
  totalAmount: originalPrice,
  amount: discountedPrice,
  location: instrumentDetail.location,
  status: "已预约"
}
```

### 需求页面期望的数据结构：
```javascript
{
  instrumentId: number,
  instrumentName: string,
  experimentName: string,
  appointmentTimes: array,
  totalAmount: number,
  // 其他字段...
}
```
