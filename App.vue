<script>
  import * as token from '@/common/request/token.js'

  import notice from '@/common/utils/notice.js'
	import { mapState } from "vuex";
	import api from "@/api/modify";

  export default {
    computed: {
			...mapState(["userInfo", "userRole"]),
		},
    onLaunch(options) {
      uni.hideTabBar({
        animation: false
      })
      if (this.isAuditEnv) {
        if(this?.userInfo?.menuCodes?.includes('app_center') ?? false){
          uni.reLaunch({
            url: 'pages/index/index'
          });
        }else{
          // 审核环境下会隐藏对话菜单,所以初始页去数据空间
          if(this.isAuditEnv){
            uni.reLaunch({
              url: 'pages/index/index'
            });
          }else{
            // 根据角色决定跳转到哪个页面
            this.navigateByUserRole();
          }
        }
      }
      token.checkOnLaunch(options)
      // #ifdef APP-PLUS
      plus.screen.lockOrientation('portrait-primary')
      // #endif
    },
    onShow(options) {
      uni.hideTabBar({
        animation: false
      })
      this.navigateByUserRole();
      token.checkOnShow(options)
      this.checkForUpdate()
    },
    onHide() {
      notice.resetReject()
      notice.saveCounts()
    },
    methods: {
      // 根据用户角色决定跳转页面
      navigateByUserRole() {
        // 从store中获取用户角色
        const userRole = this.userRole || 'user';
        
        // 根据角色跳转到不同页面
        if (userRole === 'admin') {
          uni.reLaunch({
            url: 'pages/admin/orderManage'
          });
        } else {
          uni.reLaunch({
            url: 'pages/home/<USER>'
          });
        }
      },
      checkForUpdate() {
        // #ifdef MP-WEIXIN
        if (!wx.canIUse('getUpdateManager')) {
          return
        }
        const updateManager = wx.getUpdateManager()
        updateManager.onCheckForUpdate(res => {
          if (res.hasUpdate) {
            updateManager.onUpdateReady(() => {
              updateManager.applyUpdate()
            })
            updateManager.onUpdateFailed(() => {
              wx.showModal({
                title: '版本更新',
                content: `检测到小程序存在最新版本，为确保完整功能及体验，请删除后并搜索"松山湖材料实验室检测中心"重新进入`,
                showCancel: false,
              })
            })
          }
        })
        // #endif
      },
    },
  }
</script>

<style lang="scss">
  /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
  /* #ifndef APP-PLUS-NVUE */
  @import "uview-ui/index.scss";
  @import "@/common/styles/uview.scss"
  /* #endif */
</style>