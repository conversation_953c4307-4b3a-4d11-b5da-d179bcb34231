<svg width="375" height="145" viewBox="0 0 375 145" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_77936_49653" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="376" height="145">
<path d="M0 0H376V145H0V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_77936_49653)">
<g opacity="0.77" filter="url(#filter0_bf_77936_49653)">
<ellipse cx="177.106" cy="313.319" rx="177.106" ry="313.319" transform="matrix(0.987349 0.158561 -0.601514 0.798862 443.932 -468)" fill="#8CB3FE" fill-opacity="0.7"/>
</g>
</g>
<defs>
<filter id="filter0_bf_77936_49653" x="65.6362" y="-549.111" width="729.39" height="718.983" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="49.6"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_77936_49653"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_77936_49653" result="shape"/>
<feGaussianBlur stdDeviation="53.8" result="effect2_foregroundBlur_77936_49653"/>
</filter>
</defs>
</svg>
