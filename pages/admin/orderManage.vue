<template>
  <view class="order-list-container">
    <!-- 顶部搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <image src="/static/svg/search.svg" class="search-icon"></image>
        <input type="text" placeholder="请输入订单编号或实验名称" v-model="searchKeyword" @confirm="searchOrders" />
      </view>
    </view>

    <!-- 状态切换栏 -->
    <view class="status-tabs-container">
      <scroll-view class="status-tabs" scroll-x show-scrollbar="false">
        <view class="tabs-wrapper">
          <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: currentTab === index }"
            @tap="switchTab(index)">
            <text>{{ tab.name }}</text>
            <view class="tab-badge" v-if="tab.status === 1 && tab.count && tab.count > 0">{{ tab.count }}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list-wrapper">
      <lk-page-list ref="pageList" embed @mounted="onPageListMounted" :show-more-status="true"
        @showListChange="onShowListChange">
        <view class="order-list">
          <view class="order-item" v-for="(order, index) in orderList" :key="index" @tap="goToDetail(order.id)">
            <view class="order-header">
              <text class="instrument-name">{{ order.instrumentName || "等离子体光谱仪XP-821" }}</text>
              <view class="order-status" :class="'status-' + order.status">{{ getStatusText(order.status) }}</view>
            </view>

            <view class="order-info">
              <view class="info-row">
                <text class="info-label">订单编号：</text>
                <text class="info-value">{{ order.orderNo }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实验名称：</text>
                <text class="info-value">{{ order.experimentName }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">订单类型：</text>
                <text class="info-value">{{ getOrderTypeText(order.type) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实验类型：</text>
                <text class="info-value">{{ getExperimentTypeText(order.experimentType) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">样品信息：</text>
                <view class="sample-status-container">
                  <text v-if="getSampleStatusDisplay(order).type === 'text'" class="sample-status-text">{{
                    getSampleStatusDisplay(order).text }}</text>
                  <text v-else class="sample-status" :class="getSampleStatusDisplay(order).class">{{
                    getSampleStatusDisplay(order).text }}</text>
                  <text v-if="shouldShowSampleDetail(order)" class="sample-detail"
                    @tap.stop="goToDetail(order.id, 1)">详情</text>
                </view>
              </view>
              <view class="info-row">
                <text class="info-label">预约时间：</text>
                <text class="info-value">{{ formatAppointmentTime(order) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">创建时间：</text>
                <text class="info-value">{{ order.createTime }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">订单金额：</text>
                <text class="info-value price">¥{{ (order.totalAmount || 0).toFixed(2) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">创建用户：</text>
                <text class="contact-phone">{{ order.createUser.phoneNumber || '' }} ({{ order.createUser.name || ''
                  }})</text>
              </view>
              <view class="info-row">
                <text class="info-label">负责人：</text>
                <view class="responsible-person-container">
                  <text class="contact-phone" @tap.stop="showResponsiblePersons(order)"
                    v-if="order.responsiblePersons && order.responsiblePersons.length > 0">
                    {{ order.responsiblePersons[0].phoneNumber || '18812341234' }} ({{ order.responsiblePersons[0].name
                      || '李老师' }})
                    <text class="more-indicator" v-if="order.responsiblePersons.length > 1">等{{
                      order.responsiblePersons.length }}人</text>
                  </text>
                  <text class="contact-phone" v-else>
                    {{ order.userPhone || '18812341234' }} ({{ order.userName || '李老师' }})
                  </text>
                </view>
              </view>
            </view>

            <view class="order-footer">
              <!-- 根据订单状态和类型显示不同的操作按钮 -->
              <view class="btn-group" :class="{ 'btn-group-full': getVisibleButtonCount(order) >= 4 }">
                <!-- 待审核状态 -->
                <template v-if="order.status === 1">
                  <button class="btn btn-more" @tap.stop="showMoreActions(order.id)">更多</button>
                  <button class="btn" @tap.stop="approveOrder(order.id)">通过</button>
                  <button class="btn" @tap.stop="editOrder(order.id)">编辑</button>
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                </template>

                <!-- 待确认状态 -->
                <template v-else-if="order.status === 2">
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                  <button class="btn" @tap.stop="editOrder(order.id)">编辑</button>
                  <button class="btn" @tap.stop="closeOrder(order.id)">关闭订单</button>
                </template>

                <!-- 待实验状态 -->
                <template v-else-if="order.status === 3">
                  <button class="btn btn-more" @tap.stop="showMoreActions(order.id)">更多</button>
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                  <button class="btn" @tap.stop="editOrder(order.id)">编辑</button>
                  <button v-if="order.type === 1 || order.type === 2" class="btn"
                    @tap.stop="startExperiment(order.id)">上机</button>
                </template>

                <!-- 实验中状态 -->
                <template v-else-if="order.status === 4">
                  <button class="btn btn-more" @tap.stop="showMoreActions(order.id)">更多</button>
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                  <button class="btn" @tap.stop="editOrder(order.id)">编辑</button>
                  <button v-if="order.type === 1 || order.type === 2" class="btn"
                    @tap.stop="finishExperiment(order.id)">下机</button>
                </template>

                <!-- 待结算状态 -->
                <template v-else-if="order.status === 5">
                  <button class="btn btn-more" @tap.stop="showMoreActions(order.id)">更多</button>
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                  <button class="btn" @tap.stop="editOrder(order.id)">编辑</button>
                  <button class="btn" @tap.stop="closeOrder(order.id)">关闭订单</button>
                </template>

                <!-- 已完成状态 -->
                <template v-else-if="order.status === 7">
                  <button class="btn btn-more" @tap.stop="showMoreActions(order.id)">更多</button>
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                  <button class="btn" @tap.stop="uploadData(order.id)">上传数据</button>
                  <button class="btn" @tap.stop="refundOrder(order.id)">退款</button>
                </template>

                <!-- 其他状态只显示查看按钮 -->
                <template v-else>
                  <button class="btn" @tap.stop="goToDetail(order.id, 0)">查看</button>
                </template>
              </view>
            </view>
          </view>
        </view>
      </lk-page-list>
    </view>

    <!-- 负责人信息弹框 -->
    <view class="responsible-modal" v-if="showResponsibleModal" @tap="closeResponsibleModal">
      <view class="modal-content" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">负责人信息</text>
          <text class="modal-close" @tap="closeResponsibleModal">×</text>
        </view>
        <view class="modal-body">
          <view class="responsible-item" v-for="(person, index) in currentResponsiblePersons" :key="index">
            <text class="responsible-number">{{ index + 1 }}.</text>
            <text class="responsible-name">{{ person.name || '未知姓名' }}</text>
            <text class="responsible-phone">电话：{{ person.phoneNumber || '未知电话' }}</text>
          </view>
        </view>
        <view class="modal-footer">
          <button class="confirm-btn" @tap="closeResponsibleModal">确定</button>
        </view>
      </view>
    </view>

    <!-- 修改订单金额弹框 -->
    <view class="edit-price-modal" v-if="showEditPriceModalFlag" @tap="closeEditPriceModal">
      <view class="price-modal-content" @tap.stop>
        <view class="price-modal-header">
          <text class="price-modal-title">修改订单金额</text>
        </view>
        <view class="price-modal-body">
          <view class="price-info-row">
            <text class="price-label">当前订单折扣后金额：</text>
            <text class="price-value">{{ currentOrderPrice.toFixed(2) }}</text>
          </view>
          <view class="price-input-row">
            <text class="price-label">修改后金额：</text>
            <input class="price-input" type="digit" v-model="newOrderPrice" placeholder="请输入" @input="onPriceInput" />
          </view>
        </view>
        <view class="price-modal-footer">
          <button class="price-cancel-btn" @tap="closeEditPriceModal">取消</button>
          <button class="price-confirm-btn" @tap="confirmEditPrice">确定</button>
        </view>
      </view>
    </view>

    <!-- 底部TabBar -->
    <custom-tab-bar :selected="0" />
  </view>
</template>

<script>
import customTabBar from '@/components/custom-tab-bar/custom-tab-bar.vue';
import orderApi from '@/api/order';

export default {
  components: {
    customTabBar
  },
  data() {
    return {
      searchKeyword: '',
      currentTab: 0,
      tabs: [
        { name: '全部订单', status: 'all' },
        { name: '待审核', status: 1, count: 0 },
        { name: '待确认', status: 2 },
        { name: '待实验', status: 3 },
        { name: '实验中', status: 4 },
        { name: '待结算', status: 5 },
        { name: '已完成', status: 7 },
        { name: '已取消', status: 8 },
        { name: '已退款', status: 11 },
        { name: '已关闭', status: 9 },
        { name: '已驳回', status: 10 }
      ],
      orderList: [],
      currentStatus: 'all',
      isRefreshing: false,
      showResponsibleModal: false,
      currentResponsiblePersons: [],
      showEditPriceModalFlag: false,
      currentOrderPrice: 0,
      newOrderPrice: '',
      currentEditOrderId: null
    }
  },
  onLoad(options) {
    if (options.status) {
      const tabIndex = this.tabs.findIndex(tab => tab.status === options.status);
      if (tabIndex !== -1) {
        this.currentTab = tabIndex;
        this.currentStatus = this.tabs[tabIndex].status;
      }
    }
  },
  onShow() {
    const userRole = this.$store.state.userRole || 'user';
    if (userRole !== 'admin') {
      uni.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },
  methods: {
    onPageListMounted() {
      console.log('分页组件已挂载，开始配置');
      this.configPageList();
    },

    configPageList() {
      if (!this.$refs.pageList) {
        console.log('pageList ref 不存在');
        return;
      }

      console.log('开始配置分页列表');
      const self = this;

      this.orderList = this.$refs.pageList.config({
        request: async (params) => {
          console.log('=== API请求开始 ===');
          console.log('当前状态:', self.currentStatus);
          console.log('分页请求参数:', params);

          const requestParams = {
            current: params.current || 1,
            size: params.size || 10
          };

          if (self.currentStatus !== null && self.currentStatus !== 'all') {
            requestParams.status = self.currentStatus;
            console.log('添加状态筛选:', requestParams.status);
          }

          if (self.searchKeyword) {
            requestParams.searchKey = self.searchKeyword;
            console.log('添加搜索关键词:', requestParams.searchKey);
          }

          console.log('最终请求参数:', requestParams);

          try {
            const result = await orderApi.getOrderList(requestParams);
            console.log('分页请求结果:', result);
            console.log('=== API请求结束 ===');
            return result;
          } catch (error) {
            console.error('API调用失败:', error);
            throw error;
          }
        },
        onRefreshFinish: () => {
          console.log('刷新完成回调');
          if (self.$refs.pageList?.pageData?.list) {
            self.orderList = self.$refs.pageList.pageData.list;
            console.log('手动同步后的 orderList:', self.orderList);
          }
          self.updatePendingCountFromList();
          self.isRefreshing = false;
        },
        onLoadMoreFinish: () => {
          console.log('加载更多完成回调');
          if (self.$refs.pageList?.pageData?.list) {
            self.orderList = self.$refs.pageList.pageData.list;
          }
        }
      });

      console.log('config返回的orderList:', this.orderList);
      console.log('开始调用refresh');
      this.$refs.pageList.refresh(true);
    },

    switchTab(index) {
      if (this.currentTab === index) return;

      if (this.isRefreshing) {
        console.log('正在刷新中，跳过重复调用');
        return;
      }

      console.log('=== 切换Tab开始 ===');
      console.log('从tab', this.currentTab, '切换到tab', index);
      console.log('从状态', this.currentStatus, '切换到状态', this.tabs[index].status);

      this.currentTab = index;
      this.currentStatus = this.tabs[index].status;
      console.log('切换到状态:', this.currentStatus);

      if (this.$refs.pageList) {
        this.isRefreshing = true;
        console.log('开始刷新列表...');
        this.$refs.pageList.refresh(true);
      }
      console.log('=== 切换Tab结束 ===');
    },

    searchOrders() {
      if (this.$refs.pageList) {
        this.$refs.pageList.refresh(true);
      }
    },

    onShowListChange(list) {
      console.log('onShowListChange 被调用，数据:', list);
      this.orderList = list;
      console.log('更新后的 orderList:', this.orderList);
    },

    getStatusText(status) {
      const statusMap = {
        1: '待审核',
        2: '待确认',
        3: '待实验',
        4: '实验中',
        5: '待结算',
        6: '已结算',
        7: '已完成',
        8: '已取消',
        9: '已关闭',
        10: '已驳回',
        11: '已退款',
        12: '完成（培训通过）',
        13: '完成（培训不通过）'
      };
      return statusMap[status] || status;
    },

    getOrderTypeText(type) {
      const typeMap = {
        1: '送样申请',
        2: '仪器预约-工程师操作',
        3: '仪器预约-自主操作',
        4: '需求订单'
      };
      return typeMap[type] || type;
    },

    getExperimentTypeText(type) {
      const typeMap = {
        1: '培训',
        2: '试测',
        3: '正式'
      };
      return typeMap[type] || type;
    },

    goToDetail(id, tab = 0) {
      uni.navigateTo({
        url: `/pages/orders/orderDetail?id=${id}&tab=${tab}`
      });
    },

    // ========== 订单操作方法 ==========

    approveOrder(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定通过？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 2, '订单已通过，状态变更为待确认');
          }
        }
      });
    },

    rejectOrder(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定驳回？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 10, '订单已驳回，状态变更为已驳回');
          }
        }
      });
    },

    closeOrder(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定关闭订单，关闭后该订单冻结余额/额度将退回',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 9, '订单已关闭，状态变更为已关闭');
          }
        }
      });
    },

    startExperiment(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定上机？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 4, '订单已上机，状态变更为实验中');
          }
        }
      });
    },

    finishExperiment(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定下机？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 3, '订单已下机，状态变更为待实验');
          }
        }
      });
    },

    settleOrder(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定结算？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 5, '订单已结算，状态变更为待结算');
          }
        }
      });
    },

    refundOrder(id) {
      const userRoleType = this.$store.state.userInfo?.roleType;
      if (userRoleType !== 1 && userRoleType !== 5) {
        uni.showToast({
          title: '权限不足，仅超级管理员和财务可操作',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '确认操作',
        content: '是否确定退款，退款后订单金额将返回账户',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 11, '订单已退款，状态变更为已退款');
          }
        }
      });
    },

    trainingPass(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定培训通过？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 12, '培训通过，状态变更为完成（培训通过）');
          }
        }
      });
    },

    trainingFail(id) {
      uni.showModal({
        title: '确认操作',
        content: '是否确定培训不通过？',
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatus(id, 13, '培训不通过，状态变更为完成（培训不通过）');
          }
        }
      });
    },

    editOrderPrice(id) {
      const userRoleType = this.$store.state.userInfo?.roleType;
      if (userRoleType !== 1 && userRoleType !== 5) {
        uni.showToast({
          title: '仅超级管理员和财务可操作',
          icon: 'none'
        });
        return;
      }

      this.showEditPriceModal(id);
    },

    showEditPriceModal(id) {
      const order = this.orderList.find(item => item.id === id);
      if (!order) return;

      this.currentEditOrderId = id;
      this.currentOrderPrice = order.totalAmount || 0;
      this.newOrderPrice = '';
      this.showEditPriceModalFlag = true;
    },

    closeEditPriceModal() {
      this.showEditPriceModalFlag = false;
      this.currentEditOrderId = null;
      this.currentOrderPrice = 0;
      this.newOrderPrice = '';
    },

    onPriceInput(e) {
      this.newOrderPrice = e.detail.value;
    },

    confirmEditPrice() {
      if (!this.newOrderPrice) {
        uni.showToast({
          title: '请输入新的金额',
          icon: 'none'
        });
        return;
      }

      const newPrice = parseFloat(this.newOrderPrice);
      if (isNaN(newPrice) || newPrice < 0) {
        uni.showToast({
          title: '请输入有效金额',
          icon: 'none'
        });
        return;
      }

      const priceDiff = newPrice - this.currentOrderPrice;

      if (priceDiff > 0) {
        uni.showModal({
          title: '提示',
          content: `新增${priceDiff.toFixed(2)}元费用，当前用户账户余额/额度不足，请联系用户充值或申请额度后继续操作`,
          showCancel: false,
          confirmText: '确定'
        });
      } else {
        this.updateOrderPrice(this.currentEditOrderId, newPrice);
        this.closeEditPriceModal();
      }
    },

    updateOrderPrice(id, newPrice) {
      const index = this.orderList.findIndex(item => item.id === id);
      if (index !== -1) {
        this.orderList[index].totalAmount = newPrice;
        uni.showToast({
          title: '订单金额修改成功',
          icon: 'success'
        });
        this.$refs.pageList.refresh(true);
      }
    },

    updateOrderStatus(id, newStatus, message) {
      const index = this.orderList.findIndex(item => item.id === id);
      if (index !== -1) {
        this.orderList[index].status = newStatus;
        uni.showToast({
          title: message,
          icon: 'success'
        });
        this.$refs.pageList.refresh(true);
      }
    },

    uploadData(id) {
      uni.showToast({
        title: '上传数据功能开发中',
        icon: 'none'
      });
    },

    editOrder(id) {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },

    // ========== 辅助方法 ==========

    showMoreActions(orderId) {
      console.log('showMoreActions 被调用，订单ID:', orderId);

      const order = this.orderList.find(item => item.id === orderId);
      if (!order) {
        console.log('未找到订单:', orderId);
        return;
      }

      console.log('找到订单:', order);
      let itemList = [];

      if (order.status === 1) {
        itemList = ['驳回', '关闭订单', '修改订单金额'];
      } else if (order.status === 3) {
        itemList = [];
        if (order.type === 1) itemList.push('结算');
        itemList.push('关闭订单');
      } else if (order.status === 4) {
        itemList = [];
        if (order.type === 1) itemList.push('结算');
        itemList.push('关闭订单');
      } else if (order.status === 5) {
        itemList = ['修改订单金额'];
      } else if (order.status === 7) {
        itemList = [];
        if (order.type === 3 && order.experimentType === 1) {
          itemList = ['培训通过', '培训不通过'];
        }
      }

      if (itemList.length === 0) {
        itemList = ['关闭订单', '修改订单金额'];
      }

      console.log('显示操作选项:', itemList);

      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          console.log('用户选择了:', res.tapIndex, itemList[res.tapIndex]);
          const selectedAction = itemList[res.tapIndex];
          this.handleMoreAction(orderId, selectedAction);
        },
        fail: (err) => {
          console.log('ActionSheet 失败:', err);
        }
      });
    },

    handleMoreAction(orderId, action) {
      switch (action) {
        case '驳回':
          this.rejectOrder(orderId);
          break;
        case '关闭订单':
          this.closeOrder(orderId);
          break;
        case '结算':
          this.settleOrder(orderId);
          break;
        case '修改订单金额':
          this.editOrderPrice(orderId);
          break;
        case '培训通过':
          this.trainingPass(orderId);
          break;
        case '培训不通过':
          this.trainingFail(orderId);
          break;
        default:
          console.log('未知操作:', action);
      }
    },

    formatAppointmentTime(order) {
      if (order.timeSlots && order.timeSlots.length > 0) {
        const timeSlot = order.timeSlots[0];
        if (timeSlot.startTime && timeSlot.endTime) {
          const startTime = this.formatDateTime(timeSlot.startTime);
          const endTime = this.formatTime(timeSlot.endTime);
          return `${startTime}-${endTime}`;
        }
      }

      if (order.instrument && order.instrument.startTime && order.instrument.endTime) {
        const startTime = this.formatDateTime(order.instrument.startTime);
        const endTime = this.formatTime(order.instrument.endTime);
        return `${startTime}-${endTime}`;
      }

      return '待安排';
    },

    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    },

    getSampleStatusDisplay(order) {
      if (order.type === 1) {
        switch (order.shipStatus) {
          case 0:
            return { type: 'text', text: '自带' };
          case 1:
            return { type: 'status', text: '待邮寄', class: 'confirm' };
          case 2:
            return { type: 'status', text: '已寄出', class: 'shipped' };
          case 3:
            return { type: 'status', text: '已签收', class: 'shipped' };
          case 4:
            return { type: 'status', text: '已寄回', class: 'returned' };
          default:
            return { type: 'text', text: '自带' };
        }
      }
      return { type: 'text', text: '自带' };
    },

    shouldShowSampleDetail(order) {
      return order.type === 1 && order.status !== 1;
    },

    updatePendingCountFromList() {
      if (this.currentStatus === 'all') {
        const pendingCount = this.orderList.filter(order => order.status === 1).length;
        const pendingTabIndex = this.tabs.findIndex(tab => tab.status === 1);
        if (pendingTabIndex !== -1) {
          this.$set(this.tabs[pendingTabIndex], 'count', pendingCount);
        }
        console.log('待审核订单数量:', pendingCount);
      }
    },

    showResponsiblePersons(order) {
      console.log('显示负责人信息，订单:', order);

      if (!order.responsiblePersons || order.responsiblePersons.length === 0) {
        uni.showToast({
          title: '暂无负责人信息',
          icon: 'none'
        });
        return;
      }

      this.currentResponsiblePersons = order.responsiblePersons;
      this.showResponsibleModal = true;
    },

    closeResponsibleModal() {
      this.showResponsibleModal = false;
      this.currentResponsiblePersons = [];
    },

    getVisibleButtonCount(order) {
      let count = 0;

      if (order.status === 1) {
        count = 4;
      } else if (order.status === 2) {
        count = 3;
      } else if (order.status === 3) {
        count = 3;
        if (order.type === 1 || order.type === 2) count++;
      } else if (order.status === 4) {
        count = 3;
        if (order.type === 1 || order.type === 2) count++;
      } else if (order.status === 5) {
        count = 4;
      } else if (order.status === 7) {
        count = 4;
      } else {
        count = 1;
      }

      return count;
    }
  }
}
</script>

<style lang="scss" scoped>
.order-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  overflow: hidden;
}

.search-bar {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  flex-shrink: 0;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f0f2f5;
  border-radius: 30rpx;
  padding: 15rpx 20rpx;

  .search-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 10rpx;
  }

  input {
    flex: 1;
    font-size: 28rpx;
    height: 40rpx;
  }
}

.status-tabs-container {
  background-color: #ffffff;
  flex-shrink: 0;
}

.order-list-wrapper {
  flex: 1;
  height: 0;
  overflow: auto;
  margin-bottom: 200rpx;
}

.status-tabs {
  width: 100%;
  white-space: nowrap;
}

.tabs-wrapper {
  display: flex;
  padding: 0 12rpx;
}

.tab-item {
  flex-shrink: 0;
  min-width: 140rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border-radius: 35rpx;
  padding: 0 20rpx;
  margin-right: 16rpx;
  transition: all 0.3s;
  margin: 20rpx 0;

  &:last-child {
    margin-right: 12rpx;
  }

  text {
    font-size: 28rpx;
    color: #666;
    transition: color 0.3s;
    white-space: nowrap;
  }

  &.active {
    background-color: #40E0D0;

    text {
      font-size: 32rpx;
      color: #FFFFFF;
      font-weight: 600;
    }
  }

  .tab-badge {
    position: absolute;
    top: -5rpx;
    right: 5rpx;
    min-width: 32rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    background-color: #FF5B5B;
    color: #FFFFFF;
    border-radius: 16rpx;
    font-size: 20rpx;
  }
}

.order-list {
  padding: 28rpx 28rpx 0 28rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 37rpx 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .instrument-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #000;
  }

  .order-status {
    font-size: 24rpx;
    padding: 8rpx 12rpx;
    border-radius: 4rpx;
    font-weight: 400;

    &.status-1 {
      color: #FF7D00;
      background-color: #FFF7E8;
    }

    &.status-2 {
      color: #FF7D00;
      background-color: #FFF7E8;
    }

    &.status-3 {
      color: #FF7D00;
      background-color: #FFF7E8;
    }

    &.status-4 {
      color: #165DFF;
      background-color: #E8F3FF;
    }

    &.status-5 {
      color: #722ED1;
      background-color: #F9F0FF;
    }

    &.status-6 {
      color: #00B42A;
      background-color: #E8FFEA;
    }

    &.status-7 {
      color: #4E5969;
      background-color: #F2F3F5;
    }

    &.status-8 {
      color: #86909C;
      background-color: #F7F8FA;
    }

    &.status-9 {
      color: #86909C;
      background-color: #F7F8FA;
    }

    &.status-10 {
      color: #F53F3F;
      background-color: #FFECE8;
    }

    &.status-11 {
      color: #00B42A;
      background-color: #E8FFEA;
    }

    &.status-12 {
      color: #00B42A;
      background-color: #E8FFEA;
    }

    &.status-13 {
      color: #F53F3F;
      background-color: #FFECE8;
    }
  }
}

.order-info {
  padding: 24rpx 0;

  .info-row {
    display: flex;
    margin-bottom: 16rpx;
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      width: 160rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #6E7887;
      flex: 1;
    }

    .info-value {
      font-size: 28rpx;
      color: #000;
      font-weight: 400;
    }

    .sample-status-container {
      display: flex;
      align-items: center;
    }

    .sample-status {
      padding: 4rpx 12rpx;
      font-size: 24rpx;
      border-radius: 4rpx;
      margin-right: 10rpx;

      &.confirm {
        color: #FF7D00;
        background-color: #FFF7E8;
      }

      &.shipped {
        color: #00B42A;
        background-color: #E8FFEA;
      }

      &.returned {
        color: #00B42A;
        background-color: #E8FFEA;
      }
    }

    .sample-status-text {
      font-size: 28rpx;
      color: #000;
      font-weight: 400;
    }

    .sample-detail {
      font-size: 28rpx;
      font-weight: 400;
      color: #26D1CB;
    }
  }
}

.order-footer {
  padding: 20rpx 0 0 0;
  border-top: 1rpx solid #f0f0f0;
  width: 100%;

  .btn-group {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
    gap: 16rpx;

    &.btn-group-full {
      width: 100%;
      justify-content: space-between;
      gap: 12rpx;

      .btn {
        flex: 1;
      }
    }
  }

  .btn {
    height: 68rpx;
    line-height: 68rpx;
    font-size: 28rpx;
    border-radius: 12rpx;
    padding: 0 24rpx;
    color: #40E0D0;
    background-color: #fff;
    border: 2rpx solid #40E0D0;
    text-align: center;
    margin: 0;
    min-width: 120rpx;
  }

  .btn-more {
    color: #666;
    border: 2rpx solid #E5E6EB;
  }
}

.responsible-person-container {
  display: flex;
  align-items: center;

  .contact-phone {
    cursor: pointer;

    .more-indicator {
      color: #40E0D0;
      font-size: 24rpx;
      margin-left: 8rpx;
      text-decoration: underline;
    }
  }
}

.responsible-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .modal-content {
    width: 600rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 24rpx 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .modal-close {
      font-size: 48rpx;
      color: #999;
      cursor: pointer;
      width: 48rpx;
      height: 48rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .modal-body {
    padding: 24rpx 32rpx;
    max-height: 400rpx;
    overflow-y: auto;

    .responsible-item {
      display: flex;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .responsible-number {
        font-size: 28rpx;
        color: #40E0D0;
        font-weight: 600;
        margin-right: 16rpx;
        min-width: 40rpx;
      }

      .responsible-name {
        font-size: 30rpx;
        color: #333;
        font-weight: 500;
        margin-right: 24rpx;
        min-width: 120rpx;
      }

      .responsible-phone {
        font-size: 28rpx;
        color: #666;
        flex: 1;
      }
    }
  }

  .modal-footer {
    padding: 24rpx 32rpx 32rpx 32rpx;
    border-top: 1rpx solid #f0f0f0;

    .confirm-btn {
      width: 100%;
      height: 80rpx;
      background-color: #40E0D0;
      color: #fff;
      border: none;
      border-radius: 12rpx;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}

// 修改订单金额弹框样式
.edit-price-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .price-modal-content {
    width: 600rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }

  .price-modal-header {
    padding: 40rpx 40rpx 30rpx 40rpx;
    text-align: center;

    .price-modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .price-modal-body {
    padding: 0 40rpx 40rpx 40rpx;

    .price-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;

      .price-label {
        font-size: 30rpx;
        color: #333;
        font-weight: 400;
      }

      .price-value {
        font-size: 30rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .price-input-row {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price-label {
        font-size: 30rpx;
        color: #333;
        font-weight: 400;
        margin-right: 20rpx;
      }

      .price-input {
        flex: 1;
        height: 80rpx;
        border: 2rpx solid #E5E6EB;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 30rpx;
        color: #333;
        text-align: center;

        &::placeholder {
          color: #C9CDD4;
          font-size: 30rpx;
        }

        &:focus {
          border-color: #40E0D0;
        }
      }
    }
  }

  .price-modal-footer {
    display: flex;
    padding: 0 40rpx 40rpx 40rpx;
    gap: 20rpx;

    .price-cancel-btn {
      flex: 1;
      height: 80rpx;
      background-color: #fff;
      color: #40E0D0;
      border: 2rpx solid #40E0D0;
      border-radius: 12rpx;
      font-size: 32rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .price-confirm-btn {
      flex: 1;
      height: 80rpx;
      background-color: #40E0D0;
      color: #fff;
      border: none;
      border-radius: 12rpx;
      font-size: 32rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>