<template>
  <view class="profile-page">
    <!-- 个人信息 -->
    <view class="info-section">
      <view class="section-header">
        <view class="section-marker"></view>
        <text class="section-title">个人信息</text>
      </view>
      
      <view class="info-rows">
        <view class="info-row">
          <text class="info-label">姓名：</text>
          <text class="info-value">林晓峰</text>
        </view>
        <view class="info-row">
          <text class="info-label">身份类型：</text>
          <text class="info-value">高校 在读本科生</text>
        </view>
        <view class="info-row">
          <text class="info-label">所在地区：</text>
          <text class="info-value">广东省深圳市南山区</text>
        </view>
        <view class="info-row">
          <text class="info-label">研究方向：</text>
          <text class="info-value">—</text>
        </view>
        <view class="info-row">
          <text class="info-label">高校/研究所：</text>
          <text class="info-value">XXXX学校XXX院</text>
        </view>
      </view>
    </view>
    
    <!-- 实名认证 -->
    <view class="info-section">
      <view class="section-header">
        <view class="section-marker"></view>
        <text class="section-title">实名认证</text>
      </view>
      
      <view class="info-rows">
        <view class="info-row">
          <text class="info-label">姓名：</text>
          <text class="info-value">林晓峰</text>
        </view>
        <view class="info-row">
          <text class="info-label">身份证号：</text>
          <text class="info-value">******************</text>
        </view>
        <view class="info-row">
          <text class="info-label">证件信息：</text>
          <view class="id-card-placeholder"></view>
        </view>
      </view>
    </view>
    
    <!-- 团队信息 -->
    <view class="info-section">
      <view class="section-header">
        <view class="section-marker"></view>
        <text class="section-title">团队信息</text>
      </view>
      
      <view class="info-rows">
        <view class="info-row">
          <text class="info-label">团队：</text>
          <text class="info-value">松山湖团队A (团队管理员)</text>
        </view>
        <view class="info-row extra-margin">
          <text class="info-value team-id">XXXXX 团队</text>
        </view>
      </view>
    </view>
    
    <!-- 用户级别 -->
    <view class="info-section">
      <view class="section-header">
        <view class="section-marker"></view>
        <text class="section-title">用户级别</text>
      </view>
      
      <view class="info-rows">
        <view class="info-row">
          <text class="info-label">用户级别：</text>
          <text class="info-value">3 级用户</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: '林晓峰',
        identityType: '高校 在读本科生',
        location: '广东省深圳市南山区',
        researchDirection: '—',
        institution: 'XXXX学校XXX院',
        idNumber: '******************',
        team: '松山湖团队A',
        isTeamAdmin: true,
        teamId: 'XXXXX',
        userLevel: 3
      }
    };
  },
  onLoad() {
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '用户信息'
    });
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.info-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx 30rpx 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-marker {
  width: 6rpx;
  height: 30rpx;
  background-color: #1CABB3;
  margin-right: 16rpx;
  border-radius: 3rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1CABB3;
}

.info-rows {
  padding-left: 22rpx;
}

.info-row {
  display: flex;
  margin-bottom: 30rpx;
}

.info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #86909C;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #000000;
  flex: 1;
}

.id-card-placeholder {
  width: 300rpx;
  height: 180rpx;
  background-color: #E5E6EB;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.team-id {
  padding-left: 180rpx;
}

.extra-margin {
  margin-top: -10rpx;
}
</style> 