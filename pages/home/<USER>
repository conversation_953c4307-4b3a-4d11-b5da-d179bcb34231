<template>
  <view class="home-container">
    <!-- 顶部背景和导航 -->
    <view class="top-section">
      <!-- 轮播图 -->
      <view class="banner-container">
        <swiper class="banner-swiper" :indicator-dots="false" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(banner, index) in bannerList" :key="index" @click="handleBannerClick(banner)">
            <view class="banner-item">
              <image class="banner-image" :src="banner.imageUrl || banner.image" mode="aspectFill"></image>
              <view class="banner-overlay">
                <!-- 顶部logo区域 -->
                <view class="banner-header">
                  <view class="logo-container">
                    <image class="logo" src="/static/home/<USER>" mode="aspectFit"></image>
                  </view>
                  <view class="header-right">
                    <view class="circle-icon">
                      <image class="home-logo2" src="/static/home/<USER>" mode="aspectFit"></image>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>

        <!-- 悬浮搜索框 -->
        <view class="floating-search-box">
          <view class="search-wrapper">
            <lk-input type="text" shape="square" v-model="searchKeyword" placeholder="请输入仪器名称"
              placeholderStyle="color: rgba(255, 255, 255, 0.6);" class="search-input" prefixIcon="search"
              :prefixIconStyle="{ color: '#ffffff' }" color="#ffffff" :customStyle="{
                'border-radius': '999rpx',
                'font-size': '28rpx',
                background: 'rgba(14, 39, 39, 0.30)',
                border: '1px solid rgba(255, 255, 255, 0.32)',
                backdropFilter: 'blur(2px)',
              }" @confirm="handleSearchClick" />
            <view class="search-btn-container">
              <text class="search-btn" @click="handleSearchClick">搜索</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 通知栏 -->
      <view class="notice-section" v-if="homeNotice">
        <view class="notice-content">
          <view class="notice-icon">
            <lk-svg src="/static/svg/notice.svg" width="32rpx" height="32rpx"></lk-svg>
          </view>
          <view class="notice-text">{{ homeNotice }}</view>
          <view class="notice-close" @tap="hideNotice">
            <text class="close-icon">×</text>
          </view>
        </view>
      </view>

      <!-- 推荐仪器 -->
      <view class="section instrument-section" :class="{ 'has-notice': homeNotice, 'no-notice': !homeNotice }"
        v-if="recommendInstruments.length > 0">
        <view class="section-header">
          <text class="section-title">推荐仪器</text>
          <text class="more-text" @click="handleMoreInstrument">查看更多</text>
        </view>
        <view class="instrument-container">
          <!-- 加载状态 -->
          <view v-if="instrumentsLoading" class="instruments-loading">
            <view class="loading-text">加载中...</view>
          </view>

          <!-- 仪器列表 -->
          <scroll-view v-else scroll-x class="instrument-scroll" show-scrollbar="false" enable-flex>
            <view class="instrument-list">
              <view class="instrument-item" v-for="(item, index) in recommendInstruments" :key="index"
                @click="handleInstrumentDetail(item)">
                <view class="instrument-tag" v-if="item.tag">{{ item.tag }}</view>
                <image class="instrument-image" :src="item.image" mode="aspectFill" @error="handleImageError"></image>
                <view class="instrument-info">
                  <view class="instrument-name">{{ item.name }}</view>
                  <view class="instrument-desc">{{ item.desc }}</view>
                  <view class="instrument-actions">
                    <view class="action-btn" @click.stop="handleReserveClick(item)">立即预约</view>
                  </view>
                </view>
              </view>
              <view class="instrument-spacer"></view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 自营实验室 -->
      <view class="section lab-section">
        <view class="section-header">
          <text class="section-title">自营实验室</text>
        </view>
        <view class="lab-list">
          <view class="lab-item" v-for="(item, index) in labList" :key="index" @click="handleLabDetail(item)">
            <image class="lab-image" :src="item.image" mode="aspectFill"></image>
            <view class="lab-info">
              <view class="lab-name">{{ item.name }}</view>
              <view class="lab-address">{{ item.address }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 检测流程 -->
      <view class="section detect-section">
        <view class="section-header">
          <text class="section-title">检测流程</text>
        </view>
        <view class="detect-grid">
          <view class="detect-item" v-for="(item, index) in detectItems" :key="index" @click="handleDetectItem(item)">
            <view class="detect-icon-wrapper">
              <image class="detect-icon" :src="item.icon" mode="aspectFit"></image>
            </view>
            <text class="detect-name">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 服务案例 -->
      <view class="section case-section">
        <view class="section-header">
          <text class="section-title">服务案例</text>
        </view>
        <!-- 标签切换 -->
        <view class="case-tabs">
          <view class="case-tab" :class="{ 'case-tab-active': caseActiveTab === 0 }" @click="switchCaseTab(0)">
            <text>论文致谢</text>
          </view>
          <view class="case-tab" :class="{ 'case-tab-active': caseActiveTab === 1 }" @click="switchCaseTab(1)">
            <text>经典案例</text>
          </view>
        </view>

        <!-- 论文致谢内容 -->
        <view v-if="caseActiveTab === 0">
          <view class="case-item" v-for="(item, index) in paperCases" :key="index" @click="handleCaseDetail(item)">
            <view class="case-images">
              <image class="case-image" :src="item.image" mode="aspectFill"></image>
            </view>
            <view class="case-name">{{ item.name }}</view>
            <view class="case-title">{{ item.title }}</view>
            <view class="case-info">
              <lk-svg src="/static/svg/paper_acknowledgements2.svg" width="36rpx" height="36rpx"></lk-svg>
              <text class="case-author">{{ item.author }}</text>
              <text class="case-institution">{{ item.institution }}</text>
            </view>
            <view class="case-bottom">
              <text class="case-text">#影响因子:16.6</text>
              <view class="case-text">#纳米压痕仪（DSI）</view>
            </view>
          </view>
        </view>

        <!-- 经典案例内容 -->
        <view v-if="caseActiveTab === 1" class="case-list">
          <view class="case-item" v-for="(item, index) in classicCases" :key="index" @click="handleCaseDetail(item)">
            <view class="case-images">
              <image class="case-image" :src="item.casesImage" mode="aspectFill"></image>
            </view>
            <view class="case-title">{{ item.title }}</view>
            <view class="case-content">
              <text class="case-content-text">{{ item.content }}</text>
              <text class="case-content-text">{{ item.content2 }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 平台优势 -->
      <view class="section platform-section" v-if="caseActiveTab === 0">
        <view class="platform-title">平台优势</view>
        <view class="platform-desc">平台工程师主要有硕博学历人才构成<br>技术精湛、服务至上、精益求精</view>
        <view class="platform-stats">
          <view class="stats-row">
            <view class="stat-item left-top">
              <view class="stat-value">500+<text class="stat-unit">单位</text></view>
              <view class="stat-label">服务高校等科研单位</view>
            </view>
            <view class="stat-item right-top">
              <view class="stat-value">2<text class="stat-unit">亿+</text></view>
              <view class="stat-label">测试仪器</view>
            </view>
          </view>
          <view class="stats-row">
            <view class="stat-item left-bottom">
              <view class="stat-value">3<text class="stat-unit">天</text></view>
              <view class="stat-label">测试周期</view>
            </view>
            <view class="stat-item right-bottom">
              <view class="stat-value">100<text class="stat-unit">%</text></view>
              <view class="stat-label">满意度</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="section contact-section" v-if="caseActiveTab === 0">
        <image class="contact-image" src="/static/home/<USER>" mode="aspectFill"></image>
        <view class="contact-title">松山湖材料实验室材料制备与表征平台</view>
        <view class="contact-box">
          <view class="contact-item">
            <lk-svg src="/static/svg/home_address.svg" width="36rpx" height="36rpx" class="contact-icon"></lk-svg>
            <text class="contact-text">广东省东莞市松山湖国际创新创业社区C1栋</text>
          </view>
          <view class="contact-item">
            <lk-svg src="/static/svg/home_phone.svg" width="36rpx" height="36rpx" class="contact-icon"></lk-svg>
            <text class="contact-text contact-text-phone">
              <text>吴老师，17666247728 ，0769-89136547；</text>
              <text>徐老师，18681123110</text>
            </text>
          </view>
          <view class="contact-text contact-left">欢迎咨询、预约、合作</view>
        </view>
      </view>

      <!-- 我们的客户 -->
      <view class="section customers-section" v-if="caseActiveTab === 0">
        <view class="section-header">
          <text class="section-title">我们的客户</text>
        </view>
        <view class="customers-map-container">
          <image class="customers-map" src="/static/home/<USER>" mode="widthFix"></image>
        </view>
      </view>
    </view>
    <verification-popup :show="showVerificationPopup" @cancel="hideVerificationPopup"
      @confirm="goToVerification"></verification-popup>

    <!-- 底部选项卡 -->
    <custom-tab-bar :selected="0"></custom-tab-bar>
  </view>
</template>

<script>
import customTabBar from '@/components/custom-tab-bar/custom-tab-bar.vue';
import { InstrumentStatus } from '@/constants/instrument.js';
import verificationPopup from '../user/component/verification-popup.vue';
import aiApi from '@/api/index.js';

export default {
  components: {
    customTabBar,
    verificationPopup
  },
  data() {
    return {
      searchKeyword: '',
      recommendInstruments: [],
      instrumentsLoading: false,
      showVerificationPopup: false, // 控制实名认证提示弹框显示
      bannerList: [], // 轮播图数据
      bannerLoading: false, // 轮播图加载状态
      homeNotice: '', // 首页通知内容
      labList: [
        {
          id: 1,
          name: '磁控溅射',
          image: 'https://huayun-ai-obs-public.huayuntiantu.com/40e70f98-7d35-415c-8645-dcb1e574367e.png',
          address: '换频（上海） SQ-1907-0202Y'
        },
        {
          id: 2,
          name: '脉冲激光沉积与磁控溅射联合',
          image: 'https://huayun-ai-obs-public.huayuntiantu.com/f85932fe-1fda-4535-b08b-59ac5680cdf6.png',
          address: '北京中科信佳科技有限公司 PLD-450DB'
        }
      ],
      detectItems: [
        {
          id: 1,
          name: '登录/注册',
          icon: '/static/svg/login_register.svg'
        },
        {
          id: 2,
          name: '提交需求',
          icon: '/static/svg/submit_request.svg'
        },
        {
          id: 3,
          name: '沟通方案',
          icon: '/static/svg/communication_plan.svg'
        },
        {
          id: 4,
          name: '预约检测',
          icon: '/static/svg/appointment_testing.svg'
        },
        {
          id: 5,
          name: '出具结果',
          icon: '/static/svg/provide_results.svg'
        },
        {
          id: 6,
          name: '线上开票',
          icon: '/static/svg/online_invoicing.svg'
        }
      ],
      caseData: {
        id: 1,
        title: 'Joining of metallic glasses in liquid via ultrasonic vibrations',
        image: '/static/case/case1.png',
        author: '李博士',
        authorAvatar: '/static/user/avatar.png',
        views: '1.6万',
        institution: '清华大学'
      },
      caseActiveTab: 0,
      paperCases: [
        {
          id: 1,
          name: 'Nature communications',
          title: 'Joining of metallic glasses in liquid via ultrasonic vibrations',
          image: '/static/home/<USER>',
          author: '李路遥',
          authorAvatar: '/static/user/avatar.png',
          views: '1.6万',
          institution: '深圳大学',
          date: '2023-09-15',
          action: '查看详情'
        },
      ],
      classicCases: [
        {
          id: 1,
          title: '纳米压痕技术在生物样品（亚微米级位置）的硬度测试及材料微柱压缩测试中的应用',
          content: '#纳米压痕仪(DSI)',
          casesImage: '/static/home/<USER>',
          content2: '#压痕测试和微柱压缩测试',
        },
        {
          id: 2,
          title: '角分辨偏振拉曼光谱（ARPR）在MoTe2各向异性分析中的应用',
          content: '#快速显微共聚焦拉曼成像系统',
          casesImage: '/static/home/<USER>',
          content2: '#偏振分辨的光谱学...',
        },
        {
          id: 3,
          title: '白光干涉仪在测量光滑样品表面划痕及孔洞体积的典型应用',
          content: '#白光干涉仪（WLI）',
          casesImage: '/static/home/<USER>',
          content2: '#轮廓测试',
        },
        {
          id: 4,
          title: '无液氦综合物性测量系统（PPMS）三个应用案例',
          content: '#无液氦综合物性测量系统（PPMS）',
          casesImage: '/static/home/<USER>',
          content2: '#电运输测试',
        },
        {
          id: 5,
          title: '磁光克尔显微镜（MOKE）在楔形Pt/Co/Ta垂直磁化多层膜结构研究中的应用',
          content: '#无液氦综合物性测量系统（PPMS）',
          casesImage: '/static/home/<USER>',
          content2: '#电运输测试',
        },
        {
          id: 6,
          title: '#聚焦离子束-扫描电镜...',
          content: '#聚焦离子束-扫描电镜...',
          casesImage: '/static/home/<USER>',
          content2: '#常规分析型...',
          content3: '#两相...',
        },

        {
          id: 7,
          title: '场发射扫描电镜在金属组织内位错的形貌观察及位错密度定性分析中的应用（',
          content: '#场发射扫描电镜(FESEM)',
          casesImage: '/static/home/<USER>',
          content2: '#扫描电镜-ECCI-位...',
        },
        {
          id: 8,
          title: 'XPS在细金属丝表面氧化状态分析中的应用',
          content: '#多功能X射线光电子能谱表面分析...',
          casesImage: '/static/home/<USER>',
          content2: '#薄膜厚度测试',
        },
        {
          id: 9,
          title: '拉曼在SiC外延层形貌缺陷表征中的应用',
          content: '#快速显微共聚焦拉曼成像系统',
          casesImage: '/static/home/<USER>',
          content2: '#非破坏性、微区...',
        },
      ]
    }
  },
  onLoad() {
    // 页面加载时获取推荐仪器和轮播图
    this.fetchRecommendInstruments();
    this.fetchBannerList();
    this.fetchSystemSettings();
  },

  onShow() {
    // 页面显示时可以选择性刷新数据
    // this.fetchRecommendInstruments();
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.fetchRecommendInstruments().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    hideVerificationPopup() {
      this.showVerificationPopup = false;
    },
    goToVerification() {
      this.hideVerificationPopup();
      uni.navigateTo({
        url: '/pages/user/verification'
      });
    },
    // 获取推荐仪器
    async fetchRecommendInstruments() {
      try {
        this.instrumentsLoading = true;

        // 调用分页接口，获取前3个仪器
        const data = await aiApi.getInstrumentList();

        if (data && data.length > 0) {
          // 转换数据格式
          this.recommendInstruments = data.map(item => ({
            id: item.instrumentId,
            name: item.instrumentName,
            desc: item.instrumentDescription,
            image: item.fileUrl,
            tag: '限时优惠',
            // 保留原始数据
            originalData: item
          }));
        }
      } catch (error) {

      } finally {
        this.instrumentsLoading = false;
      }
    },

    handleSearchClick() {
      // 跳转到搜索页面，携带搜索关键词
      if (this.searchKeyword.trim()) {
        // 如果用户输入了搜索内容，直接跳转到搜索结果页
        uni.navigateTo({
          url: `/pages/search/index?searchKey=${encodeURIComponent(this.searchKeyword.trim())}`
        });
      } else {
        // 如果没有输入内容，跳转到搜索页面
        uni.navigateTo({
          url: '/pages/search/index'
        });
      }
    },
    handleMoreInstrument() {
      // 强制更新选中状态并跳转
      this.$nextTick(() => {
        uni.switchTab({
          url: '/pages/instrument/instrument'
        });
      });
    },
    handleInstrumentDetail(item) {
      // 跳转到仪器详情页
      uni.navigateTo({
        url: `/pages/instrument/detail?id=${item.id}`
      });
    },

    // 处理立即预约点击
    handleReserveClick(item) {
      // if(this.$store.state.userInfo.identityAuthenticationId == 0){
      //    this.showVerificationPopup = true;
      //   return;
      // }
      // 跳转到预约页面
      uni.navigateTo({
        url: `/pages/instrument/reserve?instrumentId=${item.id}&instrumentName=${encodeURIComponent(item.name)}`
      });
    },

    // 处理图片加载错误
    handleImageError(e) {
      // 可以设置默认图片
    },
    handleLabDetail(item) {
      // 跳转到实验室详情页
      uni.navigateTo({
        url: `/pages/lab/detail?id=${item.id}`
      });
    },
    handleDetectItem(item) {
      // 处理检测项目点击
      uni.navigateTo({
        url: `/pages/detect/index?id=${item.id}&name=${item.name}`
      });
    },
    handleMoreCases() {
      // 跳转到更多案例页面
      uni.navigateTo({
        url: '/pages/case/list'
      });
    },
    handleCaseDetail(item) {
      // 跳转到案例详情页
      uni.navigateTo({
        url: `/pages/case/detail?id=${item.id}`
      });
    },
    switchCaseTab(tabIndex) {
      this.caseActiveTab = tabIndex;
    },

    // 获取轮播图数据
    async fetchBannerList() {
      try {
        this.bannerLoading = true;
        console.log('开始获取轮播图数据...');

        const data = await aiApi.getBannerList();
        console.log('轮播图数据:', data);

        if (data && Array.isArray(data) && data.length > 0) {
          this.bannerList = data.map(item => ({
            id: item.id,
            title: item.title || '不止于数据，深究于机理',
            subtitle: item.subtitle || item.description || '科研级检测，助力材料创新',
            imageUrl: item.imageUrl || item.image || item.picUrl,
            linkUrl: item.linkUrl || item.url,
            // 保留原始数据
            originalData: item
          }));
        } else {
          // 如果没有数据，bannerList保持为空数组，会显示默认内容
          this.bannerList = [];
        }
      } catch (error) {
        console.error('获取轮播图失败:', error);
        // 出错时使用空数组，显示默认内容
        this.bannerList = [];
      } finally {
        this.bannerLoading = false;
      }
    },

    // 处理轮播图点击
    handleBannerClick(banner) {

      if (banner.dumpUrl) {
        uni.navigateTo({
          url: `${banner.dumpUrl}`
        });
      } else {
        // 没有链接地址，可以显示提示或者跳转到默认页面
        uni.showToast({
          title: banner.title || '轮播图',
          icon: 'none'
        });
      }
    },

    // 获取系统设置
    async fetchSystemSettings() {
      try {
        console.log('开始获取系统设置...');
        const result = await aiApi.fetchAndStoreSystemSettings();
        console.log('系统设置结果:', result);
        if (result && result.homeNotice) {
          this.homeNotice = result.homeNotice;
          console.log('设置homeNotice:', this.homeNotice);
        }
      } catch (error) {
        console.error('获取系统设置失败:', error);
      }
    },

    // 隐藏通知
    hideNotice() {
      this.homeNotice = '';
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  overflow-y: auto;
  background-color: #f5f8fb;
}

.top-section {
  display: flex;
  flex-direction: column;
}

/* 轮播图样式 */
.banner-container {
  width: 100%;
  height: 600rpx;
  position: relative;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-item {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 轮播图覆盖层 */
.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20rpx 30rpx 60rpx;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
}

/* 轮播图顶部header */
.banner-header {
  display: flex;
  margin-top: 44rpx;
  z-index: 2;
}

.banner-header .logo-container {
  display: flex;
  align-items: center;
  margin-right: 10rpx;
}

.banner-header .logo {
  width: 92rpx;
  height: 72rpx;
}

.banner-header .header-right {
  display: flex;
  align-items: center;
}

.banner-header .circle-icon {
  display: flex;
  align-items: center;
}

.banner-header .home-logo2 {
  width: 134rpx;
  height: 72rpx;
}

/* 轮播图内容区域 */
.banner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  z-index: 2;
}

.banner-title {
  font-size: 48rpx;
  font-weight: 900;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 28rpx;
  font-weight: 400;
  opacity: 0.9;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 悬浮搜索框 */
.floating-search-box {
  position: absolute;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 10;
}

.floating-search-box .search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.floating-search-box .search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.floating-search-box .search-btn-container {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 10rpx;
  z-index: 1;
}

.floating-search-box .search-btn {
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 100%;
}

/* 默认轮播图样式 */
.default-banner {
  background: linear-gradient(135deg, #40E0D0 0%, #20B2AA 100%);
}

.default-banner .header {
  width: 100%;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.default-banner .logo-container {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.default-banner .logo-left {
  display: flex;
  align-items: center;
}

.default-banner .logo {
  width: 92rpx;
  height: 72rpx;
  margin-right: 10rpx;
}

.default-banner .header-right {
  display: flex;
  align-items: center;
}

.default-banner .home-logo2 {
  width: 134rpx;
  height: 72rpx;
}

.default-banner .title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}

.default-banner .title {
  color: #fff;
  font-size: 48rpx;
  font-weight: 900;
  margin-top: 60rpx;
}

.default-banner .subtitle {
  font-size: 28rpx;
  margin-top: 10rpx;
  font-weight: 400;
  color: #fff;
}

.header {
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-left {
  display: flex;
  align-items: center;
}

.logo {
  width: 92rpx;
  height: 72rpx;
  margin-right: 10rpx;
}


.header-right {
  display: flex;
  align-items: center;
}

.dots {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.home-logo2 {
  width: 134rpx;
  height: 72rpx;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}

.title {
  color: #fff;
  font-size: 48rpx;
  font-weight: 900;
  margin-top: 60rpx;
}

.subtitle {
  font-size: 28rpx;
  margin-top: 10rpx;
  font-weight: 400;
  color: #fff;
}

.search-box {
  margin: 80rpx 30rpx 10rpx;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  margin-left: 10rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.search-btn-container {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 10rpx;
  z-index: 1;
}

.search-btn {
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 100%;
}

.content-area {
  flex: 1;
  box-sizing: border-box;
  padding-bottom: 120rpx;
}

.section {
  margin: 20rpx 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

.section-title {
  font-size: 36rpx;
  color: #000;
  font-weight: 600;
}

.more-text {
  font-size: 24rpx;
  color: #999;
}

/* 推荐仪器 */
.instrument-section {
  padding: 28rpx 0 0rpx 0;
  background-color: #F5F7FA;
  box-shadow: none;
  margin: 0;
  border-radius: 0;
}

/* 推荐仪器区域动态上边距 */
.instrument-section.has-notice {
  padding-top: 28rpx;
}

.instrument-section.no-notice {
  padding-top: 48rpx;
}

.instrument-section .section-header {
  padding: 0 30rpx;
  margin-bottom: 24rpx;
}

.instrument-section .section-title {
  font-size: 34rpx;
  font-weight: 600;
}

.instrument-section .more-text {
  color: #86909C;
  font-size: 26rpx;
}

.instrument-container {
  position: relative;
  width: 100%;
}

/* 推荐仪器加载状态 */
.instruments-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 280rpx;
  padding: 0 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.instrument-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: scroll;
}

.instrument-scroll::-webkit-scrollbar {
  display: none;
}

.instrument-list {
  display: flex;
  flex-direction: row;
  padding: 0 0 20rpx 30rpx;
}

.instrument-item:nth-child(3n) {
  margin-right: 0;
}

.instrument-item {
  width: 280rpx;
  margin-right: 20rpx;
  position: relative;
  display: inline-block;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #FFFFFF;
  flex-shrink: 0;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.instrument-spacer {
  width: 30rpx;
  flex-shrink: 0;
}

.instrument-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background-color: #FF6B6B;
  color: #fff;
  font-size: 18rpx;
  padding: 4rpx 12rpx;
  border-radius: 200rpx;
  z-index: 1;
  font-weight: 400;
}

.instrument-image {
  width: 100%;
  height: 180rpx;
}

.instrument-info {
  padding: 16rpx;
}

.instrument-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #101217;
  white-space: normal;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.instrument-desc {
  font-size: 24rpx;
  color: #4E5969;
  white-space: normal;
  margin-bottom: 16rpx;
}

.instrument-actions {
  margin-top: 10rpx;
}

.action-btn {
  display: inline-block;
  font-size: 28rpx;
  color: #FFFFFF;
  background-color: #40E0D0;
  padding: 12rpx 0;
  border-radius: 40rpx;
  text-align: center;
  width: 100%;
  font-weight: 500;
}

/* 自营实验室 */
.lab-section {
  padding: 28rpx 0 0 0;
  background-color: #F5F7FA;
  box-shadow: none;
  margin: 0;
  border-radius: 0;
}

.lab-list {
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;
  padding: 0 30rpx;
  margin-bottom: 48rpx;
}

.lab-item {
  display: flex;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 28rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.lab-item:last-child {
  margin-bottom: 0;
}

.lab-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12rpx;
}

.lab-info {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.lab-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #101217;
  margin-bottom: 28rpx;
}

.lab-address {
  font-size: 28rpx;
  color: #4E5969;
  font-weight: 400;
  line-height: 1.4;
}

/* 检测项目 */
.detect-section {
  padding: 30rpx 20rpx;
  margin: 0;
  background-color: #fff;
}

.detect-grid {
  display: flex;
  flex-wrap: wrap;
}

.detect-item {
  width: 33.3333%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 0;
}

.detect-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  background-color: #E8F4FF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.detect-icon {
  width: 50rpx;
  height: 50rpx;
}

.detect-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-top: 8rpx;
  font-weight: 400;
}

/* 服务案例 */
.case-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.case-name {
  font-size: 28rpx;
  font-weight: 400;
  color: #86909C;
}

.case-title {
  color: #101217;
  font-size: 32rpx;
  font-weight: 500;
  margin: 20rpx 0 30rpx 0;
  line-height: 1.4;
}

.case-info {
  display: flex;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #E5E5E5;
}

.case-author-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}

.case-author {
  font-size: 28rpx;
  font-weight: 400;
  color: #86909C;
  margin-left: 10rpx;
}

.case-institution {
  font-size: 28rpx;
  font-weight: 400;
  color: #86909C;
  margin-left: 10rpx;
}

.case-views {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 平台优势 */
.platform-section {
  padding: 50rpx 30rpx 70rpx;
  background-color: #fff;
  border-radius: 20rpx;
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/1b14e3bc-db8f-47ad-9838-5843613d9799.png');
  background-size: 99% 99%;
  background-repeat: no-repeat;
  margin: 48rpx 0 0 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.platform-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000;
  text-align: center;
  margin-bottom: 18rpx;
}

.platform-desc {
  font-size: 24rpx;
  color: #4E5969;
  text-align: center;
  margin-bottom: 52rpx;
  line-height: 1.5;
}

.platform-stats {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
  height: 400rpx;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 50%;
  position: relative;
  margin-bottom: 20rpx;
}

.left-top,
.left-bottom {
  margin-right: 20rpx;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stat-item {
  text-align: center;
  width: 50%;
  height: 100%;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 16rpx;
  border: 6rpx solid #FFF;
  background: rgba(255, 255, 255, 0.40);
  backdrop-filter: blur(2px);
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: rgba(38, 209, 203, 1);
  line-height: 1;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.stat-unit {
  font-size: 24rpx;
  font-weight: 400;
  margin-top: 16rpx;
  margin-left: 4rpx;
  color: rgba(16, 18, 23, 1);
}

.stat-label {
  font-size: 24rpx;
  font-weight: 400;
  color: rgba(16, 18, 23, 1);
}

/* 底部信息 */
.footer-info {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.location-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.location-address,
.contact-info,
.service-tags {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 客户地图 */
.customers-section {
  margin: 48rpx 30rpx 140rpx 30rpx;
}

.customers-section .section-header {
  margin-bottom: 32rpx;
  display: flex;
  justify-content: center;
}

.customers-map-container {
  padding: 44rpx 0;
  border-radius: 16rpx;
  background-color: #FFFFFF;
}

.customers-map {
  width: 100%;
  height: 478rpx;
}

/* 服务案例标签切换 */
.case-tabs {
  display: flex;
  margin-bottom: 20rpx;

}

.case-tab {
  padding: 12rpx 30rpx;
  font-size: 32rpx;
  color: #4E5969;
  position: relative;
}

.case-tab-active {
  color: #101217;
  font-weight: 600;
}

.case-tab-active::after {
  content: '';
  position: absolute;
  bottom: 18rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 7px;
  background-color: #1CABB3;
  mix-blend-mode: darken;
}

.case-list {
  margin-bottom: 144rpx;
}

/* 案例项 */
.case-item {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.case-item:last-child {
  border-bottom: none;
}

.case-images {
  display: flex;
  border-radius: 8rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  height: 220rpx;
  border-radius: 8rpx;
}

.case-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.case-content {
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid #E5E5E5;

  .case-content-text {
    font-size: 24rpx;
    color: #26D1CB;
    font-weight: 500;
    background-color: #F7F8FA;
    border-radius: 10rpx;
    padding: 4rpx 12rpx;
    margin-right: 16rpx;
  }
}

.case-info {
  display: flex;
  align-items: center;

}

.case-author-avatar {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
}

.case-author {
  font-size: 24rpx;
  margin-left: 10rpx;
  color: #666;
}

.case-institution {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.case-views {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 服务案例日期和操作 */
.case-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;

  .case-text {
    font-size: 24rpx;
    color: #26D1CB;
    font-weight: 500;
    background-color: #F7F8FA;
    border-radius: 10rpx;
    padding: 4rpx 12rpx;
    margin-right: 16rpx;
  }
}

.case-date {
  font-size: 22rpx;
  color: #999;
}

.case-action {
  font-size: 22rpx;
  color: #3498db;
}

.lab-section .section-header {
  padding: 0 30rpx;
}

/* 联系方式 */
.contact-section {
  margin: 48rpx 30rpx 0 30rpx;
  padding: 40rpx 20rpx;
  border-radius: 20rpx;
  background-color: #fff;
}

.contact-image {
  width: 100%;
  height: 360rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
}

.contact-title {
  font-size: 32rpx;
  color: #101217;
  font-weight: 600;
  margin-bottom: 24rpx;
}

.contact-box {
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.contact-icon {
  flex-shrink: 0;
}

.contact-text {
  font-size: 28rpx;
  color: #4E5969;
  font-weight: 400;
  line-height: 1.5;
}

.contact-left {
  margin-left: 12rpx;
}

/* 通知栏样式 */
.notice-section {
  margin: 28rpx 28rpx 0;
  padding: 0;
}

.notice-content {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  position: relative;
  background: linear-gradient(180deg, #FFF8EE -65.38%, #FFF 100%);
  border-radius: 6px;
  border: 1px solid #FFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.notice-icon {
  display: none;
}

.notice-text {
  flex: 1;
  font-size: 28rpx;
  color: #000;
  line-height: 1.4;
  font-weight: 400;
}

.notice-close {
  margin-left: 20rpx;
  flex-shrink: 0;
  padding: 0;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 50rpx;
  color: #000;
  font-weight: 300;
  line-height: 1;
  padding-bottom: 10rpx;
}
</style>