<template>
  <view class="us">
    <view class="us-logo">
      <image src="@/static/logo.png" />
    </view>
    <view class="us-title"> 松山湖材料实验室检测中心 </view>
    <view class="us-txt">松山湖材料实验室检测中心是一个致力于通过人工智能技术赋能教育的创新平台。专注于将最前沿的AI技术与教育相结合，提供个性化的教育解决方案。</view>

  </view>
</template>
<script>
  export default ({
    data() {
      return {
        version: "",
      }
    },
    onLoad() {
      this.getVersion()
    },
    methods: {
      async getVersion() {
        // #ifdef MP-WEIXIN
        this.version = uni.getAccountInfoSync().miniProgram.version
        // #endif

        // #ifdef APP-PLUS
        plus.runtime.getProperty(plus.runtime.appid, wgtinfo  => {
          this.version = wgtinfo.version
        })
        // #endif
        
        // #ifdef H5
        this.version = uni.getSystemInfoSync().appVersion
        // #endif
      },

    }
  })
</script>

<style lang="scss" scoped>
  .us {
    &-logo {
      margin-left: 294rpx;
      margin-top: 160rpx;
      width: 138rpx;
      height: 231rpx;
      // background: linear-gradient(180deg, #619dff 0%, #3d7fff 100%);
      border-radius: 28rpx;

      image {
      width: 138rpx;
      height: 231rpx;
        border-radius: 28rpx;
      }
    }

    &-title {
      padding-top: 40rpx;
      text-align: center;
      font-size: 48rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }

    &-txt {
      padding-top: 20rpx;
	  margin:0rpx 100rpx;
      text-align: center;
      font-size: 32rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #777777;
    }
  }
</style>
