<template>
  <view class="order-detail-container">
    <!-- Tab切换栏 -->
    <view class="tab-section">
      <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: currentTab === index }"
        @tap="switchTab(index)">
        <text>{{ tab.name }}</text>
      </view>
    </view>

    <!-- 内容区 -->
    <swiper class="content-swiper" :current="currentTab" @change="swiperChange"
      :style="{ height: contentHeight + 'px' }">
      <!-- 预约信息 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-content">
          <view class="info-section">
            <view class="info-row">
              <text class="info-label">订单编号：</text>
              <text class="info-value">{{ orderData.orderNo }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">实验名称：</text>
              <text class="info-value">{{ orderData.title }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">订单类型：</text>
              <text class="info-value">{{ orderData.orderType }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">实验类型：</text>
              <text class="info-value">{{ orderData.experimentType }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">样品信息：</text>
              <view class="sample-status-container">
                <text v-if="orderData.sampleStatus === '自带'" class="sample-status-text">{{ orderData.sampleStatus
                }}</text>
                <text v-if="orderData.sampleStatus === '待邮寄'" class="sample-status confirm">{{ orderData.sampleStatus
                }}</text>
                <text v-if="orderData.sampleStatus === '已寄出'" class="sample-status shipped">{{ orderData.sampleStatus
                }}</text>
                <text v-if="orderData.sampleStatus === '已寄回'" class="sample-status returned">{{ orderData.sampleStatus
                }}</text>
                <text v-if="orderData.sampleStatus !== '自带'" class="sample-detail" @tap="switchToSampleTab">详情</text>
              </view>
            </view>
            <view class="info-row">
              <text class="info-label">预约时间：</text>
              <text class="info-value">{{ orderData.appointmentTime }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">创建时间：</text>
              <text class="info-value">{{ orderData.createTime }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">备注：</text>
              <text class="info-value">{{ orderData.remark || "这是一段备注" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">耗材：</text>
              <view class="info-value">
                <view v-if="orderData.consumables && orderData.consumables.length > 0" class="expandable-list">
                  <!-- 显示的内容 -->
                  <view class="list-content">
                    <view v-for="(item, index) in getDisplayConsumables()" :key="index" class="list-item-row">
                      <text class="item-text">{{ item.name }}{{ item.num > 1 ? ' *' + item.num : '' }} ¥{{ item.price
                        }}</text>
                    </view>
                  </view>
                  <!-- 展开/收起按钮 -->
                  <view v-if="orderData.consumables.length > 1" class="expand-btn" @tap="toggleConsumables">
                    <text class="expand-text">{{ consumablesExpanded ? '收起' : '展开' }}</text>
                    <text class="expand-icon">{{ consumablesExpanded ? '▲' : '▼' }}</text>
                  </view>
                </view>
                <text v-else>无</text>
              </view>
            </view>
            <view class="info-row">
              <text class="info-label">增值服务：</text>
              <view class="info-value">
                <view v-if="orderData.incrementServices && orderData.incrementServices.length > 0"
                  class="expandable-list">
                  <!-- 显示的内容 -->
                  <view class="list-content">
                    <view v-for="(item, index) in getDisplayServices()" :key="index" class="list-item-row">
                      <text class="item-text">{{ item.name }}{{ item.num > 1 ? ' *' + item.num : '' }} ¥{{ item.price
                        }}</text>
                    </view>
                  </view>
                  <!-- 展开/收起按钮 -->
                  <view v-if="orderData.incrementServices.length > 1" class="expand-btn" @tap="toggleServices">
                    <text class="expand-text">{{ servicesExpanded ? '收起' : '展开' }}</text>
                    <text class="expand-icon">{{ servicesExpanded ? '▲' : '▼' }}</text>
                  </view>
                </view>
                <text v-else>无</text>
              </view>
            </view>
            <view class="info-row">
              <text class="info-label">仪器收费方式：</text>
              <text class="info-value">{{ orderData.chargeMethod || "按样品 1000元/样品" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">预估金额：</text>
              <text class="info-value">¥ {{ orderData.totalAmount || "0" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">折后金额：</text>
              <text class="info-value">¥ {{ orderData.amount || "0" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">支付方式：</text>
              <text class="info-value">{{ orderData.payTypeText || "" }}</text>
            </view>
          </view>
        </scroll-view>
      </swiper-item>

      <!-- 样品邮寄 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-content sample-delivery-content">
          <!-- 实验室收件地址 -->
          <view class="address-card">
            <view class="card-header">
              <view class="circle-number blue">
                <image src="/static/svg/sample_return.svg" class="header-icon"></image>
              </view>
              <text class="header-title">实验室收件地址</text>
            </view>
            <view class="card-body">
              <view class="contact-info">
                <text class="contact-name">李老师</text>
                <text class="contact-phone">15512344562</text>
              </view>
              <view class="address-text">
                此处填写详细地址信息此处填写详细地址信息此处填写详细地址信息此处填写详细地址信息
              </view>
            </view>
          </view>

          <!-- 样品寄回地址 -->
          <view class="address-card" v-if="needReturn">
            <view class="card-header">
              <view class="circle-number green">
                <image src="/static/svg/laboratory_address.svg" class="header-icon"></image>
              </view>
              <text class="header-title">样品寄回地址</text>
            </view>
            <view class="card-body">
              <view class="contact-info">
                <text class="contact-name">李老师</text>
                <text class="contact-phone">15512344562</text>
              </view>
              <view class="address-text">
                此处填写详细地址信息此处填写详细地址信息此处填写详细地址信息此处填写详细地址信息
              </view>
              <view class="address-note">
                可在个人中心-用户信息-收货地址中修改
              </view>
            </view>
            <view class="edit-icon">
              <image src="/static/chat/edit.svg" mode="aspectFit"></image>
            </view>
          </view>

          <!-- 样品寄出 -->
          <view class="logistics-section">
            <view class="section-title">样品寄出</view>
            <view class="form-item">
              <view class="form-row">
                <text class="form-label">快递 <text class="required">*</text></text>
                <view class="form-right">
                  <text class="placeholder">请选择</text>
                  <image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
                </view>
              </view>
            </view>
            <view class="form-item">
              <view class="form-row">
                <text class="form-label">快递编号 <text class="required">*</text></text>
                <view class="form-right">
                  <input type="text" placeholder="请输入" />
                </view>
              </view>
            </view>
            <view class="form-action">
              <button class="action-button">点击查看物流详情</button>
            </view>
          </view>

          <!-- 样品寄回 -->
          <view class="logistics-section" v-if="needReturn">
            <view class="section-title">样品寄回</view>
            <view class="form-item">
              <view class="form-row">
                <text class="form-label">快递 <text class="required">*</text></text>
                <view class="form-right">
                  <text class="placeholder">请选择</text>
                  <image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
                </view>
              </view>
            </view>
            <view class="form-item">
              <view class="form-row">
                <text class="form-label">快递编号 <text class="required">*</text></text>
                <view class="form-right">
                  <input type="text" placeholder="请输入" />
                </view>
              </view>
            </view>
            <view class="form-action">
              <button class="action-button">点击查看物流详情</button>
            </view>
          </view>

          <!-- 底部留白 -->
          <view class="bottom-space"></view>
        </scroll-view>
      </swiper-item>

      <!-- 上下机时间 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-content sample-delivery-content">
          <!-- 已选时间段 -->
          <view class="time-section">
            <view class="section-title">已选时间</view>
            <view class="time-list">
              <view class="time-item" v-for="(time, index) in selectedTimes" :key="index">
                <text>{{ time.date }} {{ time.timeRange }}</text>
              </view>
            </view>
          </view>

          <!-- 实验列表 -->
          <view class="experiment-list">
            <view class="experiment-section" v-for="(experiment, index) in experiments" :key="index">
              <view class="section-title">第{{ index + 1 }}次实验</view>
              <view class="exp-info-row-container">
                <view class="exp-info-row">
                  <text class="exp-label">上机时间：</text>
                  <text class="exp-value">{{ experiment.startTime }}</text>
                </view>
                <view class="exp-info-row">
                  <text class="exp-label">下机时间：</text>
                  <text class="exp-value">{{ experiment.endTime }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部留白 -->
          <view class="bottom-space"></view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import orderApi from '@/api/order';

export default {
  data() {
    return {
      orderId: '',
      currentTab: 0,
      contentHeight: 0,
      tabs: [
        { name: '预约信息' },
        { name: '样品邮寄' },
        { name: '上下机时间' }
      ],
      orderData: {
        id: '',
        orderNo: '000001',
        title: '这是一个实验名称',
        orderType: '培训',
        status: 'pending',
        createTime: '2025-03-28 10: 30-11: 30',
        appointmentTime: '2025-03-28 10: 30-11: 30',
        experimentType: '培训',
        sampleStatus: '', // 初始为空，等待loadOrderDetail设置
        remark: '这是一段备注',
        materials: '耗材 500元/次',
        additionalService: '耗材 500元/次',
        chargeMethod: '按样品 1000元/样品',
        estimatedPrice: '2000.00',
        discountPrice: '1400.00',
        paymentMethod: '松山湖回款额度',
        // 测试数据：耗材数组
        consumables: [
          { name: '试剂A', num: 2, price: 500 },
          { name: '试剂B', num: 1, price: 300 },
          { name: '试剂C', num: 3, price: 800 },
          { name: '试剂D', num: 1, price: 200 }
        ],
        // 测试数据：增值服务数组
        incrementServices: [
          { name: '数据分析服务', num: 1, price: 1000 },
          { name: '报告编写服务', num: 2, price: 600 },
          { name: '技术咨询服务', num: 1, price: 400 }
        ]
      },
      // 展开/收起状态
      consumablesExpanded: false, // 耗材是否展开
      servicesExpanded: false, // 增值服务是否展开
      needReturn: true, // 是否需要寄回样品
      selectedTimes: [
        { date: '2025-03-28', timeRange: '9:00-11:00' },
        { date: '2025-03-28', timeRange: '9:00-11:00' },
        { date: '2025-03-28', timeRange: '9:00-11:00' }
      ],
      experiments: [
        {
          startTime: '2025-03-28 10:30',
          endTime: '这是一个实验名称'
        },
        {
          startTime: '2025-03-28 10:30',
          endTime: '这是一个实验名称'
        }
      ]
    }
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      this.loadOrderDetail();
    }
    if (options.tab) {
      this.currentTab = parseInt(options.tab) || 0;
    }
    // 获取窗口高度
    const systemInfo = uni.getSystemInfoSync();
    // 减去顶部导航栏和tab栏的高度
    this.contentHeight = systemInfo.windowHeight - 94; // 只考虑tab栏的高度
  },
  methods: {
    // 加载订单详情
    async loadOrderDetail() {
      if (!this.orderId) {
        console.error('订单ID不存在');
        return;
      }

      try {
        uni.showLoading({
          title: '加载中...'
        });

        // 调用API获取订单详情
        const orderDetail = await orderApi.getOrderDetail({ id: this.orderId });
        console.log('订单详情数据:', orderDetail);

        // 更新订单数据
        this.orderData = {
          ...this.orderData,
          ...orderDetail,
          // 根据后端返回的字段映射到前端显示
          title: orderDetail.experimentName || this.orderData.title,
          orderType: this.getOrderTypeText(orderDetail.type),
          experimentType: this.getExperimentTypeText(orderDetail.experimentType),
          status: orderDetail.status,
          sampleStatus: this.getSampleStatusText(orderDetail.shipStatus),
          estimatedPrice: (orderDetail.totalAmount || 0).toFixed(2),
          discountPrice: (orderDetail.actualAmount || orderDetail.totalAmount || 0).toFixed(2)
        };
     
        // 根据样品状态设置是否需要寄回
        this.needReturn = orderDetail.sampleReturnable === 1;

        console.log('加载订单详情成功', this.orderId, this.orderData);

      } catch (error) {
        console.error('加载订单详情失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 获取订单类型文本
    getOrderTypeText(type) {
      const typeMap = {
        1: '送样申请',
        2: '仪器预约-工程师操作',
        3: '仪器预约-自主操作',
        4: '需求订单'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取实验类型文本
    getExperimentTypeText(type) {
      const typeMap = {
        1: '培训',
        2: '试测',
        3: '正式'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取样品状态文本
    getSampleStatusText(shipStatus) {
      const statusMap = {
        0: '自带',
        1: '待邮寄',
        2: '已寄出',
        3: '已签收',
        4: '已寄回'
      };
      return statusMap[shipStatus] || '自带';
    },

    // 切换Tab
    switchTab(index) {
      this.currentTab = index;
    },

    // 滑动切换
    swiperChange(e) {
      this.currentTab = e.detail.current;
    },

    // 从样品信息点击详情切换到样品邮寄tab
    switchToSampleTab() {
      this.currentTab = 1;
    },

    // 获取要显示的耗材列表
    getDisplayConsumables() {
      if (!this.orderData.consumables || this.orderData.consumables.length === 0) {
        return [];
      }
      // 如果展开或者只有1条数据，显示全部
      if (this.consumablesExpanded || this.orderData.consumables.length <= 1) {
        return this.orderData.consumables;
      }
      // 否则只显示第一条
      return [this.orderData.consumables[0]];
    },

    // 获取要显示的增值服务列表
    getDisplayServices() {
      if (!this.orderData.incrementServices || this.orderData.incrementServices.length === 0) {
        return [];
      }
      // 如果展开或者只有1条数据，显示全部
      if (this.servicesExpanded || this.orderData.incrementServices.length <= 1) {
        return this.orderData.incrementServices;
      }
      // 否则只显示第一条
      return [this.orderData.incrementServices[0]];
    },

    // 切换耗材展开/收起状态
    toggleConsumables() {
      this.consumablesExpanded = !this.consumablesExpanded;
    },

    // 切换增值服务展开/收起状态
    toggleServices() {
      this.servicesExpanded = !this.servicesExpanded;
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
}

.tab-section {
  display: flex;
  padding: 12rpx 12rpx 24rpx 12rpx;
  border-bottom: 1rpx solid #E5E5E5;

  .tab-item {
    flex: 1;
    height: 70rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    border-radius: 35rpx;
    padding: 0 10rpx;
    transition: all 0.3s;
    margin: 0 10rpx;

    text {
      font-size: 28rpx;
      color: #666;
      transition: color 0.3s;
    }

    &.active {
      background-color: #40E0D0;

      text {
        font-size: 32rpx;
        color: #FFFFFF;
        font-weight: 600;
      }
    }
  }
}

.content-swiper {
  flex: 1;
}

.tab-content {
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;

  &.sample-delivery-content {
    background-color: #F5F7FA;
  }
}

.info-section {
  background-color: #FFFFFF;
  padding: 37rpx 24rpx;
  border-top: 1rpx solid #E5E5E5;

  .info-row {
    display: flex;
    margin-bottom: 26rpx;
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      width: 160rpx;
      font-size: 32rpx;
      font-weight: 400;
      color: #6E7887;
      flex: 1;
    }

    .info-value {
      font-size: 32rpx;
      color: #000;
      font-weight: 400;
      text-align: right;
    }

    .sample-status-container {
      display: flex;
      align-items: center;
    }

    .sample-status {
      padding: 4rpx 12rpx;
      font-size: 24rpx;
      border-radius: 4rpx;
      margin-right: 10rpx;

      &.confirm {
        color: #FF7D00;
        background-color: #FFF7E8;
      }

      &.shipped {
        color: #00B42A;
        background-color: #E8FFEA;
      }

      &.returned {
        color: #00B42A;
        background-color: #E8FFEA;
      }
    }

    .sample-status-text {
      font-size: 28rpx;
      color: #000;
      font-weight: 400;
    }

    .sample-detail {
      font-size: 28rpx;
      font-weight: 400;
      color: #40E0D0;
    }
  }
}

// 地址卡片样式
.address-card {
  background-color: #FFFFFF;
  margin: 20rpx 20rpx 0;
  padding: 0;
  position: relative;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .card-header {
    display: flex;
    align-items: center;
    padding: 30rpx 24rpx 20rpx;
    border-bottom: 2rpx solid #f2f3f5;

    .circle-number {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      &.blue {
        background-color: #40E0D0;
      }

      &.green {
        background-color: #40E0D0;
      }

      .header-icon {
        width: 24rpx;
        height: 24rpx;
        border-radius: 40rpx;
        background: #26D1CB;
        padding: 9rpx;
      }
    }

    .header-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #101217;
    }
  }

  .card-body {
    padding: 24rpx 24rpx 30rpx 30rpx;

    .contact-info {
      display: flex;
      margin-bottom: 16rpx;

      .contact-name {
        font-size: 28rpx;
        color: #101217;
        margin-right: 20rpx;
        font-weight: 500;
      }

      .contact-phone {
        font-size: 28rpx;
        color: #86909C;
        font-weight: 400;
      }
    }

    .address-text {
      font-size: 28rpx;
      color: #86909C;
      line-height: 1.5;
      margin-bottom: 16rpx;
      font-weight: 400;
    }

    .address-note {
      font-size: 24rpx;
      color: rgba(0, 0, 0, 0.90);
      background-color: #F5F5F5;
      padding: 10rpx 20rpx;
      font-weight: 400;
      border-radius: 8rpx;
      text-align: center;
      margin-top: 20rpx;
      line-height: 1.5;
    }
  }

  .edit-icon {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    width: 48rpx;
    height: 48rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.logistics-section {
  background-color: #FFFFFF;
  margin: 20rpx 20rpx 0;
  padding: 30rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #101217;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #f2f3f5;
  }

  .form-item {
    padding: 20rpx 0;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 1rpx;
      background-color: #EEEEEE;
    }

    &:last-of-type:after {
      display: none;
    }

    .form-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .form-label {
      font-size: 32rpx;
      color: #333333;

      .required {
        color: #FF0000;
      }
    }

    .form-right {
      display: flex;
      align-items: center;

      .placeholder {
        color: #999999;
        font-size: 32rpx;
        margin-right: 10rpx;
      }

      .arrow {
        width: 32rpx;
        height: 32rpx;
        flex-shrink: 0;
      }

      .arrow-icon {
        width: 36rpx;
        height: 36rpx;
      }

      input {
        text-align: right;
        height: 80rpx;
        font-size: 32rpx;
        color: #333333;

        &::placeholder {
          color: #999999;
        }
      }
    }
  }

  .form-action {
    margin-top: 40rpx;
    justify-content: flex-end;

    .action-button {
      width: 280rpx;
      height: 80rpx;
      line-height: 80rpx;
      background-color: #FFFFFF;
      color: #00C0D4;
      font-size: 28rpx;
      border-radius: 12rpx;
      text-align: center;
      font-weight: 600;
      border: 1px solid #00C0D4;
      padding: 0;
      margin: 0;
    }
  }
}

.time-section {
  background-color: #FFFFFF;
  margin: 20rpx 20rpx 0;
  padding: 30rpx 24rpx 10rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #101217;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #f2f3f5;
  }

  .time-list {
    margin-top: 30rpx;

    .time-item {
      line-height: 64rpx;
      font-size: 32rpx;
      color: #000;
      font-weight: 400;
      padding-left: 10rpx;
      border-radius: 16rpx;
      background: #F4F8FB;
      padding: 9rpx 20rpx;
      margin-bottom: 20rpx;
    }
  }
}

.experiment-list {
  .experiment-section {
    background-color: #FFFFFF;
    margin: 20rpx 20rpx 0;
    padding: 30rpx 24rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      padding-bottom: 30rpx;
      border-bottom: 2rpx solid #EEEEEE;
    }

    .exp-info-row-container {
      display: flex;
      flex-direction: column;
      margin-top: 30rpx;
    }

    .exp-info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .exp-label {
        font-size: 28rpx;
        color: #6E7887;
        font-weight: 400;
      }

      .exp-value {
        font-size: 28rpx;
        color: #000;
        text-align: right;
        font-weight: 400;
      }
    }
  }
}

.bottom-space {
  height: 40rpx;
}

/* 展开/收起功能样式 */
.expandable-list {
  width: 100%;
}

.list-content {
  margin-bottom: 8rpx;
}

.list-item-row {
  display: block;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-text {
  font-size: 32rpx;
  color: #000;
  line-height: 1.5;
  display: block;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12rpx;
  padding: 4rpx 0;
  cursor: pointer;
}

.expand-text {
  font-size: 28rpx;
  color: #40E0D0;
  margin-right: 8rpx;
}

.expand-icon {
  font-size: 24rpx;
  color: #40E0D0;
  transition: transform 0.3s ease;
}
</style>