<template>
  <view class="experiment-container">
    <!-- 顶部信息区域 -->
    <view class="header-info">
      <view class="device-info">
        <view class="device-icon">
          <image src="/static/icons/experiment.png" mode="aspectFit"></image>
        </view>
        <view class="device-details">
          <text class="device-name">{{ experimentData.deviceName }}</text>
          <text class="device-location">{{ experimentData.location }}</text>
        </view>
      </view>
      <view class="experiment-time">
        <text class="time-label">实验时间</text>
        <text class="time-value">{{ experimentData.time }}</text>
      </view>
    </view>

    <!-- 实验内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 实验信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">实验信息</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">实验名称</text>
            <text class="info-value">{{ experimentData.name }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">实验类型</text>
            <text class="info-value">{{ experimentData.type }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">样品数量</text>
            <text class="info-value">{{ experimentData.sampleCount }}个</text>
          </view>
          <view class="info-item">
            <text class="info-label">实验人员</text>
            <text class="info-value">{{ experimentData.operator }}</text>
          </view>
        </view>
      </view>

      <!-- 实验步骤 -->
      <view class="steps-section">
        <view class="section-header">
          <text class="section-title">实验步骤</text>
        </view>
        <view class="section-content">
          <view 
            class="step-item" 
            v-for="(step, index) in experimentData.steps" 
            :key="index"
            :class="{ 'completed': step.completed }"
          >
            <view class="step-checkbox" @tap="toggleStep(index)">
              <image 
                :src="step.completed ? '/static/icons/checked.png' : '/static/icons/unchecked.png'" 
                mode="aspectFit"
              ></image>
            </view>
            <view class="step-content">
              <text class="step-title">{{ index + 1 }}. {{ step.title }}</text>
              <text class="step-desc">{{ step.description }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 注意事项 -->
      <view class="notice-section">
        <view class="section-header">
          <text class="section-title">注意事项</text>
        </view>
        <view class="section-content">
          <view class="notice-item" v-for="(notice, index) in experimentData.notices" :key="index">
            <text class="notice-dot">•</text>
            <text class="notice-text">{{ notice }}</text>
          </view>
        </view>
      </view>

      <!-- 实验记录 -->
      <view class="record-section">
        <view class="section-header">
          <text class="section-title">实验记录</text>
        </view>
        <view class="section-content">
          <view class="record-input">
            <textarea 
              v-model="experimentRecord" 
              placeholder="请输入实验过程中的观察记录、数据等内容..." 
              maxlength="1000"
            ></textarea>
            <text class="word-count">{{ experimentRecord.length }}/1000</text>
          </view>
        </view>
      </view>

      <!-- 实验文件 -->
      <view class="files-section">
        <view class="section-header">
          <text class="section-title">实验文件</text>
        </view>
        <view class="section-content">
          <view class="upload-area">
            <view class="uploaded-files">
              <view class="file-item" v-for="(file, index) in uploadedFiles" :key="index">
                <view class="file-icon">
                  <image :src="getFileIcon(file.type)" mode="aspectFit"></image>
                </view>
                <view class="file-info">
                  <text class="file-name">{{ file.name }}</text>
                  <text class="file-size">{{ formatFileSize(file.size) }}</text>
                </view>
                <view class="file-delete" @tap="deleteFile(index)">
                  <image src="/static/svg/trash.svg" mode="aspectFit"></image>
                </view>
              </view>
            </view>
            <view class="upload-btn" @tap="chooseFile">
              <image src="/static/icons/file.png" mode="aspectFit"></image>
              <text>上传文件</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作区 -->
    <view class="footer-actions">
      <view class="action-btn cancel" @tap="cancelExperiment">取消实验</view>
      <view class="action-btn primary" @tap="completeExperiment">完成实验</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      experimentRecord: '',
      uploadedFiles: [],
      experimentData: {
        deviceName: 'X射线衍射仪 XRD-8100',
        location: 'A栋305实验室',
        time: '2023-06-20 14:00-16:00',
        name: '高精度材料分析实验',
        type: '材料成分分析',
        sampleCount: 3,
        operator: '李老师',
        steps: [
          {
            title: '设备开机与预热',
            description: '打开主电源，启动系统，等待设备预热10分钟',
            completed: false
          },
          {
            title: '样品准备与放置',
            description: '将样品放置在样品台上，确保平整稳固',
            completed: false
          },
          {
            title: '参数设置',
            description: '设置扫描角度范围为10°-80°，扫描速度为2°/min',
            completed: false
          },
          {
            title: '开始扫描',
            description: '点击软件中的"开始"按钮，启动扫描过程',
            completed: false
          },
          {
            title: '数据采集',
            description: '等待扫描完成，保存原始数据文件',
            completed: false
          },
          {
            title: '设备关闭',
            description: '实验结束后，关闭X射线源，然后关闭主电源',
            completed: false
          }
        ],
        notices: [
          '实验过程中请穿戴好个人防护装备，包括防护眼镜和手套',
          '操作X射线设备时，确保辐射防护门已关闭',
          '样品更换时，必须确保X射线源已关闭',
          '如遇设备异常，请立即停止实验并联系技术支持人员',
          '实验结束后，请清理实验台面，恢复设备初始状态'
        ]
      }
    }
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      // 实际项目中应该根据orderId加载实验数据
    }
  },
  methods: {
    // 切换步骤完成状态
    toggleStep(index) {
      this.experimentData.steps[index].completed = !this.experimentData.steps[index].completed;
    },
    
    // 选择文件
    chooseFile() {
      uni.chooseFile({
        count: 5,
        type: 'all',
        success: (res) => {
          const files = res.tempFiles;
          files.forEach(file => {
            // 获取文件类型
            const fileType = this.getFileType(file.name);
            
            // 添加到已上传文件列表
            this.uploadedFiles.push({
              name: file.name,
              size: file.size,
              path: file.path,
              type: fileType
            });
          });
          
          uni.showToast({
            title: '文件上传成功',
            icon: 'success'
          });
        }
      });
    },
    
    // 删除文件
    deleteFile(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该文件吗？',
        success: (res) => {
          if (res.confirm) {
            this.uploadedFiles.splice(index, 1);
            uni.showToast({
              title: '文件已删除',
              icon: 'success'
            });
          }
        }
      });
    },
    
    // 获取文件类型
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        'pdf': 'pdf',
        'doc': 'word',
        'docx': 'word',
        'xls': 'excel',
        'xlsx': 'excel',
        'ppt': 'ppt',
        'pptx': 'ppt',
        'jpg': 'image',
        'jpeg': 'image',
        'png': 'image',
        'gif': 'image',
        'zip': 'zip',
        'rar': 'zip',
        '7z': 'zip',
        'txt': 'text'
      };
      
      return typeMap[extension] || 'default';
    },
    
    // 获取文件图标
    getFileIcon(type) {
      const iconMap = {
        'pdf': '/static/icons/file/pdf.png',
        'word': '/static/icons/file/doc.png',
        'excel': '/static/icons/file/excel.png',
        'ppt': '/static/icons/file/ppt.png',
        'image': '/static/icons/file/image.png',
        'zip': '/static/icons/file/zip.png',
        'text': '/static/icons/file/txt.png',
        'default': '/static/icons/file/file.png'
      };
      
      return iconMap[type] || iconMap.default;
    },
    
    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + 'B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + 'KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + 'MB';
      }
    },
    
    // 取消实验
    cancelExperiment() {
      uni.showModal({
        title: '提示',
        content: '确定要取消实验吗？已录入的数据将不会保存',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    
    // 完成实验
    completeExperiment() {
      // 检查是否完成所有步骤
      const uncompletedSteps = this.experimentData.steps.filter(step => !step.completed);
      
      if (uncompletedSteps.length > 0) {
        uni.showModal({
          title: '提示',
          content: '您还有未完成的实验步骤，确定要提交吗？',
          success: (res) => {
            if (res.confirm) {
              this.submitExperiment();
            }
          }
        });
      } else {
        this.submitExperiment();
      }
    },
    
    // 提交实验数据
    submitExperiment() {
      uni.showLoading({
        title: '正在提交...'
      });
      
      // 模拟提交过程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '实验已完成',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack({
            delta: 2 // 返回到订单列表页
          });
        }, 1500);
      }, 2000);
    }
  }
}
</script>

<style lang="scss" scoped>
.experiment-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.header-info {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  padding: 30rpx;
  color: #ffffff;
  
  .device-info {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .device-icon {
      width: 80rpx;
      height: 80rpx;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
    
    .device-details {
      flex: 1;
      
      .device-name {
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 8rpx;
        display: block;
      }
      
      .device-location {
        font-size: 26rpx;
        opacity: 0.8;
      }
    }
  }
  
  .experiment-time {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    
    .time-label {
      font-size: 26rpx;
      opacity: 0.8;
      margin-right: 20rpx;
    }
    
    .time-value {
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 20rpx;
}

.info-section, .steps-section, .notice-section, .record-section, .files-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-header {
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .section-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 24rpx;
        background-color: #9C27B0;
        border-radius: 3rpx;
      }
    }
  }
  
  .section-content {
    padding: 20rpx 30rpx;
  }
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .info-label {
    width: 160rpx;
    font-size: 28rpx;
    color: #666666;
    flex-shrink: 0;
  }
  
  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: #333333;
  }
}

.step-item {
  display: flex;
  margin-bottom: 24rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  background-color: #f9f9f9;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.completed {
    background-color: rgba(156, 39, 176, 0.05);
    
    .step-title {
      color: #9C27B0;
      text-decoration: line-through;
    }
  }
  
  .step-checkbox {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
    flex-shrink: 0;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .step-content {
    flex: 1;
    
    .step-title {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
      margin-bottom: 8rpx;
      display: block;
    }
    
    .step-desc {
      font-size: 26rpx;
      color: #666666;
    }
  }
}

.notice-item {
  display: flex;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .notice-dot {
    margin-right: 12rpx;
    color: #9C27B0;
    font-size: 28rpx;
  }
  
  .notice-text {
    flex: 1;
    font-size: 26rpx;
    color: #333333;
    line-height: 1.6;
  }
}

.record-input {
  position: relative;
  
  textarea {
    width: 100%;
    height: 240rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 28rpx;
    color: #333333;
    box-sizing: border-box;
  }
  
  .word-count {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    font-size: 24rpx;
    color: #999999;
  }
}

.upload-area {
  .uploaded-files {
    margin-bottom: 20rpx;
    
    .file-item {
      display: flex;
      align-items: center;
      padding: 16rpx;
      background-color: #f9f9f9;
      border-radius: 12rpx;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .file-icon {
        width: 60rpx;
        height: 60rpx;
        margin-right: 16rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
      
      .file-info {
        flex: 1;
        
        .file-name {
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 8rpx;
          display: block;
        }
        
        .file-size {
          font-size: 24rpx;
          color: #999999;
        }
      }
      
      .file-delete {
        width: 48rpx;
        height: 48rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  
  .upload-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 180rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    border: 2rpx dashed #cccccc;
    
    image {
      width: 60rpx;
      height: 60rpx;
      margin-bottom: 16rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.footer-actions {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  
  .action-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    font-size: 28rpx;
    
    &.cancel {
      background-color: #f5f5f5;
      color: #666666;
      margin-right: 20rpx;
    }
    
    &.primary {
      background-color: #9C27B0;
      color: #ffffff;
    }
  }
}
</style> 