<template>
  <view class="order-list-container">
    <!-- 顶部搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <image src="/static/svg/search.svg" class="search-icon"></image>
        <input type="text" placeholder="请输入订单编号或实验名称" v-model="searchKeyword" @confirm="searchOrders" />
      </view>
    </view>

    <!-- 状态切换栏 -->
    <view class="status-tabs-container">
      <scroll-view class="status-tabs" scroll-x show-scrollbar="false">
        <view class="tabs-wrapper">
          <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: currentTab === index }"
            @tap="switchTab(index)">
            <text>{{ tab.name }}</text>
            <view class="tab-badge" v-if="tab.status === 1 && tab.count && tab.count > 0">{{ tab.count }}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list-wrapper">
      <lk-page-list ref="pageList" embed @mounted="onPageListMounted" :show-more-status="true"
        @showListChange="onShowListChange">
        <view class="order-list">
          <view class="order-item" v-for="(order, index) in orderList" :key="index" @tap="goToDetail(order.id)">
            <view class="order-header">
              <text class="instrument-name">{{ order.instrumentName || "等离子体光谱仪XP-821" }}</text>
              <view class="order-status" :class="'status-' + order.status">{{ getStatusText(order.status) }}</view>
            </view>

            <view class="order-info">
              <view class="info-row">
                <text class="info-label">订单编号：</text>
                <text class="info-value">{{ order.orderNo }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实验名称：</text>
                <text class="info-value">{{ order.experimentName }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">订单类型：</text>
                <text class="info-value">{{ getOrderTypeText(order.type) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实验类型：</text>
                <text class="info-value">{{ getExperimentTypeText(order.experimentType) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">样品信息：</text>
                <view class="sample-status-container">
                  <text v-if="getSampleStatusDisplay(order).type === 'text'" class="sample-status-text">{{
                    getSampleStatusDisplay(order).text }}</text>
                  <text v-else class="sample-status" :class="getSampleStatusDisplay(order).class">{{
                    getSampleStatusDisplay(order).text }}</text>
                  <text v-if="shouldShowSampleDetail(order)" class="sample-detail"
                    @tap.stop="goToDetail(order.id, 1)">详情</text>
                </view>
              </view>
              <view class="info-row">
                <text class="info-label">预约时间：</text>
                <text class="info-value">{{ formatAppointmentTime(order) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">创建时间：</text>
                <text class="info-value">{{ order.createTime }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">订单金额：</text>
                <text class="info-value price">¥{{ (order.totalAmount || 0).toFixed(2) }}</text>
              </view>
            </view>

            <view class="order-footer">
              <!-- 待审核状态 -->
              <view v-if="order.status === 1" class="btn-group">
                <button class="btn btn-cancel" @tap.stop="cancelOrder(order.id)">取消</button>
                <button class="btn btn-look" @tap.stop="goToDetail(order.id, 0)">查看</button>
              </view>
              <!-- 待确认状态 -->
              <view v-else-if="order.status === 2" class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
                <button class="btn btn-primary" @tap.stop="confirmOrder(order.id)">确认</button>
              </view>
              <!-- 待实验状态 -->
              <view v-else-if="order.status === 3" class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
                <button v-if="order.type === 3" class="btn btn-primary"
                  @tap.stop="startExperiment(order.id)">上机</button>
              </view>
              <!-- 实验中状态 -->
              <view v-else-if="order.status === 4" class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
                <button v-if="order.type === 3" class="btn btn-primary"
                  @tap.stop="finishExperiment(order.id)">下机</button>
              </view>
              <!-- 待结算状态 -->
              <view v-else-if="order.status === 5" class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
                <button class="btn btn-primary" @tap.stop="settleOrder(order.id)">结算</button>
              </view>
              <!-- 已完成状态 -->
              <view v-else-if="order.status === 7" class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
                <button class="btn btn-primary" @tap.stop="downloadData(order.id)">下载数据</button>
              </view>
              <!-- 已驳回状态 -->
              <view v-else-if="order.status === 10" class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
                <button class="btn btn-primary" @tap.stop="resubmitOrder(order.id)">重新提交</button>
              </view>
              <!-- 其他状态只显示查看按钮 -->
              <view v-else class="btn-group">
                <button class="btn btn-secondary" @tap.stop="goToDetail(order.id, 0)">查看</button>
              </view>
            </view>
          </view>
        </view>
      </lk-page-list>
    </view>
  </view>
</template>

<script>
import orderApi from '@/api/order';

export default {
  data() {
    return {
      searchKeyword: '',
      currentTab: 0,
      tabs: [
        { name: '全部订单', status: 'all' },
        { name: '待审核', status: 1, count: 0 },
        { name: '待确认', status: 2 },
        { name: '待实验', status: 3 },
        { name: '实验中', status: 4 },
        { name: '待结算', status: 5 },
        { name: '已完成', status: 7 },
        { name: '已取消', status: 8 },
        { name: '已退款', status: 11 },
        { name: '已关闭', status: 9 },
        { name: '已驳回', status: 10 }
      ],
      orderList: [],
      currentStatus: 'all', // 默认选中全部订单
      isRefreshing: false // 是否正在刷新，防止重复调用
    }
  },
  onLoad(options) {
    // 如果有状态参数，切换到对应标签
    if (options.status) {
      // 将字符串转换为数字（如果需要）
      const statusValue = options.status === 'all' ? 'all' : parseInt(options.status);

      const tabIndex = this.tabs.findIndex(tab => tab.status === statusValue);

      if (tabIndex !== -1) {
        this.currentTab = tabIndex;
        this.currentStatus = this.tabs[tabIndex].status;
      } else {
      }
    }
    // 注意：不在这里设置默认状态，让 data() 中的默认值生效
  },
  methods: {
    // 分页列表组件挂载完成
    onPageListMounted() {
      this.configPageList();
    },

    // 配置分页列表
    configPageList() {
      if (!this.$refs.pageList) {
        return;
      }

      const self = this;

      this.orderList = this.$refs.pageList.config({
        request: async (params) => {
          // 构建请求参数
          const requestParams = {
            current: params.current || 1,
            size: params.size || 10
          };

          // 如果有选中的状态，添加到参数中
          if (self.currentStatus !== null && self.currentStatus !== 'all') {
            requestParams.status = self.currentStatus;
          }

          // 如果有搜索关键词，添加到参数中
          if (self.searchKeyword) {
            requestParams.searchKey = self.searchKeyword;
          }


          try {
            // 调用API获取数据
            const result = await orderApi.getOrderList(requestParams);

            return result;
          } catch (error) {
            throw error;
          }
        },
        onRefreshFinish: () => {
          // 手动同步数据
          if (self.$refs.pageList?.pageData?.list) {
            self.orderList = self.$refs.pageList.pageData.list;
          }
          // 更新待审核徽章数量（遍历当前列表计算）
          self.updatePendingCountFromList();
          // 重置刷新标志
          self.isRefreshing = false;
        },
        onLoadMoreFinish: () => {
          // 手动同步数据
          if (self.$refs.pageList?.pageData?.list) {
            self.orderList = self.$refs.pageList.pageData.list;
          }
        }
      });
      // 初始加载数据
      this.$refs.pageList.refresh(true);
    },

    // 切换标签
    switchTab(index) {
      if (this.currentTab === index) return;

      // 防止重复调用
      if (this.isRefreshing) {
        return;
      }
      this.currentTab = index;
      this.currentStatus = this.tabs[index].status;

      // 刷新列表
      if (this.$refs.pageList) {
        this.isRefreshing = true;
        this.$refs.pageList.refresh(true);
      }
    },

    // 搜索订单
    searchOrders() {
      // 刷新列表
      if (this.$refs.pageList) {
        this.$refs.pageList.refresh(true);
      }
    },

    // 列表数据变化回调
    onShowListChange(list) {
      // 更新订单列表数据
      this.orderList = list;
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '待审核',
        2: '待确认',
        3: '待实验',
        4: '实验中',
        5: '待结算',
        6: '已结算',
        7: '已完成',
        8: '已取消',
        9: '已关闭',
        10: '已驳回',
        11: '已退款',
        12: '培训通过',
        13: '培训不通过'
      };
      return statusMap[status] || status;
    },

    // 获取订单类型文本
    getOrderTypeText(type) {
      const typeMap = {
        1: '送样申请',
        2: '仪器预约-工程师操作',
        3: '仪器预约-自主操作',
        4: '需求订单'
      };
      return typeMap[type] || type;
    },

    // 获取实验类型文本
    getExperimentTypeText(type) {
      const typeMap = {
        1: '培训',
        2: '试测',
        3: '正式'
      };
      return typeMap[type] || type;
    },

    // 跳转到订单详情
    goToDetail(id, tab = 0) {
      uni.navigateTo({
        url: `/pages/orders/orderDetail?id=${id}&tab=${tab}`
      });
    },

    // 取消订单
    async cancelOrder(id) {
      try {
        // 获取订单详情判断是否24小时内
        const orderDetail = await orderApi.getOrderDetail({ id });
        const createTime = new Date(orderDetail.createTime);
        const now = new Date();
        const hoursDiff = (now - createTime) / (1000 * 60 * 60);

        const content = hoursDiff >= 24
          ? '提前24小时以上可免费取消，24小时内需要联系负责人取消'
          : '提前24小时以上可免费取消，24小时内需要联系负责人取消';

        uni.showModal({
          title: '取消订单',
          content: content,
          cancelText: '取消',
          confirmText: '确认取消',
          success: (res) => {
            if (res.confirm) {
              if (hoursDiff >= 24) {
                // 24小时以上，直接取消
                uni.showToast({
                  title: '取消成功',
                  icon: 'success'
                });
                // 刷新列表
                this.$refs.pageList.refresh(true);
              } else {
                // 24小时内，提示联系负责人
                uni.showToast({
                  title: '请联系负责人',
                  icon: 'none'
                });
              }
            }
          }
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 确认订单
    async confirmOrder(id) {
      try {
        // 获取订单详情判断是否需要邮寄且是否已填写快递单号
        const orderDetail = await orderApi.getOrderDetail({ id });

        // 送样申请及需要邮寄的订单
        if (orderDetail.type === 1 && orderDetail.sampleReturnable === 1) {
          // 检查是否已填写快递单号（这里假设有个字段表示快递单号状态）
          if (!orderDetail.expressNo) {
            uni.showModal({
              title: '提示',
              content: '请在邮寄详情中填写快递单号',
              cancelText: '取消',
              confirmText: '去填写',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到邮寄信息页面（详情的第二个tab）
                  uni.navigateTo({
                    url: `/pages/orders/orderDetail?id=${id}&tab=1`
                  });
                }
              }
            });
            return;
          }
        }

        // 已填写快递单号或自带样品的情况
        uni.showModal({
          title: '确认订单',
          content: '负责人会修改相关订单信息，请检查后确认',
          cancelText: '取消',
          confirmText: '确认',
          success: (res) => {
            if (res.confirm) {
              // 订单状态变更为待实验
              uni.showToast({
                title: '订单已确认',
                icon: 'success'
              });
              // 刷新列表
              this.$refs.pageList.refresh(true);
            }
          }
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 开始实验
    startExperiment(id) {
      uni.navigateTo({
        url: `/pages/orders/startExperiment?id=${id}`
      });
    },

    // 结束实验（下机）
    finishExperiment(id) {
      uni.showModal({
        title: '提示',
        content: '确定要结束实验吗？',
        success: (res) => {
          if (res.confirm) {
            const index = this.orderList.findIndex(item => item.id === id);
            if (index !== -1) {
              this.orderList[index].status = 'settlement';
              uni.showToast({
                title: '实验已结束',
                icon: 'success'
              });
            }
          }
        }
      });
    },

    // 结算订单
    settleOrder(id) {
      uni.showModal({
        title: '结算',
        content: '请确认实际金额，若有疑问联系负责人',
        cancelText: '取消',
        confirmText: '确定结算',
        success: (res) => {
          if (res.confirm) {
            // 订单状态变更为已结算，扣除该订单的冻结金额
            uni.showToast({
              title: '结算完成',
              icon: 'success'
            });
            // 刷新列表
            this.$refs.pageList.refresh(true);
          }
        }
      });
    },

    // 下载数据
    downloadData(id) {
      // TODO: 等后台提供下载接口后实现
      uni.showLoading({
        title: '准备下载...'
      });

      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '下载完成',
          icon: 'success'
        });
      }, 2000);

      // 实际项目中这里应该调用下载API
      // try {
      //   const downloadUrl = await orderApi.downloadData({ id });
      //   // 处理下载逻辑
      // } catch (error) {
      //   uni.showToast({
      //     title: '下载失败',
      //     icon: 'none'
      //   });
      // }
    },

    // 重新提交订单
    async resubmitOrder(id) {
      try {
        // 获取订单详情，获取仪器信息
        const orderDetail = await orderApi.getOrderDetail({ id });

        // 跳转到预约页面，重新编辑表单提交
        const instrumentId = orderDetail.instrument?.instrumentId || orderDetail.instrumentId;
        const instrumentName = orderDetail.instrument?.name || orderDetail.instrumentName || '仪器';

        uni.navigateTo({
          url: `/pages/instrument/reserve?instrumentId=${instrumentId}&instrumentName=${encodeURIComponent(instrumentName)}&orderId=${id}`
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 格式化预约时间
    formatAppointmentTime(order) {
      // 如果有 timeSlots 数组且不为空
      if (order.timeSlots && order.timeSlots.length > 0) {
        const timeSlot = order.timeSlots[0]; // 取第一个时间段

        if (timeSlot.startTime && timeSlot.endTime) {
          // 格式化开始时间：YYYY-MM-DD HH:mm
          const startTime = this.formatDateTime(timeSlot.startTime);
          // 格式化结束时间：只显示 HH:mm
          const endTime = this.formatTime(timeSlot.endTime);

          return `${startTime}-${endTime}`;
        }
      }

      // 兼容旧的 instrument 字段格式
      if (order.instrument && order.instrument.startTime && order.instrument.endTime) {
        const startTime = this.formatDateTime(order.instrument.startTime);
        const endTime = this.formatTime(order.instrument.endTime);
        return `${startTime}-${endTime}`;
      }

      return '待安排';
    },

    // 格式化日期时间为 YYYY-MM-DD HH:mm
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';

      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 格式化时间为 HH:mm
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';

      const date = new Date(dateTimeStr);
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${hours}:${minutes}`;
    },

    // 获取样品状态显示信息
    getSampleStatusDisplay(order) {
      // 根据后端返回的 shipStatus 字段来判断样品状态
      // shipStatus: 0:自带 1:待寄出 2:已寄出 3:已签收 4:已寄回

      // 对于送样申请订单 (type === 1)
      if (order.type === 1) {
        switch (order.shipStatus) {
          case 0:
            return { type: 'text', text: '自带' };
          case 1:
            return { type: 'status', text: '待邮寄', class: 'confirm' };
          case 2:
            return { type: 'status', text: '已寄出', class: 'shipped' };
          case 3:
            return { type: 'status', text: '已签收', class: 'shipped' };
          case 4:
            return { type: 'status', text: '已寄回', class: 'returned' };
          default:
            return { type: 'text', text: '自带' };
        }
      }
      // 对于仪器预约订单，样品都是自带
      return { type: 'text', text: '自带' };
    },

    // 判断是否显示样品详情链接
    shouldShowSampleDetail(order) {
      // 送样申请订单且不是待审核状态时显示详情
      return order.type === 1 && order.status !== 1;
    },

    // 更新待审核徽章数量（遍历当前列表计算）
    updatePendingCountFromList() {
      // 只有在查看全部订单时才能准确计算徽章数量
      if (this.currentStatus === 'all') {
        const pendingCount = this.orderList.filter(order => order.status === 1).length;

        // 更新待审核tab的徽章数量
        const pendingTabIndex = this.tabs.findIndex(tab => tab.status === 1);
        if (pendingTabIndex !== -1) {
          this.$set(this.tabs[pendingTabIndex], 'count', pendingCount);
        }

      }
    }

  }
}
</script>

<style lang="scss" scoped>
.order-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  overflow: hidden;
  /* 防止整体滚动 */
}

.search-bar {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  flex-shrink: 0;
  /* 防止被压缩 */
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f0f2f5;
  border-radius: 30rpx;
  padding: 15rpx 20rpx;

  .search-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 10rpx;
  }

  input {
    flex: 1;
    font-size: 28rpx;
    height: 40rpx;
  }
}

.status-tabs-container {
  background-color: #ffffff;
  flex-shrink: 0;
  /* 防止被压缩 */
}

.order-list-wrapper {
  flex: 1;
  height: 0;
  overflow: auto;
  margin-bottom: 28rpx;
}

.status-tabs {
  width: 100%;
  white-space: nowrap;
}

.tabs-wrapper {
  display: flex;
  padding: 0 12rpx;
}

.tab-item {
  flex-shrink: 0;
  min-width: 140rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border-radius: 35rpx;
  padding: 0 20rpx;
  margin-right: 16rpx;
  transition: all 0.3s;
  margin: 20rpx 0;

  &:last-child {
    margin-right: 12rpx;
  }

  text {
    font-size: 28rpx;
    color: #666;
    transition: color 0.3s;
    white-space: nowrap;
  }

  &.active {
    background-color: #40E0D0;

    text {
      font-size: 32rpx;
      color: #FFFFFF;
      font-weight: 600;
    }
  }

  .tab-badge {
    position: absolute;
    top: -5rpx;
    right: 5rpx;
    min-width: 32rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    background-color: #FF5B5B;
    color: #FFFFFF;
    border-radius: 16rpx;
    font-size: 20rpx;
  }
}

.order-scroll {
  flex: 1;
  width: 100%;
}

.order-list {
  padding: 28rpx 28rpx 0 28rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 37rpx 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .instrument-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #000;
  }

  .order-status {
    font-size: 24rpx;
    padding: 8rpx 12rpx;
    border-radius: 4rpx;
    font-weight: 400;

    // 待审核
    &.status-1 {
      color: #FF7D00;
      background-color: #FFF7E8;
    }

    // 待确认
    &.status-2 {
      color: #FF7D00;
      background-color: #FFF7E8;
    }

    // 待实验
    &.status-3 {
      color: #FF7D00;
      background-color: #FFF7E8;
    }

    // 实验中
    &.status-4 {
      color: #165DFF;
      background-color: #E8F3FF;
    }

    // 待结算
    &.status-5 {
      color: #722ED1;
      background-color: #F9F0FF;
    }

    // 已结算
    &.status-6 {
      color: #00B42A;
      background-color: #E8FFEA;
    }

    // 已完成
    &.status-7 {
      color: #4E5969;
      background-color: #F2F3F5;
    }

    // 已取消
    &.status-8 {
      color: #86909C;
      background-color: #F7F8FA;
    }

    // 已关闭
    &.status-9 {
      color: #86909C;
      background-color: #F7F8FA;
    }

    // 已驳回
    &.status-10 {
      color: #F53F3F;
      background-color: #FFECE8;
    }

    // 已退款
    &.status-11 {
      color: #00B42A;
      background-color: #E8FFEA;
    }

    // 培训通过
    &.status-12 {
      color: #00B42A;
      background-color: #E8FFEA;
    }

    // 培训不通过
    &.status-13 {
      color: #F53F3F;
      background-color: #FFECE8;
    }
  }
}

.order-info {
  padding: 24rpx 0;

  .info-row {
    display: flex;
    margin-bottom: 16rpx;
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      width: 160rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #6E7887;
      flex: 1;
    }

    .info-value {
      font-size: 28rpx;
      color: #000;
      font-weight: 400;
    }

    .sample-status-container {
      display: flex;
      align-items: center;
    }

    .sample-status {
      padding: 4rpx 12rpx;
      font-size: 24rpx;
      border-radius: 4rpx;
      margin-right: 10rpx;

      &.confirm {
        color: #FF7D00;
        background-color: #FFF7E8;
      }

      &.shipped {
        color: #00B42A;
        background-color: #E8FFEA;
      }

      &.returned {
        color: #00B42A;
        background-color: #E8FFEA;
      }
    }

    .sample-status-text {
      font-size: 28rpx;
      color: #000;
      font-weight: 400;
    }

    .sample-detail {
      font-size: 28rpx;
      font-weight: 400;
      color: #26D1CB;
    }
  }
}

.order-footer {
  padding: 20rpx 0 0 0;
  border-top: 1rpx solid #f0f0f0;
  width: 100%;

  .btn-group {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
  }

  .btn {
    height: 68rpx;
    line-height: 68rpx;
    font-size: 32rpx;
    border-radius: 12rpx;
    margin: 0;

    &.btn-cancel {
      color: #4E5969;
      background-color: #fff;
      margin-right: 32rpx;
      border: 1rpx solid #E5E6EB;
    }

    &.btn-look {
      color: #26D1CB;
      background-color: #fff;
      border: 1rpx solid #26D1CB;
    }

    &.btn-secondary {
      color: #26D1CB;
      background-color: #fff;
      border: 1rpx solid #26D1CB;
    }

    &.btn-primary {
      color: #fff;
      margin-left: 32rpx;
      background-color: #26D1CB;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.loading-state,
.load-all {
  text-align: center;
  padding: 30rpx 0;

  text {
    font-size: 26rpx;
    color: #999;
  }
}
</style>