<template>
  <view class="identity-picker" v-if="show">
    <view class="picker-mask" @tap="onCancel"></view>
    <view class="picker-content">
      <view class="picker-header">
        <text class="cancel-btn" @tap="onCancel">取消</text>
        <text class="title">选择身份类型</text>
        <text class="confirm-btn" @tap="onConfirm">确认</text>
      </view>
      
      <view class="picker-body">
        <view class="picker-columns">
          <!-- 第一级选项 -->
          <view class="column left-column">
            <view 
              class="identity-item" 
              v-for="(item, index) in firstLevelOptions" 
              :key="index"
              :class="{ active: selectedFirstLevel === index }"
              @tap="selectFirstLevel(index)"
            >
              <text>{{ item.label }}</text>
            </view>
          </view>
          
          <!-- 第二级选项 -->
          <view class="column right-column">
            <view 
              class="identity-item" 
              v-for="(item, index) in secondLevelOptions" 
              :key="index"
              :class="{ active: selectedSecondLevel === index }"
              @tap="selectSecondLevel(index)"
            >
              <text>{{ item.label }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="picker-footer">
        <view class="indicator"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'identity-picker',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    defaultFirstLevel: {
      type: Number,
      default: 0
    },
    defaultSecondLevel: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      selectedFirstLevel: 0,
      selectedSecondLevel: 0,
      // 第一级选项
      firstLevelOptions: [
        { label: '高校', value: 'university', hasChildren: true },
        { label: '研究院', value: 'institute', hasChildren: false }
      ],
      // 第二级选项（根据第一级动态变化）
      secondLevelOptionsMap: {
        0: [ // 高校的子选项
          { label: '老师', value: 'teacher' },
          { label: '在读本科', value: 'undergraduate' },
          { label: '在读硕士', value: 'master' },
          { label: '在读博士', value: 'phd' },
          { label: '博士后', value: 'postdoc' }
        ],
        1: [] // 研究院没有子选项
      }
    }
  },
  computed: {
    // 当前选中的第二级选项列表
    secondLevelOptions() {
      return this.secondLevelOptionsMap[this.selectedFirstLevel] || [];
    },
    
    // 当前选中的完整选项
    selectedOption() {
      const firstLevel = this.firstLevelOptions[this.selectedFirstLevel];
      
      // 如果第一级没有子选项，直接返回第一级
      if (!firstLevel.hasChildren) {
        return {
          firstLevel,
          secondLevel: null,
          fullLabel: firstLevel.label,
          fullValue: firstLevel.value
        };
      }
      
      // 如果有子选项，返回组合值
      const secondLevel = this.secondLevelOptions[this.selectedSecondLevel] || null;
      if (!secondLevel) {
        return {
          firstLevel,
          secondLevel: null,
          fullLabel: firstLevel.label,
          fullValue: firstLevel.value
        };
      }
      
      return {
        firstLevel,
        secondLevel,
        fullLabel: `${firstLevel.label} ${secondLevel.label}`,
        fullValue: `${firstLevel.value}_${secondLevel.value}`
      };
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.selectedFirstLevel = this.defaultFirstLevel;
        this.selectedSecondLevel = this.defaultSecondLevel;
      }
    },
    // 当第一级选项变化时，重置第二级选项的选中状态
    selectedFirstLevel() {
      this.selectedSecondLevel = 0;
    }
  },
  methods: {
    // 选择第一级选项
    selectFirstLevel(index) {
      this.selectedFirstLevel = index;
    },
    
    // 选择第二级选项
    selectSecondLevel(index) {
      this.selectedSecondLevel = index;
    },
    
    // 取消选择
    onCancel() {
      this.$emit('cancel');
    },
    
    // 确认选择
    onConfirm() {
      this.$emit('confirm', {
        firstLevelIndex: this.selectedFirstLevel,
        secondLevelIndex: this.selectedSecondLevel,
        option: this.selectedOption
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.identity-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.picker-content {
  position: relative;
  z-index: 1002;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1px solid #F0F0F0;
  
  .cancel-btn {
    font-size: 32rpx;
    color: #333;
  }
  
  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #000;
  }
  
  .confirm-btn {
    font-size: 32rpx;
    color: #40E0D0;
  }
}

.picker-body {
  padding: 20rpx 0;
  max-height: 600rpx;
  overflow-y: hidden;
}

.picker-columns {
  display: flex;
  height: 400rpx;
  
  .column {
    flex: 1;
    height: 100%;
    overflow-y: auto;
  }
  
  .left-column {
    border-right: 1px solid #F0F0F0;
  }
}

.identity-item {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  
  &.active {
    color: #40E0D0;
    background-color: #F5F5F5;
    font-weight: 500;
  }
}

.picker-footer {
  height: 40rpx;
  display: flex;
  justify-content: center;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  
  .indicator {
    width: 120rpx;
    height: 8rpx;
    background-color: #D8D8D8;
    border-radius: 4rpx;
    margin-top: 16rpx;
  }
}
</style> 