<template>
  <view class="recharge-popup" v-if="show">
    <view class="popup-mask" @tap="onCancel"></view>
    <view class="popup-content">
      <view class="popup-title">充值</view>
      
      <view class="popup-body">
        <view class="notice-box">
          <view class="notice-text">提示：当前仅支持对公打款，请打款后联系负责人修改账户余额</view>
        </view>
        
        <view class="account-section">
          <view class="label-text">对公打款账户：</view>
          <view class="account-number">44444556456465465454848</view>
        </view>
        
        <view class="label-text">请添加负责人：</view>
        
        <view class="qrcode-container">
          <image class="qrcode" :src="financeQrCode || '/static/qrcode.png'" mode="aspectFit"></image>
        </view>
        
        <view class="hint-text">请微信扫码</view>
        <view class="hint-text">添加企业微信咨询</view>
      </view>
      
      <view class="confirm-btn" @tap="onConfirm">确定</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'recharge',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      financeQrCode: ''
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.getFinanceQrCode();
      }
    }
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm() {
      this.$emit('confirm');
    },
    getFinanceQrCode() {
      // 从本地存储获取财务二维码
      this.financeQrCode = uni.getStorageSync('financeQrCode') || '';
    }
  }
}
</script>

<style lang="scss" scoped>
.recharge-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.popup-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1002;
  display: flex;
  flex-direction: column;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  padding: 30rpx 0;
}

.popup-body {
  padding: 20rpx 40rpx 40rpx;
}

.notice-box {
  background-color: #FFF9F0;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #FF9900;
  line-height: 1.6;
}

.account-section {
  margin-bottom: 30rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.account-number {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}

.qrcode {
  width: 300rpx;
  height: 300rpx;
}

.hint-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  line-height: 1.6;
}

.confirm-btn {
  height: 90rpx;
  background-color: #40E0D0;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}
</style> 