<template>
  <view class="customer-service-popup" v-if="show">
    <view class="popup-mask" @tap="onCancel"></view>
    <view class="popup-content">
      <view class="popup-header">
        <view class="popup-title">在线咨询</view>
        <view class="popup-close" @tap="onCancel">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="popup-body">
        <view class="qrcode-container">
          <image class="qrcode" :src="seekQrCode || '/static/qrcode.png'" mode="aspectFit"></image>
        </view>
        
        <view class="hint-text">请微信扫码</view>
        <view class="hint-text">添加企业微信咨询</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'customer-service',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      seekQrCode: ''
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.getSeekQrCode();
      }
    }
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    getSeekQrCode() {
      // 从本地存储获取客服二维码
      this.seekQrCode = uni.getStorageSync('seekQrCode') || '';
    }
  }
}
</script>

<style lang="scss" scoped>
.customer-service-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.popup-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1002;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  position: relative;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  padding: 8rpx;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  font-weight: 300;
}

.popup-body {
  padding: 0 40rpx 40rpx;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}

.qrcode {
  width: 300rpx;
  height: 300rpx;
}

.hint-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  line-height: 1.6;
}
</style>