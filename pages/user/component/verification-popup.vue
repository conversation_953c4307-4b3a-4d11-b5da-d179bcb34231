<template>
  <view class="verification-popup" v-if="show" @tap.stop="">
    <view class="popup-mask" @tap="onCancel"></view>
    <view class="popup-content" @tap.stop="">
      <view class="popup-title">提示</view>
      <view class="popup-message">请完善个人信息并完成实名认证后继续</view>
      <view class="popup-buttons">
        <view class="cancel-btn" @tap="onCancel">取消</view>
        <view class="confirm-btn" @tap="onConfirm">去完善</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'verification-popup',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm() {
      this.$emit('confirm');
    }
  }
}
</script>

<style lang="scss" scoped>
.verification-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  visibility: visible;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.popup-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  z-index: 1002;
  display: flex;
  flex-direction: column;
  padding: 30rpx 0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
  margin-bottom: 20rpx;
}

.popup-message {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  padding: 0 40rpx;
  margin-bottom: 30rpx;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
}

.cancel-btn, .confirm-btn {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cancel-btn {
  border: 1px solid #40E0D0;
  color: #40E0D0;
  background-color: #fff;
}

.confirm-btn {
  background-color: #40E0D0;
  color: #fff;
}
</style> 