<template>
	<view class="address-page">
		<!-- 地址列表 -->
		<view class="address-list">
			<view class="address-item" v-for="(item, index) in addressList" :key="index" @click="selectAddress(item)">
				<view class="address-info">
					<view class="address-header">
						<view class="name-phone">
							<text class="name">{{item.name}}</text>
							<text class="phone">{{item.phone}}</text>
						</view>
						<view class="default-tag" v-if="item.isDefault">默认</view>
					</view>
					<view class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</view>
				</view>
				
				<!-- 底部操作区域 -->
				<view class="address-actions-row">
					<!-- 设为默认地址 -->
					<view class="default-setting" @click.stop="setDefault(item)">
						<view class="checkbox" :class="{'checked': item.isDefault}">
							<view class="check-icon" v-if="item.isDefault"></view>
						</view>
						<text>设为默认</text>
					</view>
					
					<!-- 编辑和删除按钮 -->
					<view class="action-buttons">
						<view class="action-btn edit-btn" @click.stop="editAddress(item)">
							<text>编辑</text>
						</view>
						<view class="action-btn delete-btn" @click.stop="deleteAddress(item)">
							<text>删除</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部添加按钮 -->
		<view class="add-btn-container">
			<view class="add-btn" @click="navigateToAdd">
				<text>添加新地址</text>
			</view>
		</view>
		
		<!-- 删除确认弹窗 -->
		<delete-address-popup 
			:show="showDeleteConfirm" 
			@confirm="confirmDelete"
			@cancel="cancelDelete">
		</delete-address-popup>
	</view>
</template>

<script>
import DeleteAddressPopup from './component/delete-address-popup.vue';

export default {
	components: {
		DeleteAddressPopup
	},
	data() {
		return {
			addressList: [
				{
					id: 1,
					name: '张三',
					phone: '13800138000',
					province: '广东省',
					city: '深圳市',
					district: '南山区',
					address: '科技园南区8栋101',
					isDefault: true
				},
				{
					id: 2,
					name: '李四',
					phone: '13800138001',
					province: '广东省',
					city: '广州市',
					district: '天河区',
					address: '天河路385号',
					isDefault: false
				},
				{
					id: 3,
					name: '王五',
					phone: '13800138002',
					province: '广东省',
					city: '东莞市',
					district: '松山湖区',
					address: '新城大道1号',
					isDefault: false
				}
			],
			showDeleteConfirm: false,
			currentDeleteId: null
		}
	},
	onLoad() {
		// 设置原生导航栏标题
		uni.setNavigationBarTitle({
			title: '收货地址'
		});
		
		// 从服务器获取地址列表
		this.getAddressList();
	},
	methods: {
		// 获取地址列表
		getAddressList() {
			// TODO: 从服务器获取地址列表
			// 这里使用模拟数据，实际应用中应该调用API
		},
		
		// 选择地址
		selectAddress(item) {
			// 选择地址后返回上一页
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2];
			if (prevPage) {
				// 将选中的地址传回上一页
				prevPage.$vm.selectedAddress = item;
				uni.navigateBack();
			}
		},
		
		// 编辑地址
		editAddress(item) {
			uni.navigateTo({
				url: `/pages/user/address-add?id=${item.id}`
			});
		},
		
		// 删除地址
		deleteAddress(item) {
			this.currentDeleteId = item.id;
			this.showDeleteConfirm = true;
		},
		
		// 取消删除
		cancelDelete() {
			this.showDeleteConfirm = false;
			this.currentDeleteId = null;
		},
		
		// 确认删除
		confirmDelete() {
			if (this.currentDeleteId) {
				// TODO: 调用API删除地址
				// 模拟删除
				this.addressList = this.addressList.filter(item => item.id !== this.currentDeleteId);
				this.currentDeleteId = null;
				
				uni.showToast({
					title: '删除成功',
					icon: 'none'
				});
				
				this.showDeleteConfirm = false;
			}
		},
		
		// 设置默认地址
		setDefault(item) {
			if (item.isDefault) return;
			
			// 将所有地址设为非默认
			this.addressList.forEach(address => {
				address.isDefault = false;
			});
			
			// 设置当前地址为默认
			item.isDefault = true;
			
			// TODO: 调用API更新默认地址
			uni.showToast({
				title: '已设为默认地址',
				icon: 'none'
			});
		},
		
		// 跳转到添加地址页
		navigateToAdd() {
			uni.navigateTo({
				url: '/pages/user/address-add'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.address-page {
	background-color: #F5F7FA;
	min-height: 100vh;
	padding: 20rpx;
	box-sizing: border-box;
	padding-bottom: 160rpx;
}

.address-list {
	.address-item {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
	}
	
	.address-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
		
		.name-phone {
			display: flex;
			align-items: center;
			
			.name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				margin-right: 20rpx;
			}
			
			.phone {
				font-size: 28rpx;
				color: #666;
			}
		}
		
		.default-tag {
			background-color: #40E0D0;
			color: #FFFFFF;
			font-size: 24rpx;
			padding: 4rpx 16rpx;
			border-radius: 20rpx;
		}
	}
	
	.address-detail {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
		margin-bottom: 30rpx;
	}
	
	.address-actions-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1rpx solid #EEEEEE;
		padding-top: 20rpx;
	}
	
	.default-setting {
		display: flex;
		align-items: center;
		
		.checkbox {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			border: 1px solid #CCCCCC;
			margin-right: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			&.checked {
				border-color: #40E0D0;
				background-color: #40E0D0;
			}
			
			.check-icon {
				width: 20rpx;
				height: 20rpx;
				border-radius: 50%;
				background-color: #FFFFFF;
			}
		}
		
		text {
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.action-buttons {
		display: flex;
		
		.action-btn {
			padding: 10rpx 30rpx;
			border-radius: 8rpx;
			margin-left: 20rpx;
			background-color: #F5F5F5;
			
			text {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
}

.add-btn-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #FFFFFF;
	padding: 20rpx 30rpx 40rpx;
}

.add-btn {
	width: 100%;
	height: 90rpx;
	background-color: #20C4C1;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	
	text {
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
	}
}

.bottom-line {
	width: 120rpx;
	height: 8rpx;
	background-color: #333333;
	border-radius: 4rpx;
	margin-top: 20rpx;
}
</style> 