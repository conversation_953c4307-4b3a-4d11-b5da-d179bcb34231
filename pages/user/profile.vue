<template>
  <view class="profile-page">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 姓名 -->
      <view class="form-item">
        <view class="label">
          <text>姓名</text>
          <text class="required">*</text>
        </view>
        <input
          class="input"
          v-model="userInfo.name"
          placeholder="请输入真实姓名"
          placeholder-class="placeholder"
        />
      </view>

      <!-- 身份类型 -->
      <view class="form-item picker-item" @click="showIdentityPicker">
        <view class="label">
          <text>身份类型</text>
          <text class="required">*</text>
        </view>
        <view class="picker-content">
          <text class="picker-text" :class="{ 'placeholder': !userInfo.identityType }">
            {{ userInfo.identityType || '高校 老师' }}
          </text>
           <image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
        </view>
      </view>

      <!-- 所在地区 -->
      <view class="form-item picker-item" @click="showRegionPicker">
        <view class="label">
          <text>所在地区</text>
          <text class="required">*</text>
        </view>
        <view class="picker-content">
          <text class="picker-text" :class="{ 'placeholder': !userInfo.region }">
            {{ userInfo.region || '广东省深圳市南山区' }}
          </text>
           <image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
        </view>
      </view>

      <!-- 研究方向 -->
      <view class="form-item">
        <view class="label">
          <text>研究方向</text>
        </view>
        <input
          class="input"
          v-model="userInfo.research"
          placeholder="请输入"
          placeholder-class="placeholder"
        />
      </view>

      <!-- 高校/研究所 -->
      <view class="form-item picker-item" @click="showSchoolPicker">
        <view class="label">
          <text>高校/研究所</text>
          <text class="required">*</text>
        </view>
        <view class="picker-content">
          <text class="picker-text" :class="{ 'placeholder': !userInfo.school }">
            {{ userInfo.school || '请选择' }}
          </text>
           <image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
        </view>
      </view>

      <!-- 所在院系 -->
      <view class="form-item picker-item" @click="showDepartmentPicker">
        <view class="label">
          <text>所在院系</text>
          <text class="required">*</text>
        </view>
        <view class="picker-content">
          <text class="picker-text" :class="{ 'placeholder': !userInfo.department }">
            {{ userInfo.department || '请选择' }}
          </text>
           <image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="btn-container">
      <button class="save-btn" @click="handleSave">保存</button>
      <button class="next-btn" @click="handleNext">下一步</button>
    </view>

    <!-- 地区选择器 -->
    <region-picker 
      v-if="showRegionPickerFlag" 
      :value="regionValue"
      @confirm="onRegionConfirm" 
      @cancel="onRegionCancel"
    />
  </view>
</template>

<script>
import regionPicker from './components/region-picker.vue';

export default {
  components: {
    regionPicker
  },
  data() {
    return {
      userInfo: {
        name: '',
        identityType: '',
        region: '',
        research: '',
        school: '',
        department: ''
      },
      identityOptions: ['企业', '高校', '科研院所', '个人'],
      schoolOptions: ['清华大学', '北京大学', '复旦大学', '上海交通大学', '浙江大学', '中科院'],
      departmentOptions: ['计算机学院', '材料学院', '物理学院', '化学学院', '生物学院', '机械学院'],
      showRegionPickerFlag: false,
      regionValue: {
        province: '',
        city: '',
        district: ''
      }
    };
  },

  onLoad() {
    this.loadUserInfo();
  },

  methods: {
    // 显示身份类型选择器
    showIdentityPicker() {
      uni.showActionSheet({
        itemList: this.identityOptions,
        success: (res) => {
          this.userInfo.identityType = this.identityOptions[res.tapIndex];
        }
      });
    },

    // 显示地区选择器
    showRegionPicker() {
      this.showRegionPickerFlag = true;
    },

    // 显示学校选择器
    showSchoolPicker() {
      uni.showActionSheet({
        itemList: this.schoolOptions,
        success: (res) => {
          this.userInfo.school = this.schoolOptions[res.tapIndex];
        }
      });
    },

    // 显示院系选择器
    showDepartmentPicker() {
      uni.showActionSheet({
        itemList: this.departmentOptions,
        success: (res) => {
          this.userInfo.department = this.departmentOptions[res.tapIndex];
        }
      });
    },

    // 地区选择确认
    onRegionConfirm(region) {
      this.userInfo.region = `${region.province}${region.city}${region.district}`;
      this.regionValue = region;
      this.showRegionPickerFlag = false;
    },

    // 地区选择取消
    onRegionCancel() {
      this.showRegionPickerFlag = false;
    },

    // 加载用户信息
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo };
      }
    },

    // 保存
    handleSave() {
      this.saveUserInfo();
    },

    // 下一步
    handleNext() {
      if (this.validateForm()) {
        this.saveUserInfo();
        uni.navigateTo({
          url: '/pages/user/verification'
        });
      }
    },

    // 表单验证
    validateForm() {
      if (!this.userInfo.name || !this.userInfo.name.trim()) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return false;
      }

      if (!this.userInfo.identityType) {
        uni.showToast({
          title: '请选择身份类型',
          icon: 'none'
        });
        return false;
      }

      if (!this.userInfo.region) {
        uni.showToast({
          title: '请选择所在地区',
          icon: 'none'
        });
        return false;
      }

      if (!this.userInfo.school) {
        uni.showToast({
          title: '请选择高校/研究所',
          icon: 'none'
        });
        return false;
      }

      if (!this.userInfo.department) {
        uni.showToast({
          title: '请选择所在院系',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 保存用户信息
    saveUserInfo() {
      uni.setStorageSync('userInfo', this.userInfo);
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  background-color: #F5F5F5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 表单容器 */
.form-container {
  flex: 1;
  background-color: #FFFFFF;
  margin: 0;
  padding: 0;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #F0F0F0;
  background-color: #FFFFFF;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  width: 200rpx;
  font-size: 32rpx;
  color: #333333;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  font-weight: 400;
}

.required {
  color: #FF4D4F;
  margin-left: 8rpx;
  font-size: 32rpx;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  text-align: right;
  border: none;
  outline: none;
  background: transparent;

  &::placeholder {
    color: #CCCCCC;
  }
}

.placeholder {
  color: #CCCCCC !important;
}

/* 选择器项 */
.picker-item {
  cursor: pointer;
}

.picker-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.picker-text {
  font-size: 32rpx;
  color: #333333;
  margin-right: 16rpx;

  &.placeholder {
    color: #CCCCCC;
  }
}

.arrow {
		width: 32rpx;
		height: 32rpx;
		flex-shrink: 0;
	}

/* 底部按钮 */
.btn-container {
  display: flex;
  padding: 40rpx 30rpx;
  background-color: #F5F5F5;
  gap: 20rpx;
  margin-top: auto;
}

.save-btn {
  flex: 1;
  height: 88rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #40E0D0;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #40E0D0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;

  &::after {
    border: none;
  }
}

.next-btn {
  flex: 1;
  height: 88rpx;
  background-color: #40E0D0;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;

  &::after {
    border: none;
  }
}
</style>