<template>
	<view class="verification-page">
		<!-- 顶部提示 -->
		<view class="top-notice">
			<text class="notice-text">注：认证成功后无法修改，请确认您的消息填写正确</text>
		</view>
		
		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 姓名 -->
			<view class="form-item">
				<view class="label">
					<text>姓名</text>
					<text class="required">*</text>
				</view>
				<view class="input-wrapper">
					<input 
						class="form-input" 
						type="text" 
						v-model="formData.name" 
						placeholder="请输入真实姓名"
						placeholder-style="color: #C0C4CC;"
					/>
				</view>
			</view>
			
			<!-- 身份证号 -->
			<view class="form-item">
				<view class="label">
					<text>身份证号</text>
					<text class="required">*</text>
				</view>
				<view class="input-wrapper">
					<input 
						class="form-input" 
						type="idcard" 
						v-model="formData.idCard" 
						placeholder="请输入"
						placeholder-style="color: #C0C4CC;"
					/>
				</view>
			</view>
			
			<!-- 证件类型 -->
			<view class="form-item" @tap="showCardTypePicker = true">
				<view class="label">
					<text>证件类型</text>
					<text class="required">*</text>
				</view>
				<view class="input-wrapper">
					<text class="form-value" v-if="formData.cardType">{{formData.cardType}}</text>
					<text class="placeholder" v-else>校园一卡通</text>
				</view>
				<view class="arrow-right">
					<image src="/static/user/chevron-right.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<!-- 证件上传 -->
			<view class="upload-section">
				<view class="upload-box" @tap="chooseImage">
					<image v-if="formData.cardImage" :src="formData.cardImage" mode="aspectFit" class="preview-image"></image>
					<view v-else class="upload-btn">点击上传</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="submit-btn" @tap="submitVerification">立即认证</view>
		</view>
		
		<!-- 证件类型选择器 -->
		<u-picker 
			:show="showCardTypePicker" 
			:columns="[cardTypeOptions]" 
			@confirm="onCardTypeConfirm" 
			@cancel="showCardTypePicker = false"
		></u-picker>
	</view>
</template>

<script>
export default {
	components: {
	},
	data() {
		return {
			formData: {
				name: '',
				idCard: '',
				cardType: '校园一卡通',
				cardImage: ''
			},
			showCardTypePicker: false,
			cardTypeOptions: [
				{
					text: '校园一卡通',
					value: '校园一卡通'
				},
				{
					text: '学生证',
					value: '学生证'
				},
				{
					text: '工作证',
					value: '工作证'
				}
			]
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 1, // 最多可以选择的图片张数
				sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 从相册选择或使用相机拍照
				success: (res) => {
					// 返回选定照片的本地文件路径列表
					this.formData.cardImage = res.tempFilePaths[0];
				}
			});
		},
		
		// 证件类型选择确认
		onCardTypeConfirm(e) {
			this.formData.cardType = e.value[0].text;
			this.showCardTypePicker = false;
		},
		
		// 提交认证
		submitVerification() {
			// 表单验证
			if (!this.formData.name) {
				uni.showToast({
					title: '请输入姓名',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.idCard) {
				uni.showToast({
					title: '请输入身份证号',
					icon: 'none'
				});
				return;
			}
			
			// 身份证号格式验证
			const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
			if (!idCardReg.test(this.formData.idCard)) {
				uni.showToast({
					title: '身份证号格式不正确',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.cardImage) {
				uni.showToast({
					title: '请上传证件照片',
					icon: 'none'
				});
				return;
			}
			
			// 提交信息
			uni.showLoading({
				title: '提交中...'
			});
			
			// 模拟API调用
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '提交成功，等待审核',
					icon: 'success'
				});
				
				// 延迟返回
				setTimeout(() => {
					uni.navigateBack({
						delta: 1 // 返回一级，回到个人中心页面
					});
				}, 1500);
			}, 1500);
		}
	}
}
</script>

<style lang="scss" scoped>
.verification-page {
	background-color: #F5F7FA;
	min-height: 100vh;
}

.navbar {
	display: flex;
	align-items: center;
	height: 88rpx;
	background-color: #FFFFFF;
	position: relative;
	
	.navbar-left {
		position: absolute;
		left: 30rpx;
		height: 100%;
		display: flex;
		align-items: center;
		
		.back-icon {
			width: 44rpx;
			height: 44rpx;
		}
	}
	
	.navbar-title {
		flex: 1;
		text-align: center;
		font-size: 36rpx;
		font-weight: 500;
		color: #333333;
	}
	
	.navbar-right {
		position: absolute;
		right: 30rpx;
		height: 100%;
		display: flex;
		align-items: center;
		
		.more-icon {
			width: 44rpx;
			height: 44rpx;
		}
	}
}

.top-notice {
	padding: 20rpx 30rpx;
	
	.notice-text {
		font-size: 28rpx;
		color: #FF9500;
	}
}

.form-container {
	background-color: #FFFFFF;
	margin-top: 20rpx;
	padding: 0 30rpx;
}

.form-item {
	display: flex;
	align-items: center;
	min-height: 110rpx;
	border-bottom: 1px solid #F0F0F0;
	
	&:last-child {
		border-bottom: none;
	}
	
	.label {
		width: 200rpx;
		font-size: 32rpx;
		color: #333333;
		display: flex;
		align-items: center;
		
		.required {
			color: #FF5252;
			margin-left: 6rpx;
		}
	}
	
	.input-wrapper {
		flex: 1;
		padding-right: 20rpx;
		
		.form-input {
			width: 100%;
			height: 80rpx;
			font-size: 32rpx;
			color: #333333;
		}
		
		.form-value {
			font-size: 32rpx;
			color: #333333;
		}
		
		.placeholder {
			font-size: 32rpx;
			color: #C0C4CC;
		}
	}
	
	.arrow-right {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		image {
			width: 32rpx;
			height: 32rpx;
		}
	}
}

.upload-section {
	padding: 30rpx 0;
	
	.upload-box {
		width: 200rpx;
		height: 200rpx;
		position: relative;
		border-radius: 8rpx;
		background-color: #F5F7FA;
		
		.preview-image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		
		.upload-btn {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #40E0D0;
			border: 1px solid #40E0D0;
			border-radius: 8rpx;
			background-color: #fff;
		}
	}
}

.bottom-buttons {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	background-color: #FFFFFF;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.submit-btn {
		height: 90rpx;
		border-radius: 45rpx;
		background-color: #40E0D0;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
	}
}
</style> 