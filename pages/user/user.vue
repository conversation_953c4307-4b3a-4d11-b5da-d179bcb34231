<template>
	<view class="user-page">
		<!-- 状态栏高度占位 -->
		<u-status-bar></u-status-bar>
		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="user-info">
				<view class="avatar-container" @tap="chooseAvatar">
					<image class="avatar" :src="userInfo.avatar ? userInfo.avatar : '/static/logo.png'"
						@error="onAvatarError"></image>
					<view class="camera-icon">
						<lk-svg src="/static/svg/camera.svg" width="24rpx" height="24rpx"></lk-svg>
					</view>
				</view>
				<view class="user-details">
					<view class="user-name-row">
						<text class="user-name">{{ userInfo.name || '' }}</text>
						<view v-if="userInfo.identityAuthenticationId == 0" class="verification-tag">未认证</view>
					</view>
					<text class="user-phone">{{ userInfo.phoneNumber || '' }}</text>
					<view v-if="userInfo.roleType === 6 || userInfo.roleType === 7 || userInfo.roleType === 8"
						class="user-level">
						<lk-svg src="/static/svg/starRating.svg" width="128rpx" height="36rpx"></lk-svg>
						<lk-svg src="/static/svg/question.svg" class="info-icon" width="36rpx" height="36rpx"></lk-svg>
					</view>
				</view>
				<view v-if="userInfo.isAdmin" class="switch-btn" @tap="openRoleSelector">切换</view>
			</view>
			<!-- 用户卡片中的团队信息 - 只在用户角色显示 -->
			<view class="team-info" v-if="userRole === 'user'">
				<text class="team-hint">团队相关问题及额度充值问题请联系负责人</text>
				<text class="contact-btn" @tap="showContactInfo">点击联系</text>
			</view>
		</view>

		<scroll-view class="content" scroll-y>
			<!-- 订单信息卡片 - 只在用户角色显示 -->
			<view class="order-section" v-if="userRole === 'user'">
				<view class="card-header">
					<text class="card-title">我的订单</text>
					<view class="view-all" @tap="navToAllOrders">
						<text>查看全部</text>
						<lk-svg src="/static/svg/lookAll.svg" width="36rpx" height="36rpx"></lk-svg>
					</view>
				</view>
				<view class="order-icons">
					<view class="order-item" @tap="navToOrderStatus('pending')">
						<view class="order-icon-wrapper">
							<lk-svg src="/static/svg/pendingApproval.svg" width="56rpx" height="56rpx"></lk-svg>
						</view>
						<text>待审核</text>
					</view>
					<view class="order-item" @tap="navToOrderStatus('confirm')">
						<view class="order-icon-wrapper">
							<lk-svg src="/static/svg/toBeConfirmed.svg" width="56rpx" height="56rpx"></lk-svg>
						</view>
						<text>待确认</text>
					</view>
					<view class="order-item" @tap="navToOrderStatus('experiment')">
						<view class="order-icon-wrapper">
							<lk-svg src="/static/svg/toBeTested.svg" width="56rpx" height="56rpx"></lk-svg>
							<view class="badge" v-if="experimentCount > 0">{{ experimentCount }}</view>
						</view>
						<text>待实验</text>
					</view>
					<view class="order-item" @tap="navToOrderStatus('payment')">
						<view class="order-icon-wrapper">
							<lk-svg src="/static/svg/pendingSettlement.svg" width="56rpx" height="56rpx"></lk-svg>
						</view>
						<text>待结算</text>
					</view>
				</view>
			</view>

			<!-- 个人账户卡片 - 只在用户角色显示 -->
			<view class="account-card" v-if="userRole === 'user'">
				<view class="account-header">
					<view class="account-icon">
						<lk-svg src="/static/svg/personalAccount.svg" width="48rpx" height="48rpx"></lk-svg>
					</view>
					<text class="account-title">个人账户</text>
				</view>

				<view class="account-content-wrapper">
					<view class="account-content">
						<view class="account-label">余额</view>
						<view class="balance-amount">
							<text class="amount">{{ getUserInfo.accountBalance || '0.00' }}</text>
							<text class="currency">元</text>
						</view>
					</view>
					<view class="recharge-btn" @tap="showRecharge">充值</view>
				</view>
			</view>

			<!-- 团队账户卡片 - 只在用户角色显示，遍历所有团队 -->

			<template v-if="userRole === 'user'">
				<view class="account-card" v-for="(team, teamIndex) in userTeams" :key="teamIndex">
					<view class="account-header">
						<view class="account-icon">
							<lk-svg src="/static/svg/personalAccount.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="account-title">{{ team.name || '团队账户' }}</text>
					</view>

					<view class="team-info-rows">
						<view class="team-info-row">
							<text class="info-label">团队管理员：</text>
							<text class="info-value">{{ team.adminName || team.name || '' }}</text>
						</view>
						<view class="team-info-row">
							<text class="info-label">团队成员：</text>
							<text class="info-value link" @tap="showTeamMembers(team, teamIndex)">查看团队成员</text>
						</view>
						<view class="team-info-row">
							<text class="info-label">团队折扣：</text>
							<text class="info-value">{{ team.discount ? (team.discount * 10).toFixed(0) + ' 折'
								: '无折扣' }}</text>
						</view>
					</view>

					<view class="team-balance-cards">
						<view class="team-balance-card">
							<view class="team-balance-title">团队余额</view>
							<view class="team-balance-amount">
								<text class="amount">{{ team.accountBalance || '0.00' }}</text>
								<text class="currency">元</text>
							</view>
							<view class="team-recharge-btn" @tap="showRecharge">充值</view>
						</view>

						<view class="team-balance-card">
							<view class="team-balance-title">团队额度</view>
							<view class="team-balance-amount">
								<text class="amount">{{ team.accountCreditLimit || '0.00' }}</text>
								<text class="currency">元</text>
							</view>
							<view class="credit-used">
								<text>已用信用额度</text>
								<text>{{ team.usedCreditLimit || '0.00' }}</text>
							</view>
							<view class="team-apply-btn" @tap="showCreditApply">申请额度</view>
						</view>
					</view>
				</view>
			</template>

			<!-- 菜单项列表 -->
			<view class="menu-list">
				<!-- 用户角色菜单 -->
				<template v-if="userRole === 'user'">
					<view class="menu-item" @tap="navigateTo('/pages/user/profile')">
						<view class="menu-icon">
							<lk-svg src="/static/svg/userInfo.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="menu-text">个人信息</text>
						<image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
					</view>

					<view class="menu-item" @tap="navigateTo('/pages/user/verification')">
						<view class="menu-icon">
							<lk-svg src="/static/svg/realNameAuth.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="menu-text">实名认证</text>
						<image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
					</view>

					<view class="menu-item" @tap="navigateTo('/pages/setup/us')">
						<view class="menu-icon">
							<lk-svg src="/static/svg/hotel.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="menu-text">关于我们</text>
						<image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
					</view>

					<view class="menu-item" @tap="navigateTo('/pages/user/address')">
						<view class="menu-icon">
							<lk-svg src="/static/svg/address.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="menu-text">收货地址</text>
						<image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
					</view>
				</template>

				<!-- 管理员角色菜单 - 只保留个人信息和实名认证 -->
				<template v-else>
					<view class="menu-item" @tap="navigateTo('/pages/home/<USER>')">
						<view class="menu-icon">
							<lk-svg src="/static/svg/userInfo.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="menu-text">个人信息</text>
						<image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
					</view>

					<view class="menu-item" @tap="navigateTo('/pages/user/verification')">
						<view class="menu-icon">
							<lk-svg src="/static/svg/realNameAuth.svg" width="48rpx" height="48rpx"></lk-svg>
						</view>
						<text class="menu-text">实名认证</text>
						<image src="/static/user/chevron-right.png" mode="aspectFit" class="arrow"></image>
					</view>
				</template>
			</view>

			<!-- 退出按钮 -->
			<view class="logout-btn" :class="{ 'admin-logout-btn': userRole === 'admin' }" @tap="handleLogout">退出登录
			</view>
		</scroll-view>

		<!-- 在线客服悬浮按钮 -->
		<view class="floating-chat-btn" @tap="openCustomerService" v-if="userRole === 'user'">
			<lk-svg src="/static/svg/message.svg" width="48rpx" height="48rpx"></lk-svg>
			<text>在线咨询</text>
		</view>

		<!-- 底部TabBar -->
		<custom-tab-bar :selected="userRole === 'admin' ? 2 : 4" />

		<!-- 角色选择器 -->
		<u-picker :show="showRolePicker" :columns="[roleList]" keyName="label" :defaultIndex="[currentRoleIndex]"
			@confirm="onRoleConfirm" @cancel="showRolePicker = false"></u-picker>

		<!-- 团队成员弹框 -->
		<view class="team-members-popup" v-if="showTeamMembersPopup">
			<view class="popup-mask" @tap="hideTeamMembers"></view>
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">团队成员</text>
					<view class="popup-close" @tap="hideTeamMembers">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="popup-subtitle">
					修改请联系负责人
				</view>
				<view class="members-list">
					<view v-if="teamMembers && teamMembers.length > 0">
						<view class="member-item" v-for="(member, index) in teamMembers" :key="index">
							<text class="member-name">{{ member.name }}</text>
							<text class="member-phone">{{ member.phone }}</text>
						</view>
					</view>
					<view v-else class="no-members">
						<text class="no-members-text">暂无团队成员</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 申请额度弹框 -->
		<lk-credit-apply :show="showCreditApplyPopup" @cancel="hideCreditApply"
			@confirm="hideCreditApply"></lk-credit-apply>

		<!-- 充值弹框 -->
		<lk-recharge :show="showRechargePopup" @cancel="hideRecharge" @confirm="hideRecharge"></lk-recharge>

		<!-- 实名认证提示弹框 -->
		<verification-popup :show="showVerificationPopup" @cancel="hideVerificationPopup"
			@confirm="goToVerification"></verification-popup>

		<!-- 在线咨询弹框 -->
		<customer-service :show="showCustomerServicePopup" @cancel="hideCustomerService"></customer-service>

		<!-- #ifdef MP-WEIXIN -->
		<lk-mp-privacy></lk-mp-privacy>
		<!-- #endif -->
	</view>
</template>

<script>
import api from "@/api/modify"
import userApi from "@/api/user"
import customTabBar from '@/components/custom-tab-bar/custom-tab-bar.vue';
import loadIcons from '@/components/svgIcon/data.js';
import lkCreditApply from './component/credit-apply.vue';
import lkRecharge from './component/recharge.vue';
import verificationPopup from './component/verification-popup.vue';
import customerService from './component/customer-service.vue';
import { mapState } from "vuex";
import { handle401Error, is401Error } from '@/common/utils/auth.js';

export default {
	components: {
		customTabBar,
		lkCreditApply,
		lkRecharge,
		verificationPopup,
		customerService
	},
	data() {
		return {
			avatar: '',
			errorAvatar: 'error',
			username: '',
			userInfo: {},
			pendingCount: 0, // 待审核数量
			confirmCount: 0, // 待确认数量
			experimentCount: 5, // 待实验数量
			icons: null,
			pageStyle: {
				backgroundColor: '#f0f5ff',
			},
			showRolePicker: false, // 控制角色选择器的显示
			roleList: [
				{ label: '管理员', value: 'admin' },
				{ label: '用户', value: 'user' }
			],
			showTeamMembersPopup: false, // 控制团队成员弹框显示
			teamMembers: [], // 改为空数组，从接口获取
			showCreditApplyPopup: false, // 控制申请额度弹框显示
			showRechargePopup: false, // 控制充值弹框显示
			showVerificationPopup: false, // 控制实名认证提示弹框显示
			showCustomerServicePopup: false, // 控制在线咨询弹框显示
			isVerified: true, // 用户是否已实名认证
			fileList: [], // 用于u-upload组件
			currentTeam: null, // 当前团队信息
			getUserInfo: {}, // 是否获取用户信息
		}
	},
	async created() {
		this.icons = await loadIcons;
		// 在created生命周期中也直接显示弹框
		this.showVerificationPopup = this.userInfo.identityAuthenticationId == 0 ? true : false;
	},
	onBackPress() {
		// #ifdef APP-PLUS
		plus.android.runtimeMainActivity().moveTaskToBack(false);
		return true
		// #endif
	},
	onShow() {
		if (!this.loginValid) {
			return
		}
		const userInfo = this.$store.state.userInfo
		this.username = userInfo.username
		this.userInfo = userInfo

		// 检查用户是否已实名认证
		this.checkVerificationStatus()

		// 获取用户信息
		this.fetchUserInfo()

		// 获取系统设置
		this.fetchSystemSettings()
	},
	computed: {
		...mapState(["userInfo", "userRole"]),
		loginValid() {
			return !!this.$store.state.token
		},
		// 当前选中的角色
		selectedRole: {
			get() {
				return this.userRole || 'user';
			},
			set(val) {
				// 不需要在这里处理，在onRoleConfirm中已经提交到Vuex了
			}
		},
		// 当前角色在选择器中的索引
		currentRoleIndex() {
			const currentRole = this.userRole || 'user';
			return this.roleList.findIndex(role => role.value === currentRole);
		},
		// 用户的所有团队
		userTeams() {
			// 确保返回数组
			let teams = [];
			if (this.getUserInfo.teams) {
				if (Array.isArray(this.getUserInfo.teams)) {
					teams = this.getUserInfo.teams;
				} else {
					// 如果teams不是数组，可能是对象，尝试转换
					teams = [this.getUserInfo.teams];
				}
			}

			return teams;
		}
	},
	methods: {
		onAvatarError() {
			this.errorAvatar = this.avatar
		},

		// 选择头像
		chooseAvatar() {
			// 检查平台权限
			// #ifdef APP-PLUS
			// 检查相机和相册权限
			const checkPermissions = () => {
				return new Promise((resolve, reject) => {
					plus.android.requestPermissions(
						['android.permission.CAMERA', 'android.permission.READ_EXTERNAL_STORAGE'],
						(result) => {
							resolve(result)
						},
						(error) => {
							reject(error)
						}
					)
				})
			}

			// APP平台先检查权限
			checkPermissions().then(() => {
				this.doChooseImage()
			}).catch((error) => {
				uni.showModal({
					title: '权限提示',
					content: '需要相机和相册权限才能选择头像，请在设置中开启',
					confirmText: '去设置',
					success: (res) => {
						if (res.confirm) {
							plus.runtime.openURL('app-settings:')
						}
					}
				})
			})
			// #endif

			// #ifdef MP-WEIXIN
			// 微信小程序直接选择图片
			this.doChooseImage()
			// #endif

			// #ifdef H5 || MP-ALIPAY
			// 其他平台直接选择图片
			this.doChooseImage()
			// #endif
		},

		// 执行图片选择
		doChooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'], // 压缩图片
				sourceType: ['album', 'camera'], // 相册和相机
				success: (res) => {

					if (!res.tempFilePaths || res.tempFilePaths.length === 0) {
						uni.showToast({
							title: '未获取到图片',
							icon: 'none'
						})
						return
					}

					const tempFilePath = res.tempFilePaths[0]

					// 检查文件是否存在
					// #ifdef APP-PLUS
					plus.io.resolveLocalFileSystemURL(tempFilePath,
						(entry) => {
							this.processSelectedImage(tempFilePath)
						},
						(error) => {
							uni.showToast({
								title: '图片文件异常',
								icon: 'none'
							})
						}
					)
					// #endif

					// #ifdef H5 || MP-WEIXIN || MP-ALIPAY
					this.processSelectedImage(tempFilePath)
					// #endif
				},
				fail: (error) => {
					// 根据不同的错误类型给出不同的提示
					let errorMsg = '选择图片失败'
					if (error.errMsg) {
						const errMsg = error.errMsg.toLowerCase()
						if (errMsg.includes('cancel')) {
							// 用户取消，不显示错误提示
							return
						} else if (errMsg.includes('permission') || errMsg.includes('auth')) {
							errorMsg = '请授权访问相册和相机'
						} else if (errMsg.includes('system') || errMsg.includes('fail')) {
							errorMsg = '系统错误，请重试'
						} else {
							errorMsg = `选择失败: ${error.errMsg}`
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 3000
					})
				}
			})
		},

		// 处理微信小程序隐私授权
		handleWeixinPrivacy() {
			// 检查是否支持隐私接口
			if (wx.requirePrivacyAuthorize) {
				wx.requirePrivacyAuthorize({
					success: () => {
						this.doChooseImage()
					},
					fail: (res) => {
						if (res.errMsg.includes('requirePrivacyAuthorize:fail api scope is not declared')) {
							uni.showToast({
								title: '请先同意隐私协议',
								icon: 'none'
							})
						} else {
							uni.showToast({
								title: '授权失败，请重试',
								icon: 'none'
							})
						}
					}
				})
			} else {
				// 不支持隐私接口的版本，直接选择图片
				this.doChooseImage()
			}
		},

		// 处理选中的图片
		processSelectedImage(tempFilePath) {
			// 模拟afterRead事件的数据结构
			const event = {
				file: {
					url: tempFilePath,
					name: 'avatar.jpg'
				}
			}

			// 调用上传处理方法
			this.afterRead(event)
		},

		// 头像上传后的回调
		async afterRead(event) {
			// 显示上传中提示
			uni.showLoading({
				title: '上传中...'
			});

			try {
				let file = event.file;
				const envcfg = require('@/common/config/index.js').default;

				// 使用uni.uploadFile上传文件
				const uploadResult = await new Promise((resolve, reject) => {
					uni.uploadFile({
						url: `${envcfg.baseUrl}/instrument/system/file/public/upload`,
						header: {
							Authorization: uni.getStorageSync("token")
						},
						filePath: file.url,
						name: "file",
						success: (res) => {
							try {
								let result = JSON.parse(res.data);
								if (res.statusCode === 200 && (result.code === 200 || result.success)) {
									resolve(result);
								} else {
									reject(new Error(result.message || '上传失败'));
								}
							} catch (parseError) {
								reject(new Error('响应解析失败'));
							}
						},
						fail: (error) => {
							reject(new Error('网络上传失败'));
						}
					});
				});

				// 获取上传后的文件URL
				const avatarUrl = uploadResult.data?.fileUrl || uploadResult.data?.uploadVirtualPath || uploadResult.data?.url;

				if (!avatarUrl) {
					throw new Error('未获取到文件URL');
				}

				// 更新用户头像
				await this.updateUserAvatar(avatarUrl);

				uni.hideLoading();
				uni.showToast({
					title: '头像上传成功',
					icon: 'success'
				});

			} catch (error) {
				uni.hideLoading();
				uni.showToast({
					title: error.message || '上传失败，请重试',
					icon: 'none'
				});
			}
		},

		// 更新用户头像
		async updateUserAvatar(avatarUrl) {
			try {
				const result = await api.updateUser({
					avatar: avatarUrl,
					name: this.userInfo.name || ''
				});

				if (result.code === 200 || result.success) {
					// 更新成功，更新本地用户信息
					const updatedUserInfo = {
						...this.userInfo,
						avatar: avatarUrl
					};

					// 更新Vuex中的用户信息
					this.$store.commit('SET_USERINFO', updatedUserInfo);

					// 更新本地数据
					this.userInfo = updatedUserInfo;

					uni.showToast({
						title: '头像更新成功',
						icon: 'success'
					});
				} else {
					throw new Error(result.message || '更新失败');
				}
			} catch (error) {
				uni.showToast({
					title: error.message || '更新失败',
					icon: 'none'
				});
			}
		},
		showContactInfo() {
			uni.showActionSheet({
				itemList: ['拨打 0769-89136118'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 用户点击了拨打电话
						this.makePhoneCall('0769-89136118');
					}
				},
				fail: (res) => {
				}
			});
		},

		// 拨打电话
		makePhoneCall(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber: phoneNumber,
				success: () => {
				},
				fail: (error) => {
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none'
					});
				}
			});
		},
		navToAllOrders() {
			uni.navigateTo({
				url: '/pages/orders/orderList'
			})
		},
		navToOrderStatus(status) {
			// 状态映射：将用户页面的状态名称映射到订单列表页面的状态值
			const statusMap = {
				'pending': 1,    // 待审核
				'confirm': 2,    // 待确认  
				'experiment': 3, // 待实验
				'payment': 5     // 待结算
			};

			const statusValue = statusMap[status];
			if (statusValue) {
				uni.navigateTo({
					url: `/pages/orders/orderList?status=${statusValue}`
				});
			} else {
				// 如果状态不匹配，跳转到全部订单
				uni.navigateTo({
					url: '/pages/orders/orderList'
				});
			}
		},
		// 显示团队成员弹框
		showTeamMembers(team, teamIndex) {
			// 处理团队成员数据
			if (team.teamMembers && team.teamMembers.length > 0) {
				this.teamMembers = team.teamMembers.map(member => ({
					name: member.name || member.userName || '',
					phone: member.phoneNumber || member.phone || ''
				}));
			} else {
				// 如果没有团队成员，设置为空数组
				this.teamMembers = [];
			}

			this.showTeamMembersPopup = true;
		},
		// 隐藏团队成员弹框
		hideTeamMembers() {
			this.showTeamMembersPopup = false;
		},
		navigateTo(url) {
			uni.navigateTo({
				url
			})
		},
		handleLogout() {
			uni.showModal({
				title: '提示',
				content: '确认退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						userApi.logout().then(res => {
							uni.showToast({
								title: '退出成功',
								icon: 'none'
							});
							// 使用统一的清理和跳转逻辑
							this.$store.commit('CLEAR_STORAGE');
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}).catch(error => {
							// 如果是网络错误或其他错误，也检查是否为401相关
							if (error.code && is401Error(error.code)) {
								handle401Error('登录已过期，请重新登录');
							} else {
								uni.showToast({
									title: '请求失败，请稍后再试',
									icon: 'none'
								});
							}
						});
					}
				}
			});
		},
		openCustomerService() {
			this.showCustomerServicePopup = true;
		},

		// 隐藏在线咨询弹框
		hideCustomerService() {
			this.showCustomerServicePopup = false;
		},
		// 打开角色选择器
		openRoleSelector() {
			this.showRolePicker = true;
		},

		// 角色选择确认
		async onRoleConfirm(e) {
			const role = e.value[0].value;
			const roleName = e.value[0].label;
			const currentRole = this.userRole || 'user';

			// 关闭选择器
			this.showRolePicker = false;

			// 如果选择的角色和当前角色相同，只关闭弹框，不做任何操作
			if (role === currentRole) {
				return;
			}

			// 显示加载提示
			uni.showLoading({
				title: '切换中...'
			});

			try {
				// 调用对应的API接口
				let result;
				if (role === 'admin') {
					result = await userApi.toAdmin();
				} else {
					result = await userApi.toUser();
				}
				// 检查API调用结果
				if (result && result.userId) {
					this.$store.commit('SET_USER_ROLE', role);
					this.$store.commit('SET_USERINFO', result)
					this.$store.commit('SET_TOKEN', result.accessToken)
					uni.hideLoading();
					uni.showToast({
						title: `已切换为${roleName}`,
						icon: 'success'
					});

					// 根据角色切换到对应的首页
					setTimeout(() => {
						if (role === 'admin') {
							uni.reLaunch({ // 使用reLaunch而不是switchTab，确保完全刷新页面
								url: '/pages/admin/orderManage'
							});
						} else {
							uni.reLaunch({ // 使用reLaunch而不是switchTab，确保完全刷新页面
								url: '/pages/home/<USER>'
							});
						}
					}, 500);
				} else {
					uni.showToast({
						title: '切换失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				uni.showToast({
					title: error.message || '切换失败，请稍后再试',
					icon: 'none'
				});
			}
		},
		// 显示申请额度弹框
		showCreditApply() {
			this.showCreditApplyPopup = true;
		},

		// 隐藏申请额度弹框
		hideCreditApply() {
			this.showCreditApplyPopup = false;
		},

		// 显示充值弹框
		showRecharge() {
			this.showRechargePopup = true;
		},

		// 隐藏充值弹框
		hideRecharge() {
			this.showRechargePopup = false;
		},

		// 检查用户认证状态
		checkVerificationStatus() {
			// 直接显示认证提示弹窗，不做条件判断
			this.showVerificationPopup = this.userInfo.identityAuthenticationId == 0 ? true : false;
		},

		// 关闭认证提示弹窗
		hideVerificationPopup() {
			this.showVerificationPopup = false;
		},

		// 前往实名认证页面
		goToVerification() {
			this.hideVerificationPopup();
			uni.navigateTo({
				url: '/pages/user/profile'
			});
		},

		// 获取用户信息
		async fetchUserInfo() {
			try {
				const result = await userApi.getUserInfo();

				if (result) {
					const userData = result.data || result;

					// 使用 Vue.set 确保响应式更新
					this.$set(this, 'getUserInfo', userData);

					// 处理团队信息
					if (userData.teams && userData.teams.length > 0) {
						this.currentTeam = userData.teams[0]; // 取第一个团队作为当前团队

						// 处理团队成员信息
						if (this.currentTeam.teamMembers && this.currentTeam.teamMembers.length > 0) {
							this.teamMembers = this.currentTeam.teamMembers.map(member => ({
								name: member.name || member.userName || '',
								phone: member.phoneNumber || member.phone || ''
							}));
						}
					} else {
					}

					// 强制触发计算属性更新
					this.$forceUpdate();

					// 检查用户是否已实名认证
					this.checkVerificationStatus();
				} else {
				}
			} catch (error) {
				uni.showToast({
					title: '获取用户信息失败',
					icon: 'none'
				});
			}
		},

		// 获取系统设置
		async fetchSystemSettings() {
			try {
				const aiApi = require('@/api/index.js').default;
				await aiApi.fetchAndStoreSystemSettings();
			} catch (error) {
				console.error('获取系统设置失败:', error);
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.user-page {
	background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/9ca8697e-ebdb-4054-907a-b5cc28181938.png');
	background-size: 100% 50%;
	background-repeat: no-repeat;
	min-height: 100%;
	height: 100%;
	background-color: #f5f8fb;
	padding-top: 14px;
	display: flex;
	flex-direction: column;
}

.status-time {
	position: absolute;
	top: 30rpx;
	left: 30rpx;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.top-bar {
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 30rpx;

	.actions {
		display: flex;
		gap: 24rpx;
	}

	.action-icon {
		width: 44rpx;
		height: 44rpx;
	}
}

.user-card {
	margin: 0 30rpx;
	border-radius: 24rpx;
	padding: 0 0 24rpx 10rpx;
	margin-top: 20rpx;
	box-sizing: border-box;
	width: calc(100% - 60rpx);
}

.user-info {
	display: flex;
	align-items: center;
	position: relative;
	flex-wrap: wrap;
	width: 100%;
}

.avatar-container {
	position: relative;
	margin-right: 30rpx;

	.avatar {
		width: 148rpx;
		height: 148rpx;
		border-radius: 50%;
		border: 4rpx solid #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.camera-icon {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 48rpx;
		height: 47rpx;
		background-color: #4b4b4c;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
	}
}

.user-details {
	flex: 1;
	min-width: 0;
	padding-right: 80rpx;
	display: flex;
	flex-direction: column;
}

.user-name-row {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.user-name {
	color: #000;
	font-family: "PingFang SC";
	font-weight: 500;
	font-size: 32rpx;
	margin-right: 16rpx;
	min-width: 180rpx;
}

.verification-tag {
	background-color: #40E0D0;
	color: var(--text-icon-font-wh-1100, #FFF);
	text-align: center;
	font-family: "PingFang SC";
	font-style: normal;
	font-weight: 500;
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 30rpx;
}

.switch-btn {
	font-size: 26rpx;
	color: rgba(0, 0, 0, 0.90);
	background: #F3F3F3;
	padding: 10rpx 30rpx;
	border-radius: 30rpx;
	position: absolute;
	right: 0;
	top: 60rpx;
}

.user-phone {
	color: var(---text-3, #86909C);
	font-family: "PingFang SC";
	font-size: 28rpx;
	font-style: normal;
	margin-bottom: 12rpx;
	margin-top: 12rpx;
}

.user-level {
	display: flex;
	align-items: center;
	margin-top: 4rpx;

	.info-icon {
		margin-left: 12rpx;
	}
}

.team-info {
	margin-top: 42rpx;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	width: 100%;

	.team-hint {
		font-size: 28rpx;
		color: #86909C;
		font-weight: 400;
		flex: 1;
	}

	.contact-btn {
		font-size: 28rpx;
		color: #1CABB3;
		font-weight: 400;
	}
}

.content {
	flex: 1;
	padding: 20rpx 30rpx;
	padding-bottom: 300rpx;
	/* 增加底部padding，为固定定位的退出按钮和TabBar腾出空间 */
	box-sizing: border-box;
	width: 100%;
}

.order-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background: linear-gradient(90deg, #40E0D0 0%, #20B2AA 100%);
	box-sizing: border-box;
	width: 100%;

	.card-title {
		color: #FFF;
		font-size: 30rpx;
		font-weight: 600;
	}

	.view-all {
		display: flex;
		align-items: center;
		border-radius: 30rpx;
		padding: 8rpx 20rpx;
		flex-shrink: 0;

		text {
			color: #FFF;
			font-size: 26rpx;
			font-weight: 400;
			margin-right: 16rpx;
		}

		image {
			width: 24rpx;
			height: 24rpx;
			filter: brightness(10);
		}
	}
}

.order-icons {
	display: flex;
	justify-content: space-around;
	padding: 32rpx 10rpx;
	flex-wrap: wrap;
	width: 100%;
	box-sizing: border-box;

	.order-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 0 10rpx;
		min-width: 80rpx;
		max-width: 120rpx;
		margin-bottom: 20rpx;

		.order-icon-wrapper {
			position: relative;
			margin-bottom: 16rpx;
			width: 70rpx;
			height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 50rpx;
				height: 50rpx;
			}

			.badge {
				position: absolute;
				top: -10rpx;
				right: -12rpx;
				background-color: #FF5252;
				color: white;
				font-size: 20rpx;
				border-radius: 50%;
				min-width: 32rpx;
				height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 6rpx;
				box-sizing: border-box;
			}
		}

		text {
			font-size: 24rpx;
			color: rgba(0, 0, 0, 0.90);
			font-weight: 400;
			text-align: center;
			width: 100%;
		}
	}
}

.account-card {
	background-color: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.account-header {
	display: flex;
	margin-bottom: 20rpx;

	.account-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 16rpx;
	}

	.account-title {
		color: #101217;
		font-family: "PingFang SC";
		font-size: 32rpx;
		font-style: normal;
		font-weight: 600;
		margin-left: 12rpx;
	}
}

.account-content-wrapper {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border-radius: 16rpx;
	background-color: #F9F9F9;
	box-sizing: border-box;
	width: 100%;
}

.account-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.account-balance-section {
	display: flex;
	align-items: baseline;
	margin-bottom: 16rpx;
	flex-wrap: wrap;
}

.account-label {
	color: #1CABB3;
	font-size: 24rpx;
	font-style: normal;
	font-weight: 500;
	margin-left: 16rpx;
	background-color: #FFF;
	padding: 4rpx 16rpx;
	border-radius: 200rpx;
	width: 50rpx;
}

.balance-amount {
	display: flex;
	align-items: baseline;
	margin: 16rpx 0;
	flex-wrap: wrap;

	.amount {
		color: #000;
		font-size: 48rpx;
		font-weight: 600;
	}

	.currency {
		color: #4E5969;
		font-size: 24rpx;
		font-weight: 400;
		margin-left: 18rpx;
	}
}

.temp-balance {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	flex-wrap: wrap;

	text {
		color: #4E5969;
		font-size: 32rpx;
		font-weight: 400;
		margin-right: 8rpx;
	}
}

.recharge-btn {
	background-color: #40E0D0;
	color: white;
	font-size: 28rpx;
	padding: 10rpx 36rpx;
	border-radius: 40rpx;
	box-shadow: 0 4rpx 12rpx rgba(64, 224, 208, 0.3);
}

.team-info-rows {
	padding-bottom: 20rpx;
}

.team-info-row {
	display: flex;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
	width: 100%;
	display: flex;

	.info-label {
		font-size: 32rpx;
		color: #4E5969;
		width: 180rpx;
		flex-shrink: 0;
		font-weight: 400;
		flex: 1;
	}

	.info-value {
		font-size: 32rpx;
		color: #101217;
		min-width: 0;
		word-break: break-all;

		&.link {
			color: #1CABB3;
		}
	}
}

.team-balance-cards {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
	width: 100%;
}

.team-balance-card {
	flex: 1;
	min-width: calc(50% - 20rpx);
	max-width: 100%;
	background-color: #f9f9f9;
	border-radius: 16rpx;
	padding: 20rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.team-balance-title {
	width: fit-content;
	font-size: 26rpx;
	color: #1CABB3;
	margin-bottom: 24rpx;
	font-weight: 500;
	text-align: center;
	border-radius: 200rpx;
	padding: 10rpx 20rpx;
	background-color: #FFF;
}

.team-balance-amount {
	display: flex;
	align-items: baseline;
	margin-bottom: 12rpx;
	justify-content: center;
	flex-wrap: wrap;

	.amount {
		font-size: 48rpx;
		font-weight: 600;
		color: #000;
		word-break: break-all;
	}

	.currency {
		font-size: 28rpx;
		color: #4E5969;
		margin-left: 8rpx;
	}
}

.small-balance {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8rpx;
	flex-wrap: wrap;

	text {
		font-size: 28rpx;
		color: #4E5969;
		margin-right: 4rpx;
		word-break: break-all;
		font-weight: 400;
	}

	image {
		width: 40rpx;
		height: 40rpx;
		flex-shrink: 0;
	}
}

.small-amount {
	font-size: 26rpx;
	color: #4E5969;
	margin-bottom: 16rpx;
	display: block;
	font-weight: 400;
	text-align: center;
}

.credit-used {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 16rpx;

	text {
		font-size: 22rpx;
		color: #999999;

		&:last-child {
			color: #666666;
			font-size: 26rpx;
		}
	}
}

.team-recharge-btn,
.team-apply-btn {
	text-align: center;
	font-size: 28rpx;
	font-weight: 600;
	padding: 10rpx 0;
	border-radius: 200rpx;
	width: 90%;
	margin-top: auto;
}

.team-recharge-btn {
	color: white;
	background-color: #26D1CB;
}

.team-apply-btn {
	color: #00C0D4;
	background-color: #fff;
}

.menu-list {
	background-color: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	flex-wrap: wrap;
	box-sizing: border-box;
	width: 100%;

	&:last-child {
		border-bottom: none;
	}

	.menu-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
		flex-shrink: 0;

		image {
			width: 42rpx;
			height: 42rpx;
		}
	}

	.menu-text {
		flex: 1;
		font-size: 32rpx;
		color: rgba(0, 0, 0, 0.90);
		min-width: 0;
		font-weight: 400;
	}

	.arrow {
		width: 32rpx;
		height: 32rpx;
		flex-shrink: 0;
	}
}

.logout-btn {
	background-color: #ffffff;
	text-align: center;
	padding: 24rpx 40rpx;
	color: #26D1CB;
	font-size: 32rpx;
	font-weight: 600;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.admin-logout-btn {
	bottom: 210rpx;
	/* 管理员模式下的按钮位置 */
}

.floating-chat-btn {
	position: fixed;
	right: 18rpx;
	top: 50%;
	transform: translateY(-50%);
	background-color: #26D1CB;
	width: 90rpx;
	height: 90rpx;
	border-radius: 28rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 6rpx 28rpx 4rpx rgba(0, 0, 0, 0.05), 0px 16rpx 20rpx 2rpx rgba(0, 0, 0, 0.06), 0px 10rpx 10rpx -6rpx rgba(0, 0, 0, 0.10);
	padding: 10rpx;
	z-index: 999;

	image {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 4rpx;
	}

	text {
		font-size: 20rpx;
		color: #ffffff;
		font-weight: 500;
	}
}

@media screen and (max-width: 375px) {
	.team-balance-cards {
		flex-direction: column;
	}

	.team-balance-card {
		width: 100%;
		max-width: 100%;
	}
}

/* 团队成员弹框样式 */
.team-members-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1001;
}

.popup-content {
	width: 80%;
	max-width: 600rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	position: relative;
	z-index: 1002;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0;
	position: relative;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.popup-close {
	position: absolute;
	right: 30rpx;
	top: 30rpx;
}

.close-icon {
	font-size: 40rpx;
	color: #999;
	font-weight: 300;
}

.popup-subtitle {
	text-align: center;
	font-size: 28rpx;
	color: #999;
	padding-bottom: 20rpx;
}

.members-list {
	padding: 0 20rpx;
}

.member-item {
	display: flex;
	justify-content: space-between;
	padding: 30rpx 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
	border-bottom: none;
}

.member-name {
	font-size: 30rpx;
	color: #333;
}

.member-phone {
	font-size: 30rpx;
	color: #333;
}

.no-members {
	padding: 40rpx 20rpx;
	text-align: center;
}

.no-members-text {
	font-size: 28rpx;
	color: #999;
}

.admin-stats {
	display: flex;
	flex-wrap: wrap;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 16rpx;
	gap: 20rpx;
}

.admin-stat-item {
	flex: 1;
	min-width: calc(50% - 20rpx);
	max-width: 100%;
	padding: 20rpx;
	background-color: #ffffff;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-label {
	font-size: 28rpx;
	color: #4E5969;
	margin-bottom: 10rpx;
}

.stat-value {
	font-size: 40rpx;
	color: #101217;
	font-weight: 600;
}
</style>