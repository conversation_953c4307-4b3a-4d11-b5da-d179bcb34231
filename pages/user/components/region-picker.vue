<template>
	<view class="region-picker" v-if="show">
		<view class="mask" @tap="cancel"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="cancel" @tap="cancel">取消</text>
				<text class="title">选择地区</text>
				<text class="confirm" @tap="confirm">确定</text>
			</view>

			<view class="picker-body">
				<!-- 省市区选项卡 -->
				<view class="tabs">
					<view class="tab-item" :class="{ 'active': currentTab === 0 }" @tap="switchTab(0)">
						<text>{{ selectedRegion.province || '请选择' }}</text>
					</view>
					<view class="tab-item" :class="{ 'active': currentTab === 1, 'disabled': !selectedRegion.province }"
						@tap="selectedRegion.province && switchTab(1)">
						<text>{{ selectedRegion.city || '请选择' }}</text>
					</view>
					<view class="tab-item" :class="{ 'active': currentTab === 2, 'disabled': !selectedRegion.city }"
						@tap="selectedRegion.city && switchTab(2)">
						<text>{{ selectedRegion.district || '请选择' }}</text>
					</view>
				</view>

				<!-- 地址列表 -->
				<scroll-view class="region-list" scroll-y :scroll-into-view="scrollIntoView">
					<!-- 字母索引 -->
					<view class="alphabet">
						<view class="alphabet-item" v-for="letter in alphabet" :key="letter"
							@tap="scrollToLetter(letter)">
							{{ letter }}
						</view>
					</view>

					<!-- 地址项 -->
					<view class="region-content">
						<view class="region-group" v-for="(group, letter) in currentRegionGroup" :key="letter"
							:id="'letter-' + letter">
							<view class="group-title">{{ letter }}</view>
							<view class="region-item" v-for="(item, index) in group" :key="index"
								@tap="selectRegion(item)">
								<text>{{ item.name }}</text>
								<view class="selected-icon" v-if="isSelected(item)">
									<lk-svg src="/static/svg/check.svg" width="36rpx" height="36rpx"></lk-svg>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import regionData from './region-data.js';

export default {
	props: {
		value: {
			type: Object,
			default: () => ({
				province: '',
				city: '',
				district: ''
			})
		}
	},
	data() {
		return {
			show: true,
			currentTab: 0,
			selectedRegion: {
				province: '',
				city: '',
				district: ''
			},
			alphabet: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'W', 'X', 'Y', 'Z'],
			provinceList: [],
			cityList: [],
			districtList: [],
			scrollIntoView: ''
		}
	},
	computed: {
		currentRegionGroup() {
			let list = [];

			switch (this.currentTab) {
				case 0:
					list = this.provinceList;
					break;
				case 1:
					list = this.cityList;
					break;
				case 2:
					list = this.districtList;
					break;
			}

			// 按字母分组
			const groups = {};
			this.alphabet.forEach(letter => {
				groups[letter] = [];
			});

			list.forEach(item => {
				const firstLetter = item.firstLetter.toUpperCase();
				if (groups[firstLetter]) {
					groups[firstLetter].push(item);
				}
			});

			return groups;
		}
	},
	created() {
		// 初始化省份数据
		this.provinceList = regionData.provinces.map(item => ({
			...item,
			firstLetter: item.pinyin ? item.pinyin.charAt(0).toUpperCase() : 'A'
		}));

		// 如果有默认值，则初始化
		if (this.value && this.value.province) {
			this.selectedRegion = { ...this.value };
			this.updateCityList();
			this.updateDistrictList();
		}
	},
	methods: {
		// 切换选项卡
		switchTab(index) {
			this.currentTab = index;

			// 延迟滚动到已选项
			setTimeout(() => {
				if (index === 0 && this.selectedRegion.province) {
					const province = this.provinceList.find(item => item.name === this.selectedRegion.province);
					if (province) {
						this.scrollToLetter(province.firstLetter);
					}
				} else if (index === 1 && this.selectedRegion.city) {
					const city = this.cityList.find(item => item.name === this.selectedRegion.city);
					if (city) {
						this.scrollToLetter(city.firstLetter);
					}
				} else if (index === 2 && this.selectedRegion.district) {
					const district = this.districtList.find(item => item.name === this.selectedRegion.district);
					if (district) {
						this.scrollToLetter(district.firstLetter);
					}
				}
			}, 100);
		},

		// 选择地区
		selectRegion(item) {
			switch (this.currentTab) {
				case 0:
					// 选择省份
					if (this.selectedRegion.province !== item.name) {
						this.selectedRegion.province = item.name;
						this.selectedRegion.city = '';
						this.selectedRegion.district = '';
						this.updateCityList();
						// 自动切换到市级选择
						this.switchTab(1);
					}
					break;
				case 1:
					// 选择城市
					if (this.selectedRegion.city !== item.name) {
						this.selectedRegion.city = item.name;
						this.selectedRegion.district = '';
						this.updateDistrictList();
						// 自动切换到区级选择
						this.switchTab(2);
					}
					break;
				case 2:
					// 选择区域
					this.selectedRegion.district = item.name;
					// 选择完区级后自动确认
					this.confirm();
					break;
			}
		},

		// 判断当前项是否选中
		isSelected(item) {
			switch (this.currentTab) {
				case 0:
					return this.selectedRegion.province === item.name;
				case 1:
					return this.selectedRegion.city === item.name;
				case 2:
					return this.selectedRegion.district === item.name;
			}
			return false;
		},

		// 更新城市列表
		updateCityList() {
			if (!this.selectedRegion.province) {
				this.cityList = [];
				return;
			}

			const province = regionData.provinces.find(item => item.name === this.selectedRegion.province);
			if (province) {
				this.cityList = province.cities.map(item => ({
					...item,
					firstLetter: item.pinyin ? item.pinyin.charAt(0).toUpperCase() : 'A'
				}));
			} else {
				this.cityList = [];
			}
		},

		// 更新区域列表
		updateDistrictList() {
			if (!this.selectedRegion.province || !this.selectedRegion.city) {
				this.districtList = [];
				return;
			}

			const province = regionData.provinces.find(item => item.name === this.selectedRegion.province);
			if (province) {
				const city = province.cities.find(item => item.name === this.selectedRegion.city);
				if (city) {
					this.districtList = city.districts.map(item => ({
						...item,
						firstLetter: item.pinyin ? item.pinyin.charAt(0).toUpperCase() : 'A'
					}));
				} else {
					this.districtList = [];
				}
			} else {
				this.districtList = [];
			}
		},

		// 滚动到指定字母
		scrollToLetter(letter) {
			this.scrollIntoView = 'letter-' + letter;
		},

		// 取消选择
		cancel() {
			this.$emit('cancel');
			this.show = false;
		},

		// 确认选择
		confirm() {
			if (this.selectedRegion.province && this.selectedRegion.city && this.selectedRegion.district) {
				this.$emit('confirm', { ...this.selectedRegion });
				this.show = false;
			} else {
				uni.showToast({
					title: '请完成地区选择',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.region-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
}

.mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1001;
}

.picker-content {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #FFFFFF;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	z-index: 1002;
	height: 80vh;
	display: flex;
	flex-direction: column;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

	.cancel {
		font-size: 30rpx;
		color: #999999;
		padding: 10rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
	}

	.confirm {
		font-size: 30rpx;
		color: #40E0D0;
		font-weight: 500;
		padding: 10rpx;
	}
}

.picker-body {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.tabs {
	display: flex;
	border-bottom: 1rpx solid #EEEEEE;
	padding: 0 30rpx;

	.tab-item {
		padding: 20rpx 0;
		margin-right: 30rpx;
		position: relative;
		font-size: 28rpx;
		color: #999999;

		&.active {
			color: #40E0D0;
			font-weight: 500;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				height: 4rpx;
				background-color: #40E0D0;
				border-radius: 2rpx;
			}
		}

		&.disabled {
			color: #CCCCCC;
		}
	}
}

.region-list {
	flex: 1;
	position: relative;
	padding: 0 30rpx;
}

.alphabet {
	position: absolute;
	right: 10rpx;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;

	.alphabet-item {
		padding: 6rpx 10rpx;
		font-size: 24rpx;
		color: #40E0D0;
		text-align: center;
	}
}

.region-content {
	padding: 20rpx 0;
	padding-right: 50rpx;
}

.region-group {
	margin-bottom: 30rpx;

	.group-title {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 10rpx;
	}
}

.region-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #F5F5F5;

	text {
		font-size: 30rpx;
		color: #333333;
	}

	.selected-icon {
		color: #40E0D0;
	}
}
</style>