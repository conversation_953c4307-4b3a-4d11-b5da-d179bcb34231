<template>
	<view class="address-add-page">
		<form>
			<!-- 表单项 -->
			<view class="form-group">
				<view class="form-item">
					<view class="label">收货人</view>
					<input type="text" v-model="addressData.name" placeholder="请输入收货人姓名" class="input" maxlength="20"/>
				</view>
				
				<view class="form-item">
					<view class="label">手机号码</view>
					<input type="number" v-model="addressData.phone" placeholder="请输入手机号码" class="input" maxlength="11"/>
				</view>
				
				<view class="form-item" @click="openRegionPicker">
					<view class="label">所在地区</view>
					<view class="value" :class="{'placeholder': !addressData.regionText}">
						{{ addressData.regionText || '请选择所在地区' }}
						<lk-svg src="/static/svg/chevron-right.svg" width="36rpx" height="36rpx"></lk-svg>
					</view>
				</view>
				
				<view class="form-item textarea-item">
					<view class="label">详细地址</view>
					<textarea 
						v-model="addressData.address" 
						placeholder="请输入详细地址，如街道、楼牌号等" 
						class="textarea"
						maxlength="100"
					></textarea>
				</view>
				
				<view class="form-item switch-item">
					<view class="label">设为默认地址</view>
					<switch 
						:checked="addressData.isDefault" 
						@change="switchDefaultChange" 
						color="#40E0D0" 
						class="switch"
					/>
				</view>
			</view>
		</form>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="cancel-btn" @click="goBack">
				<text>取消</text>
			</view>
			<view class="save-btn" @click="saveAddress">
				<text>保存</text>
			</view>
		</view>
		
		<!-- 地区选择器组件 -->
		<region-picker
			v-if="showRegionPicker"
			@confirm="confirmRegion"
			@cancel="cancelRegion"
		></region-picker>
	</view>
</template>

<script>
import RegionPicker from './components/region-picker.vue';

export default {
	components: {
		RegionPicker
	},
	data() {
		return {
			addressData: {
				id: '',
				name: '',
				phone: '',
				province: '',
				city: '',
				district: '',
				address: '',
				isDefault: false,
				regionText: ''
			},
			showRegionPicker: false,
			isEdit: false
		}
	},
	onLoad(options) {
		// 设置原生导航栏标题
		uni.setNavigationBarTitle({
			title: options.id ? '编辑收货地址' : '添加收货地址'
		});
		
		// 如果有id参数，则是编辑模式
		if (options.id) {
			this.isEdit = true;
			this.addressData.id = options.id;
			this.getAddressDetail(options.id);
		}
	},
	methods: {
		// 获取地址详情
		getAddressDetail(id) {
			// TODO: 从服务器获取地址详情
			// 这里模拟获取详情
			const addressList = [
				{
					id: 1,
					name: '张三',
					phone: '13800138000',
					province: '广东省',
					city: '深圳市',
					district: '南山区',
					address: '科技园南区8栋101',
					isDefault: true
				},
				{
					id: 2,
					name: '李四',
					phone: '13800138001',
					province: '广东省',
					city: '广州市',
					district: '天河区',
					address: '天河路385号',
					isDefault: false
				}
			];
			
			const address = addressList.find(item => item.id == id);
			if (address) {
				this.addressData = { 
					...address,
					regionText: `${address.province} ${address.city} ${address.district}`
				};
			}
		},
		
		// 打开地区选择器
		openRegionPicker() {
			this.showRegionPicker = true;
		},
		
		// 确认选择地区
		confirmRegion(region) {
			this.addressData.province = region.province;
			this.addressData.city = region.city;
			this.addressData.district = region.district;
			this.addressData.regionText = `${region.province} ${region.city} ${region.district}`;
			this.showRegionPicker = false;
		},
		
		// 取消选择地区
		cancelRegion() {
			this.showRegionPicker = false;
		},
		
		// 切换默认地址状态
		switchDefaultChange(e) {
			this.addressData.isDefault = e.detail.value;
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 保存地址
		saveAddress() {
			// 表单验证
			if (!this.addressData.name) {
				uni.showToast({
					title: '请输入收货人姓名',
					icon: 'none'
				});
				return;
			}
			
			if (!this.addressData.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				});
				return;
			}
			
			if (!/^1\d{10}$/.test(this.addressData.phone)) {
				uni.showToast({
					title: '手机号码格式不正确',
					icon: 'none'
				});
				return;
			}
			
			if (!this.addressData.province || !this.addressData.city || !this.addressData.district) {
				uni.showToast({
					title: '请选择所在地区',
					icon: 'none'
				});
				return;
			}
			
			if (!this.addressData.address) {
				uni.showToast({
					title: '请输入详细地址',
					icon: 'none'
				});
				return;
			}
			
			// TODO: 保存地址到服务器
			// 模拟保存操作
			setTimeout(() => {
				uni.showToast({
					title: this.isEdit ? '修改成功' : '添加成功',
					icon: 'success'
				});
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}, 500);
		}
	}
}
</script>

<style lang="scss" scoped>
.address-add-page {
	background-color: #F5F7FA;
	min-height: 100vh;
	padding-bottom: 180rpx;
	box-sizing: border-box;
}

.form-group {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin: 20rpx;
	padding: 0 30rpx;
}

.form-item {
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid #EEEEEE;
	padding: 30rpx 0;
	
	&:last-child {
		border-bottom: none;
	}
	
	.label {
		width: 180rpx;
		font-size: 30rpx;
		color: #333;
		flex-shrink: 0;
	}
	
	.input {
		flex: 1;
		height: 60rpx;
		font-size: 30rpx;
		color: #333;
	}
	
	.value {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		&.placeholder {
			color: #999;
		}
	}
}

.textarea-item {
	align-items: flex-start;
	padding: 30rpx 0;
	
	.textarea {
		flex: 1;
		height: 160rpx;
		font-size: 30rpx;
		color: #333;
		line-height: 1.5;
	}
}

.switch-item {
	display: flex;
	justify-content: space-between;
}

.bottom-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background-color: #FFFFFF;
	padding: 20rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn, .save-btn {
	flex: 1;
	height: 90rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.cancel-btn {
	background-color: #FFFFFF;
	border: 1rpx solid #DDDDDD;
	margin-right: 20rpx;
	
	text {
		color: #333333;
		font-size: 32rpx;
	}
}

.save-btn {
	background-color: #20C4C1;
	
	text {
		color: #FFFFFF;
		font-size: 32rpx;
	}
}
</style> 