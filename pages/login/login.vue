<template>
	<view class="login" :style="[getBgImage()]">

		<view class="background">

			<u-navbar @leftClick="back(false)" bgColor="transparent">
				<template #left>
					<view class="back-icon">
						<u-icon name="arrow-left" size="40rpx" color="#585858"></u-icon>
					</view>
				</template>
			</u-navbar>

			<view class="login-top"></view>

			<view class="header-wrap">
				<view class="content">
					<view class="logo-container">
						<image class="logo" src="/static/login/logo.svg" mode="aspectFit"></image>
					</view>
					<view class="title">松山湖材料实验室</view>
				</view>
			</view>
			<view class="login-area">

				<!-- 手机登录 -->
				<view v-if="state == 'phone' || state == 'account'" class="login-phone">
					<!-- 手机号 -->
					<view class="login-phone-item">
						<u-input placeholder="请输入手机号" v-model="phone" border="none"
							placeholderClass="login-input-placeholder" color="#333" maxlength="11" type="number"
							:adjustPosition="false"
							:customStyle="{ backgroundColor: '#F7F8FA', borderRadius: '24rpx', padding: '16rpx 48rpx' }">
						</u-input>
					</view>

					<!-- 密码 -->
					<view v-if="usePassword && state == 'account'" class="login-phone-item">
						<u-input placeholder="请输入验证码" color="#333" v-model="password" border="none"
							placeholderClass="login-input-placeholder" type="password" :adjustPosition="false"
							:customStyle="{ backgroundColor: '#F7F8FA', borderRadius: '24rpx', padding: '16rpx 48rpx' }">
							<template slot="suffix">
								<view class="login-phone-input-btn"
									@tap.stop="navTo('/pages/resetPassword/resetPassword?phone=' + phone)">
									获取验证码</view>
							</template>
						</u-input>
					</view>

					<!-- 验证码 -->
					<view class="login-phone-item" v-else-if="state == 'phone'">
						<u-input placeholder="请输入验证码" border="none" v-model="code"
							placeholderClass="login-input-placeholder" color="#333"
							:customStyle="{ backgroundColor: '#F7F8FA', borderRadius: '24rpx', padding: '16rpx 48rpx' }"
							maxlength="6" type="number" :adjustPosition="false">
							<template slot="suffix">
								<u-code ref="uCode" @change="codeTip = $event" seconds="60" changeText="Xs后重发"
									startText="获取验证码" endText="重新获取">
								</u-code>
								<view class="login-phone-input-btn" @click="getCode">{{ codeTip }}</view>
							</template>
						</u-input>
					</view>

					<view class="login-phone-buttons">
						<u-button
							:customStyle="{ fontSize: '32rpx', height: '96rpx', borderRadius: '48rpx', background: '#26D1CB!important' }"
							:disabled="!((phone && code && state == 'phone'))" type="primary"
							@click="submit">登录</u-button>
					</view>
				</view>


				<!-- 协议 -->
				<view class="login-protocol">
					<view class="login-protocol-radio-box" @click="protocolAgreed = !protocolAgreed">
						<view class="login-protocol-radio"
							:class="[protocolAgreed ? 'login-protocol-radio-checked' : '']">
							<icon v-if="protocolAgreed" class="login-protocol-radio-icon" type="success_no_circle"
								color="#FFF" size="32rpx">
							</icon>
						</view>
					</view>
					<view class="agree-text">登录或注册即代表您同意</view>
					<view class="login-protocol-link" @click="navTo('/pages/userProtocol/userProtocol')"
						style="display: inline-block; margin-left: 10rpx;">用户协议</view>
					<view class="agree-text">和</view>
					<view class="login-protocol-link" @click="navTo('/pages/userProtocol/userProtocol')"
						style="display: inline-block; margin-left: 10rpx;">隐私政策</view>
				</view>

				<!-- 其他登录方式 -->
				<view class="other-login" v-if="showWxLogin">
					<view class="other-login-text">其他登录方式</view>
					<!-- 根据状态显示不同的微信登录按钮 -->
					<image
						v-if="!protocolAgreed || !wxPrivacyAgreed || !!wxPhoneCode || !wxPreLoginDone || !!wxPreLoginRes"
						@click="wxQuickLogin" class="wechat-icon" src="/static/common/icons/wechat.svg"
						mode="aspectFit"></image>
					<!-- 这里需要用button来支持open-type，但由于是图标，我们还是用点击事件处理 -->
					<image v-else @click="wxQuickLogin" class="wechat-icon" src="/static/common/icons/wechat.svg"
						mode="aspectFit">
					</image>
				</view>

				<u-safe-bottom></u-safe-bottom>

				<view>

					<!-- #ifdef MP-WEIXIN || ENV_DEV -->

					<lk-mp-privacy ref="privacy" type="login"></lk-mp-privacy>

					<!-- #endif -->

					<!-- 测试环境选择 -->
					<u-picker v-if="showUrlPicker" :show="true" immediate-change :columns="urlColumns"
						:defaultIndex="urlDefaultIndex" @confirm="urlConfirm"
						@cancel="showUrlPicker = false"></u-picker>

				</view>

			</view>

			<xlg-slideCode ref="slideCodeRef"></xlg-slideCode>

		</view>
		<u-popup :show="openPopup" :round="10" mode="bottom">
			<view class="status-popup">
				<view class="status-title">密码设置</view>
				<view class="status-remind">
					<text style="color: #4E5969; font-size: 28rpx;">
						为保障您的账号安全，首次登录请修改您的密码（密码 8 至 16 位，包含大小写字母和数字的组合，可以输入特殊符号）
					</text>
				</view>
				<view class="popup">
					<view class="content">
						<u-input placeholder="请输入新密码" v-model="newPassword" type="password" password maxlength="16"
							color="#4E5969" border="none"
							:customStyle="{ backgroundColor: '#F2F3F5', borderRadius: '8rpx', height: '96rpx' }">
							<template slot="prefix">
								<view style="padding-left:16rpx"></view>
							</template>
						</u-input>

					</view>
					<view class="content">
						<u-input placeholder="请输入新密码" v-model="confirmPassword" type="password" password maxlength="16"
							color="#4E5969" border="none"
							:customStyle="{ backgroundColor: '#F2F3F5', borderRadius: '8rpx', height: '96rpx' }">
							<template slot="prefix">
								<view style="padding-left:16rpx"></view>
							</template>
						</u-input>
					</view>
				</view>
				<view class="popup-btn">
					<u-button
						:customStyle="{ backgroundColor: '#F3F3F3', color: '#1D2129', fontSize: '32rpx', fontWeight: '600' }"
						@click="resetPassword">重置</u-button>
					<u-button type="primary"
						:customStyle="{ backgroundColor: '#B694FF', color: '#FFF', fontSize: '32rpx', fontWeight: '600' }"
						:disabled="!newPassword || !confirmPassword" @click="confirmModify">确认修改</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import api from '@/api/login'
import envcfg from '@/common/config/index.js'
import CryptoJS from 'crypto-js'
import { mapState } from "vuex";

export default {
	data() {
		// #ifdef (MP-WEIXIN || ENV_DEV)
		const wxLoginEnabled = true
		// #endif
		// #ifndef (MP-WEIXIN || ENV_DEV)
		const wxLoginEnabled = false
		// #endif

		return {
			usePassword: envcfg.passwordLogin, // 是否开启密码登录
			backPath: '', // 登录成功的返回路径
			protocolAgreed: false,
			isFrom401: false, // 是否是从401错误跳转过来的

			state: 'phone', // 'select'：选择界面；'phone'：手机验证码登录；'account'：账号密码登录

			// 手机登录
			phonePrivacyAgreed: false, // 手机登录用户隐私
			phone: '', // 手机事情
			password: '', // 密码
			code: '', // 验证码
			codeTip: '', // 验证码提示
			phoneSubmiting: false,

			//密码修改
			openPopup: false,
			newPassword: '',  // 新密码    
			confirmPassword: '', // 确认密码

			// 微信登录
			wxLoginEnabled: wxLoginEnabled, // 是否启用微信登录
			wxPrivacyAgreed: false, // 微信登录用户隐私
			wxPreLoginRes: null, // 微信预登录结果，已绑定的用户避免拉起手机号授权弹框
			wxPreLoginDone: false, // 是否已完成预登录，可能成功或失败（400错误）
			wxQuickSubmiting: false, // 微信一键登录中
			wxSubmiting: false,
			wxPhoneCode: '',
			wxPhoneCodeTime: 0,
			wxPhoneCodeCheckId: null,

			/////////////////////////////////////////////////////////////
			// api切换 start
			urlDefaultIndex: 0,
			showUrlPicker: false,
		}
	},
	computed: {
		...mapState(["userInfo"]),
		urlColumns() {
			return envcfg.env == 'prod' ? [] : [envcfg.baseUrlList.map(it => it.name)]
		},
		// 控制微信登录按钮显示
		showWxLogin() {
			return this.wxLoginEnabled && typeof wx !== 'undefined'
		}
	},
	onLoad(options) {
		// #ifdef MP-WEIXIN || ENV_DEV
		if (!this.wxLoginEnabled && options.back) {
			this.phonePrivacyAgreed = true
		}
		// #endif
		if (options.phone) {
			this.phone = options.phone
		}

		this.backPath = this.$store.state.loginBackPath || ''
		if (this.backPath.includes('pages/login/login')) {
			this.backPath = ''
		} else if (this.backPath.startsWith('pages/')) {
			this.backPath = '/' + this.backPath
		}
		this.$store.commit('SET_LOGIN_BACK_PATH', '')

		// 检查是否是因为401错误跳转过来的，如果是则不自动执行微信预登录
		this.isFrom401 = options.from401 === 'true' || false

		// #ifdef MP-WEIXIN
		// #ifndef ENV_DEV
		if (this.wxLoginEnabled && !this.isFrom401) {
			this.wxPreLogin()
		}
		// #endif
		// #endif

	},
	onUnload() {
		this.stopCheckPhoneCode()
	},
	mounted() {
		// #ifdef MP-WEIXIN || ENV_DEV
		if (!this.wxLoginEnabled && !this.phonePrivacyAgreed) {
			this.openPrivacy().then(() => {
				this.phonePrivacyAgreed = true
			}).catch(() => this.back())
		}
		// #endif
	},
	methods: {
		getBgImage() {
			return {
				background: `url(https://huayun-ai-obs-public.huayuntiantu.com/77538753-433b-4089-9918-fe8a8e39bcb0.png) 0px -168px /100% 100% no-repeat #26D1CB`,
			}
		},
		openPrivacy(options) {
			return this.$refs.privacy.open(options)
		},

		resetPassword() {
			this.newPassword = '';
			this.confirmPassword = '';
		},
		confirmModify() {
			if (this.newPassword !== this.confirmPassword) {
				this.$u.toast('两次输入的密码不一致');
				return;
			}

			if (this.newPassword.length < 8 || this.newPassword.length > 16) {
				this.$u.toast('密码长度应在8-16位之间');
				return;
			}

			const hasUpperCase = /[A-Z]/.test(this.newPassword);
			const hasLowerCase = /[a-z]/.test(this.newPassword);
			const hasNumber = /[0-9]/.test(this.newPassword);

			if (!hasUpperCase || !hasLowerCase || !hasNumber) {
				this.$u.toast('密码必须包含大小写字母和数字组合');
				return;
			}

			api.setFirstLoginUpdatePwd({
				password: CryptoJS.SHA256(this.newPassword).toString(CryptoJS.enc.Hex)
			}).then(() => {
				uni.hideLoading();
				this.$u.toast('密码修改成功');
				this.openPopup = false;

				this.state = 'account';
			}).catch(err => {
				uni.hideLoading();
				this.$u.toast(err.msg || '密码修改失败，请重试');
			});
		},

		back(nav = true) {
			// 如果是因为401错误跳转过来的，不允许返回，避免无限循环
			if (this.isFrom401) {
				// 401跳转过来的，提示用户需要登录
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				return
			}

			if (!nav && this.wxLoginEnabled && (this.state == 'phone' || this.state == 'account')) {
				this.state = 'select'
			} else if (this.backPath && envcfg.pageWhiteList.includes(this.backPath.split('?')[0])) {
				this.navLaunch(this.backPath)
			} else if (getCurrentPages().length > 1) {
				this.navBack()
			} else {
				if (this?.userInfo?.menuCodes?.includes('app_center') ?? false) {
					this.navLaunch('/pages/intelligent/intelligent')
				} else {
					this.navLaunch('/pages/home/<USER>')
				}
			}
		},
		selectPhoneLogin(e) {
			if (!this.protocolAgreed) {
				this.$u.toast('请勾选同意后再登录')
				return
			}
			this.openPrivacy().then(() => {
				this.phonePrivacyAgreed = true
				this.state = 'phone'
			})
		},
		accountLogin(e) {
			if (!this.protocolAgreed) {
				this.$u.toast('请勾选同意后再登录')
				return
			}
			this.openPrivacy().then(() => {
				this.phonePrivacyAgreed = true
				this.state = 'account'
			})
		},
		// 获取验证码
		async getCode() {
			if (!this.$refs.uCode.canGetCode) {
				return
			}
			if (!this.phone || !/^1\d{10}$/.test(this.phone)) {
				this.$u.toast('请输入正确手机号码')
				return
			}

			const slideParams = await this.$refs.slideCodeRef.open()

			try {
				api.getSmsCode({
					mobile: this.phone,
					...slideParams
				}).then(() => {
					// 通知验证码组件内部开始倒计时
					this.$refs.uCode.start();
					this.$u.toast('验证码已发送')
				})
					.catch(err => {
						this.$u.toast(err.msg)
						// }
					})
			} catch (error) {
			}

		},
		async loginSuccess(userInfo, loginType) {

			uni.hideLoading();
			uni.showToast({
				message: '登录成功',
				type: 'success'
			})

			this.$store.commit('SET_USERINFO', userInfo)
			this.$store.commit('SET_TOKEN', userInfo.accessToken)
			// 设置默认角色为用户
			this.$store.commit('SET_USER_ROLE', 'user')

			this.navLaunch('/pages/home/<USER>')

			// #ifdef MP-WEIXIN
			wx.login({
				success: res => {
					if (res && res.code) {
						// api.getWxOpenId({
						//     type: 1,
						//     token: this.$store.state.token,
						//     code: res.code
						//   })
						//   .then(res => {
						//     this.$store.commit('SET_WX_OPENID', res.openid)
						//     notice.fetchCounts()
						//   })
					}
				}
			})
			// #endif

		},

		// 登录
		async submit() {
			if (!this.protocolAgreed) {
				this.$u.toast('请勾选同意后再登录')
				return
			}
			if (!this.phone || !/^1\d{10}$/.test(this.phone)) {
				this.$u.toast('请输入正确手机号码')
				return
			}
			if (this.usePassword && !this.password && this.state == 'account') {
				this.$u.toast('请输入正确密码')
				return
			}
			if (this.phoneSubmiting) {
				return
			}

			this.phoneSubmiting = true
			try {
				let res = null
				if (this.state == 'phone') {
					// 滑块校验
					uni.showLoading({ title: '正在登录...' })
					res = await api.getAuthValid({
						mobile: this.phone,
						code: this.code,
					})
				} else if (this.state == 'account') {
					uni.showLoading({ title: '正在登录...' })
					res = await api.tenantListByPassword({
						account: this.phone,
						password: CryptoJS.SHA256(this.password).toString(CryptoJS.enc.Hex),
					})
				}

				this.getLogin(res, this.state)
			} catch (err) {
				this.$u.toast('登录失败，请稍后再试')
			} finally {
				this.phoneSubmiting = false
				uni.hideLoading()
			}
		},

		startCheckPhoneCode() {
			if (!this.wxPhoneCode) {
				if (this.wxPhoneCodeCheckId) {
					clearInterval(this.wxPhoneCodeCheckId)
					this.wxPhoneCodeCheckId = null
				}
			} else if (!this.wxPhoneCodeCheckId) {
				this.wxPhoneCodeCheckId = setInterval(() => {
					if (!this.wxPhoneCode) {
						this.stopCheckPhoneCode()
					}
					if (Date.now() - this.wxPhoneCodeTime > 4 * 60 * 1000) {
						this.wxPhoneCode = ''
						this.stopCheckPhoneCode()
					}
				}, 1000)
			}
		},

		stopCheckPhoneCode() {
			if (this.wxPhoneCodeCheckId) {
				clearInterval(this.wxPhoneCodeCheckId)
				this.wxPhoneCodeCheckId = null
			}
		},

		// #ifdef MP-WEIXIN || ENV_DEV
		wxGetPhoneNumber(e) {
			this.wxGetingPhone = false
			if (!e.detail.code) {
				this.$u.toast('您可以选择手机验证码登录')
				return
			}

			this.wxPhoneCode = e.detail.code
			this.wxPhoneCodeTime = Date.now()
			this.wxLogin()
			this.startCheckPhoneCode()
		},

		wxQuickLogin() {
			if (!this.protocolAgreed) {
				this.$u.toast('请勾选同意后再登录')
				return
			}

			// 未完成预登录
			if (!this.wxPreLoginDone) {
				if (this.wxQuickSubmiting) {
					return
				}
				this.wxQuickSubmiting = true
				uni.showLoading({
					title: '正在登录'
				})
				// 先预登录，确定是否需要手机号授权组件（这个组件要收钱，省点用）
				this.wxPreLogin().then(() => {
					uni.hideLoading()
					// 重新登录
					this.wxQuickLogin()
				}).catch(() => {
					uni.hideLoading()
					this.$u.toast('登录失败，请稍候再试')
				}).finally(() => {
					this.wxQuickSubmiting = false
				})
				return
			}

			// 已预登录成功
			if (this.wxPreLoginRes) {
				this.getLogin(this.wxPreLoginRes, 'phone')
				return
			}

			// 用户未绑定微信，提示使用手机验证码登录
			this.$u.toast('该微信号未绑定，请使用手机验证码登录')

			// 可选：自动跳转到手机验证码登录页面
			setTimeout(() => {
				this.selectPhoneLogin()
			}, 1500)
		},

		// 预登录，用户点击微信快速登录时立即登录
		wxPreLogin() {
			return new Promise((resolve, reject) => {
				wx.login({
					success: res => {
						api.wxLogin({
							jsCode: res.code,
						}).then(res => {
							this.wxPreLoginRes = res
							this.wxPreLoginDone = true
							resolve()
						}).catch(err => {
							if (err?.code == 400) {
								this.wxPreLoginDone = true
								resolve()
							} else {
								reject()
							}
						})
					}
				})
			})
		},

		wxLogin() {
			if (this.wxSubmiting) {
				return
			}
			this.wxSubmiting = true
			uni.showLoading({
				title: '正在登录...'
			})
			wx.login({
				success: res => {
					api.wxLogin({
						jsCode: res.code,
						code: this.wxPhoneCode || '',
					}).then(res => {
						uni.hideLoading()
						this.wxPhoneCode = ''
						this.loginSuccess(res, 'wechat')
					}).catch(err => {
						uni.hideLoading()
						if (err.code == 400) {
							// 手机号code已被消费
							this.wxPhoneCode = ''
							// 移除toast弹框，不显示错误信息
						}
					}).finally(() => {
						this.wxSubmiting = false
					})
				},
				fail: res => {
					this.wxSubmiting = false
					uni.hideLoading()
					this.$u.toast('小程序登录失败')
				}
			})
		},

		// #endif

		openUrlPicker() {
			// 当前预生产演示发版，临时禁用2024/06/18
			return
		},
		urlConfirm(e) {
			this.showUrlPicker = false
			envcfg.setBaseUrl(envcfg.baseUrlList[e.indexs[0]].url)
			this.$u.toast(e.value[0])
		},
		getLogin(tenantUsers, loginType) {
			this.loginSuccess(tenantUsers, loginType)
		},

		// 导航方法
		navTo(url) {
			uni.navigateTo({
				url: url
			})
		},

		navLaunch(url) {
			uni.reLaunch({
				url: url
			})
		},

		navBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.auditEnvTip {
	text-align: center;
	margin-top: 34px;
	color: #333;
}

.login-phone-item {
	background-color: #ffffff;
	border-radius: 12rpx;
	height: 96rpx;
	margin-bottom: 32rpx;
	padding: 0 32rpx;
	display: flex;
	align-items: center;
}

.login {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	overflow: hidden;

	.background {
		height: 100%;
		width: 100%;
		box-sizing: border-box;
		background-repeat: no-repeat;
		background-position: top;
		background-size: 100% 520rpx;
		position: absolute;
		left: 0;
		z-index: -1;
		top: 0;
		z-index: 999;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}

	& * {
		padding: 0;
		margin: 0;
		line-height: 1em;
	}

	.header-wrap {
		padding: 380rpx 104rpx 80rpx 104rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;

		.content {
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.logo-container {
			width: 120rpx;
			height: 120rpx;
			background: white;
			border-radius: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: 32rpx;
		}

		.logo {
			width: 80rpx;
			height: 80rpx;
		}

		.title {
			font-size: 36rpx;
			font-weight: 600;
			color: white;
			text-align: center;
		}

	}


	&-select {
		display: flex;
		flex-direction: column;
		overflow: hidden;
		margin-top: 50rpx;

		::v-deep .u-button {
			margin: 20rpx 0;
			height: 100rpx;
			border-radius: 48rpx;
			font-size: 32rpx;
			font-weight: 400;
		}

	}

	&-phone {
		display: flex;
		flex-direction: column;

		.prefix_text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #1D2129;
			line-height: 40rpx;
			text-align: left;
			font-style: normal;
			padding-right: 80rpx;
			padding-left: 32rpx;
		}

		&-title {
			font-size: 32rpx;
			color: #333333;
			font-weight: bold;
		}

		&-input-btn {
			font-size: 28rpx;
			font-weight: 400;
			color: #26D1CB;
			padding: 8rpx 16rpx;
		}

		&-item {
			margin-top: 0rpx;
		}

		&-buttons {
			margin: 48rpx 0 32rpx 0;

			::v-deep .u-button {
				height: 96rpx;
				border-radius: 48rpx;
				font-size: 32rpx;
				font-weight: 600;
				margin-bottom: 0;
			}

		}

	}

	::v-deep .login-input-placeholder {
		font-size: 28rpx;
		font-weight: 400;
		color: #AEAEB2;
	}

	&-register {
		font-size: 28rpx;
		color: #6A6A6D;
		display: flex;
		// justify-content: center;

		&-link {
			color: #26D1CB;
		}
	}

	&-protocol {
		text-align: center;
		font-size: 24rpx;
		line-height: 34rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;

		&-radio-box {
			padding: 15rpx 8rpx 15rpx 0rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
		}

		&-radio {
			border: 1px solid #26D1CB;
			width: 36rpx;
			height: 36rpx;
			border-radius: 50%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;

			&-checked {
				background-color: #26D1CB;
				border: 1px solid #26D1CB;
			}

			&-icon {
				display: flex;
				flex-direction: row;
				align-items: center;
				transform: scale(0.5);
			}
		}

		.agree-text {
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;
			line-height: 34rpx;
			text-align: left;
			font-style: normal;
		}

		&-link {
			color: #26D1CB;
			line-height: 34rpx;
			font-size: 24rpx;
		}
	}

	.status-popup {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		width: 100%;
		padding: 32rpx;

		.status-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #1D2129;
			margin-bottom: 24rpx;
			text-align: center;
		}

		.status-remind {
			font-size: 28rpx;
			font-weight: 400;
			color: #4E5969;
			margin-bottom: 32rpx;
			line-height: 1.4;
			padding: 0 16rpx;
		}

		.popup {
			display: flex;
			flex-direction: column;
			width: 100%;
			margin-bottom: 32rpx;
			gap: 24rpx;

			.content {
				width: 100%;
			}
		}

		.popup-btn {
			display: flex;
			flex-direction: row;
			align-items: center;
			width: 100%;
			margin-top: 16rpx;
			margin-bottom: 16rpx;

			::v-deep .u-button {
				margin: 0 8rpx;
				flex: 1;
				padding: 24rpx 0;
				justify-content: center;
				align-items: center;
				border-radius: 100rpx;
				font-size: 32rpx;
				height: 96rpx;
			}
		}
	}

	.record {
		height: 30px;
		color: #c1c1c2;
		margin: 0 auto;
		text-align: center;
	}
}

.login-protocol {
	margin-bottom: 48rpx;
	font-size: 24rpx;
	justify-content: center;
}

.noneLogin {
	text-align: center;
	color: #585858;
	margin-top: 16rpx;
}

.login-area {
	background: white;
	padding: 64rpx 48rpx 0rpx 48rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
}

.back-icon {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #606060;

	.back-text {
		margin-left: 10rpx;
	}
}

.other-login {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 64rpx;
	margin-bottom: 32rpx;

	.other-login-text {
		font-size: 24rpx;
		color: #414141;
		margin-bottom: 32rpx;
		font-weight: 400;
	}

	.wechat-icon {
		width: 80rpx;
		height: 80rpx;
	}
}
</style>