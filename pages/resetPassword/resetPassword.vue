<template>
  <view class="reset-password" >


    <u-navbar @leftClick="navBack" bgColor="transparent">
				<template #left>
					<view class="back-icon">
						<u-icon name="arrow-left" size="40rpx" color="#585858"></u-icon>
						<text class="back-text">
							返回
						</text>
					</view>
				</template>
			</u-navbar>
    <view class="form" :rules="rules" :style="{ marginTop: `${uNavbarHeight}px`,borderTop: '1px solid #E6E6E6' ,padding: '48rpx 30rpx 0 30rpx'}">
      <view class="form-item">
        <u-input class="form-phone" placeholder="请输入手机号" v-model="phone" border="bottom" placeholderClass="placeholder"
          color="#333" maxlength="11" type="number" :adjustPosition="false" :customStyle="inputStyle" :placeholderStyle="placeholderStyle">
        </u-input>
      </view>
      <view class="form-item">
        <u-input class="form-code" placeholder="请输入验证码" border="bottom" v-model="smsCode" placeholderClass="placeholder"
          color="#333" maxlength="6" type="number" :adjustPosition="false" :customStyle="inputStyle" :placeholderStyle="placeholderStyle">
          <template slot="suffix">
            <u-code ref="uCode" @change="smsTip = $event" seconds="60" changeText="Xs后重发" startText="获取验证码"
              endText="重新获取">
            </u-code>
            <view class="form-phone-btn" @click="getSmsCode">{{ smsTip }}</view>
          </template>
        </u-input>
      </view>

      <view class="form-item">
        <u-input class="form-phone" placeholder="请输入新密码" v-model="password" border="bottom" placeholderClass="placeholder"
          color="#333" type="password" :adjustPosition="false" :customStyle="inputStyle" :placeholderStyle="placeholderStyle">
        </u-input>
      </view>

      <view class="form-item">
        <u-input class="form-phone" placeholder="请再次输入新密码" v-model="password1" border="bottom"
          placeholderClass="placeholder" color="#333" type="password" :adjustPosition="false" :customStyle="inputStyle" :placeholderStyle="placeholderStyle">
        </u-input>
      </view>
      <view class="form-item tip">
        密码需8至16位，包含大小写字母和数字的组合，可以输入特殊符号。
      </view>

    </view>

    <view class="confirm" :style="{ padding: '0 30rpx' }">
      <u-button :disabled="!(phone && smsCode && password && password1)" text="确认修改" type="primary" :loading="submiting"
        @click="submit"></u-button>
    </view>

	<xlg-slideCode  ref="slideCodeRef"></xlg-slideCode>

  </view>
</template>

<script>
  import api from '@/api/login.js'
	import crypto from '@/common/utils/crypto.js'
	import CryptoJS from 'crypto-js'

  export default {
    data() {
      return {
        phone: '',
        smsCode: '',
        password: '',
        password1: '',
        rules: {

        },
        inputStyle: {
          background: '#F9F9F9',
          height: '36px',
          borderRadius: '8px',
          border: 'none',
          fontSize: '16px',
          fontWeight: '400',
          paddingLeft: '32rpx',
          paddingRight: '32rpx',
        },
        placeholderStyle: {
          color: '#4E5969!important',
          fontWeight: '400!important',
        },
        smsTip: '',
        submiting: false,

      }
    },
    onLoad(query) {
      this.phone = query.phone || ''
    },
    methods: {
      async getSmsCode() {
        if (!this.$refs.uCode.canGetCode || !this.validatePhone()) {
          return
        }
        const slideParams = await this.$refs.slideCodeRef.open()
        try {

          await api.resetPwdSmsCode({
            mobile: this.phone,
            bizType: 3,
            ...slideParams
          })
          this.$u.toast('验证码已发送')
          this.$refs.uCode.start()

        } catch (error) {
          this.$refs.uCode.reset()
          this.getSmsCode();
        }


      },
      validatePhone() {
        if (!this.phone || !/^1\d{10}$/.test(this.phone)) {
          this.$u.toast('请输入正确手机号码')
          return false
        }
        return true
      },
      submit() {
        if (!this.validatePhone()) {
          return
        }
        if (!this.smsCode) {
          this.$u.toast('请输入验证码')
          return
        }
        if (!this.password || !this.password1) {
          this.$u.toast('请输入密码')
          return
        }
        const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/
        if (!passwordPattern.test(this.password) || !passwordPattern.test(this.password1)) {
          this.$u.toast('密码需8至16位，包含大小写字母和数字的组合，可以输入特殊符号。')
          return
        }

        if (this.password != this.password1) {
          this.$u.toast('两次输入密码不一致')
          return
        }
        if (this.submiting) {
          return
        }
        this.submiting = true
        api.resetPwd({
          mobile: this.phone,
          code: this.smsCode,
          password: CryptoJS.SHA256(this.password).toString(CryptoJS.enc.Hex),
          password1: CryptoJS.SHA256(this.password1).toString(CryptoJS.enc.Hex),
        }).then(() => {
          
          setTimeout(() => {
            uni.showToast({
              title: '修改成功',
              icon: 'none'
            }, 500)
          })

          setTimeout(() => {
            this.navBack()
          }, 1000)
        }).finally(() => {
          this.submiting = false
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .reset-password {
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: top;
    box-sizing: border-box;
    background-size: 100% 520rpx;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    .tip{
      font-size: 24rpx;
      color: #777;
      padding: 0 10rpx;
    }
    .header {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      &-tag {
        display: inline-flex;
      }

      &-title {
        display: inline-flex;
        font-size: 40rpx;
        font-weight: bold;
        color: #333;
        margin-top: 30rpx;
        padding-bottom: 24rpx;
        position: relative;
        display: inline-block;

      }

    }

    .form {

      .u-border-bottom {
        border-color: #e6e6e6 !important;
        padding: 24rpx 0 !important;
      }

      ::v-deep .placeholder {
        color: #4E5969 !important;
        font-weight: 400 !important;
      }

      ::v-deep .uni-input-input {
        font-size: 16px !important;
        font-weight: bold;
      }

      &-phone-btn {
        font-size: 30rpx;
        font-weight: 400;
        color: #1CBE83;
      }

      &-item {
        margin-top: 32rpx;
      }
    }

    .confirm {
      flex: 1;
      margin: 32rpx 0;

      ::v-deep .u-button {
        height: 80rpx;
        border-radius: 16rpx;
        font-size: 32rpx;
        font-weight: 400;
        background-color: $lq-color-primary;
      }

    }


  }
  .back-icon{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #606060;
		.back-text{
			margin-left: 10rpx;
		}	
		
	}
</style>