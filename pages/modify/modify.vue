<template>
	<view class="modify">
		<view class="modify-wrapper">
			<view class="modify-item bottom-boder">
				<view class="modify-item-title">头像</view>
				<view class="modify-item-content">
					<u-upload :fileList="fileList" @afterRead="afterRead" name="1" class="modify-item-content-icon" :maxCount="1">
						<u-avatar shape="circle" v-if="imageList" :src="imageList"></u-avatar>
						<u-avatar v-else shape="circle" style=" background: #a9c6ff;" src="../../static/avatge.png"></u-avatar>
					</u-upload>
				</view>
			</view>
			<view class="modify-item bottom-boder">
				<view class="modify-item-title">姓名</view>
				<view class="modify-item-content">
					<input type="text" v-model="username" placeholder="请输入姓名" />
				</view>
			</view>
			<!-- <view class="modify-item bottom-boder">
				<view class="modify-item-title">性别</view>
				<view class="modify-item-content">
					<u-radio-group v-model="sex">
						<u-radio :customStyle="{margin: '8px'}" activeColor="#33ABFF" shape="circle" v-for="item in sexList"
							:label="item.label" :name="item.value" :key="item.label" placeholder="请选择">
						</u-radio>
					</u-radio-group>
				</view>
			</view> -->
			<view class="modify-item bottom-boder">
				<view class="modify-item-title">手机号</view>
				<view class="modify-item-content">
					<input style="color:#aaa" type="text" v-model="phone" :disabled="true" placeholder="请输入手机号" />
				</view>
			</view>
		</view>
		<u-toast ref="uToast" />

		<view class="btn-box">
			<u-button :custom-style="{bottom: '80rpx'}" text="取消" @click="handleBack"></u-button>
			<u-button :custom-style="{bottom: '80rpx'}" :loading="submiting" @click="submit" text="确认"
				type="primary"></u-button>
		</view>
		<u-safe-bottom></u-safe-bottom>
		<!-- #ifdef MP-WEIXIN -->
		<lk-mp-privacy></lk-mp-privacy>
		<!-- #endif -->
		<u-safe-bottom></u-safe-bottom>
	</view>
</template>

<script>
	import api from "@/api/modify";
	import envcfg from '@/common/config/index.js'
	export default {
		data() {
			return {
				imageList: "",
				action: "/instrument/system/file/public/upload",
				pic: "",
				username: "",
				sex: 1,
				phone: "",
				sex: "",
				oldData: {},
				fileList: [],
				header: {},
				submiting: false,
				sexList: [{
						label: "男",
						value: 1,
					},
					{
						label: "女",
						value: 2,
					},
				],
			};
		},
		mounted() {
			let token = uni.getStorageSync("token");
			this.header = {
				"anxun-auth": token ? "bearer " + token : "",
				Authorization: "Basic YXBwOmFwcF9wYXN3b3Jk",
				Cookie: token ? "x-access-token=" + token : "",
			};
		},
		methods: {
			handleBack() {
				uni.navigateBack()
			},
			afterRead(event) {
				let file = event.file;
				uni.uploadFile({
					url: `${envcfg.baseUrl}/instrument/system/file/public/upload`,
					header: {
						Authorization: uni.getStorageSync("token")
					},
					filePath: file.url,
					name: "file",
					success: (res) => {
						let result = JSON.parse(res.data);
						if (res.statusCode == 200) {
							this.imageList = result.data.fileUrl;
							this.pic = result.data.fileName;
						}
					},
					fail: () => {},
				});
			},
			submit() {
				this.submiting = true;
				api
					.updateUser({
						avatar: this.imageList,
						username: this.username,
						// sex: this.sex
					})
					.then((res) => {
						this.$refs.uToast.show({
							message: "修改成功",
							type: "success",
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 800);
					})
					.catch((error) => {
						this.$refs.uToast.show({
							message: "服务器异常",
							type: "warning",
						});
					})
					.finally(() => {
						this.submiting = false
						uni.hideLoading()
					})
			},
		},
	};
</script>

<style>
	page {
		background-color: #f7f7f7;
		overflow: hidden;
	}
</style>
<style lang="scss" scoped>
	.bottom-boder {
		border-bottom: 2rpx solid #eeeeee;
	}

	.modify {
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		min-height: calc(100vh - 0px);

		&-wrapper {
			background: #fff;
			margin: 32rpx 32rpx 0 32rpx;
			border-radius: 24rpx;
		}

		&-item {
			margin: 0 30rpx;
			padding: 16rpx 0;
			background: #fff;
			height: 104rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-title {
				font-weight: 400;
				font-size: 32rpx;
				color: rgba(0, 0, 0, 0.9);
				font-family: PingFang SC, PingFang SC;
			}

			&-content {
				display: flex;
				align-items: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 32rpx;
				color: rgba(0, 0, 0, 0.4);

				&-icon {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
				}

				input {
					text-align: right;
					color: #333;
					font-size: 32rpx;
				}
			}
		}

		&-bottom {
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			background-color: #fff;
		}
	}

	.btn-box {
		position: fixed;
		left: 30rpx;
		right: 30rpx;
		bottom: 0rpx;
		width: auto;
		display: flex;

		::v-deep .u-button {
			height: 80rpx;
			border-radius: 16rpx;
			font-size: 30rpx;
			font-weight: 400;
			margin: 0 20rpx;

		}

		::v-deep .u-button:nth-child(1) {
			color: #1CBE83;
			background: #E8F9F2;

		}

		::v-deep .u-button:nth-child(2) {
			background:#1CBE83;
			color: #fff;

		}
	}
</style>