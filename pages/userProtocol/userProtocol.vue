<template>
  <!-- #ifndef APP-PLUS -->
  <web-view src="https://privacy.huayuntiantu.com/aiparent.html"></web-view>
  <!-- #endif -->

  <!-- #ifdef APP-PLUS -->
  <view class="privacy">
    <web-view :webview-styles="webStyle" :src="privacyUrl"></web-view>
    <view v-if="true" class="disagree">
      <u-button type="primary" @click="disagree">撤回同意</u-button>
    </view>
  </view>
  <!-- #endif -->
</template>

<script>
  export default {
    data() {
      return {
        privacyUrl: 'https://privacy.huayuntiantu.com/aiparent.html',
        webHeight: '',
        showDisagree: true
      }
    },
    computed: {
      webStyle() {
        const style = {}
        if (this.webHeight) {
          style.height = this.webHeight
        }
        return style
      }
    },
    created() {
      // #ifdef APP-PLUS
      const info = uni.getSystemInfoSync()
      if (info.platform == 'android') {
        this.privacyUrl = 'https://privacy.huayuntiantu.com/aiparent.html'
        if (plus.runtime.isAgreePrivacy()) {
          this.webHeight = info.windowHeight - uni.upx2px(140)
        }
      }
      // #endif
    },
    methods: {
      disagree() {
        plus.runtime.disagreePrivacy()
        this.$u.toast('即将退出应用')
        setTimeout(() => {
          plus.runtime.quit()
        }, 3000)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .privacy {
    display: flex;
    flex-direction: column;

    .disagree {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      height: 140rpx;
      background-color: #FFFFFF;

      ::v-deep .u-button {
        margin-top: 10rpx;
        width: 690rpx;
        height: 100rpx;
        font-size: 32rpx;
      }
    }
  }
</style>