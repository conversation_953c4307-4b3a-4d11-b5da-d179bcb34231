<template>
  <view class="search-page">
    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input-box">
        <image src="/static/svg/search.svg" class="search-icon" mode="aspectFit"></image>
        <input 
          type="text" 
          v-model="keyword" 
          placeholder="请输入仪器名称搜索" 
          class="search-input" 
          @confirm="handleSearch"
          focus
          confirm-type="search"
        />
      </view>
      <text class="cancel-btn" @click="handleCancel">取消</text>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 历史记录 -->
      <view class="history-section" v-if="searchHistory.length > 0 && !showSearchResults">
        <view class="history-header">
          <text class="history-title">历史记录</text>
          <image src="/static/svg/trash.svg" class="history-clear" @click="clearHistory" mode="aspectFit"></image>
        </view>
        <view class="history-list">
          <view
            class="history-item"
            v-for="(item, index) in searchHistory"
            :key="index"
            @click="selectHistory(item)"
          >
            <text>{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view class="search-results" v-if="showSearchResults">
        <!-- 搜索结果列表 -->
        <scroll-view scroll-y class="result-list" v-if="searchResults.length > 0">
          <view
            class="result-item"
            v-for="item in searchResults"
            :key="item.id"
            @click="handleItemClick(item)"
          >
            <image class="result-image" :src="item.picImg || '/static/none.png'" mode="aspectFill"></image>
            <view class="result-info">
              <view class="result-name">{{item.name}}</view>
              <view class="result-desc">{{item.description || '暂无描述'}}</view>
            </view>
          </view>
        </scroll-view>

        <!-- 空状态 -->
        <view class="empty-result" v-else-if="!isSearching">
          <image class="empty-icon" src="/static/common/empty.png" mode="aspectFit"></image>
          <view class="empty-text">暂无相关结果</view>
          <view class="suggestion">请尝试其他关键词搜索</view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-result" v-if="isSearching">
          <text>搜索中...</text>
        </view>
      </view>
    </view>

    <!-- 底部指示器 -->
    <view class="bottom-indicator"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: '',
      searchHistory: ['等离子', '光谱', '光刻机', '场发射电子显微镜', '电子显微镜', '原子'],
      showSearchResults: false,
      currentKeyword: '',
      searchResults: [],
      isSearching: false
    }
  },
  onLoad(options) {
    // 如果从其他页面跳转过来带有关键词
    if (options && options.searchKey) {
      this.keyword = decodeURIComponent(options.searchKey);
      this.handleSearch();
    } else if (options && options.keyword) {
      this.keyword = options.keyword;
      this.handleSearch();
    }
  },
  methods: {
    async handleSearch() {
      if (!this.keyword.trim()) return;

      // 保存搜索历史记录
      if (!this.searchHistory.includes(this.keyword)) {
        this.searchHistory.unshift(this.keyword);
        // 只保留最近的10条记录
        if (this.searchHistory.length > 10) {
          this.searchHistory.pop();
        }

        // 保存到本地存储
        uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
      }

      // 在当前页面显示搜索结果
      this.currentKeyword = this.keyword;
      this.showSearchResults = true;
      this.isSearching = true;
      this.searchResults = [];

      try {
        // 调用搜索API
        const api = require('@/api/instrument.js').default;
        const result = await api.getInstrumentPage({
          current: 1,
          size: 9999,
          name: this.keyword
        });

        console.log('搜索结果:', result);

        if (result && result.records) {
          this.searchResults = result.records;
        } else {
          this.searchResults = [];
        }

      } catch (error) {
        console.error('搜索失败:', error);
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
        this.searchResults = [];
      } finally {
        this.isSearching = false;
      }
    },
    handleCancel() {
      if (this.showSearchResults) {
        // 如果正在显示搜索结果，清除搜索返回到历史记录页面
        this.showSearchResults = false;
        this.keyword = '';
        this.currentKeyword = '';
        this.searchResults = [];
        this.isSearching = false;
      } else {
        // 否则返回上一页
        uni.navigateBack();
      }
    },
    selectHistory(item) {
      this.keyword = item;
      this.handleSearch();
    },
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定要清空所有历史记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = [];
            uni.removeStorageSync('searchHistory');
          }
        }
      });
    },
    loadSearchHistory() {
      try {
        const history = uni.getStorageSync('searchHistory');
        if (history) {
          this.searchHistory = JSON.parse(history);
        }
      } catch (e) {
        console.error('读取搜索历史记录失败', e);
      }
    },

    // 处理搜索结果项点击
    handleItemClick(item) {
      uni.navigateTo({
        url: `/pages/instrument/detail?id=${item.id}`
      });
    }
  },
  onShow() {
    this.loadSearchHistory();
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  margin-top: var(--status-bar-height);
}

.nav-left {
  width: 60rpx;
  display: flex;
  align-items: center;
}

.nav-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.nav-right {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.search-input-box {
  flex: 1;
  display: flex;
  align-items: center;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 20rpx;
}

.search-icon {
  margin-right: 10rpx;
  width: 48rpx;
  height: 48rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}

.cancel-btn {
  padding: 0 0 0 20rpx;
  font-size: 28rpx;
  color: #007aff;
}

.history-section {
  padding: 0 30rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.history-clear {
  width: 36rpx;
  height: 36rpx;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
}

.history-item {
  padding: 10rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #333;
}

.bottom-indicator {
  position: absolute;
  bottom: 34rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 5rpx;
  background-color: #000;
  border-radius: 2.5rpx;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
}

/* 搜索结果样式 */
.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-list {
  flex: 1;
}

.result-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.result-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.empty-result {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.suggestion {
  font-size: 26rpx;
  color: #ccc;
}

.loading-result {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
}
</style> 