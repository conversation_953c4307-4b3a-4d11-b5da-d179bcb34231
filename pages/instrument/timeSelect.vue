<template>
  <view class="time-select-container">
    <!-- 固定顶部区域 -->
    <view class="fixed-top">
      <!-- 状态栏占位 -->
      <view
        class="status-bar"
        :style="{ height: statusBarHeight + 'px' }"
      ></view>

      <!-- 顶部导航栏 -->
      <view class="header">
        <view class="left-section">
          <view class="back-btn" @tap="handleCancel">
            <u-icon name="arrow-left" size="20" color="#333"></u-icon>
          </view>
          <view class="info-btn" @tap="showRuleInfo">
            <text class="info-icon">ⓘ</text>
            <text class="info-text">规则</text>
          </view>
        </view>
        <view class="center-section">
          <text class="title">{{ "选择预约时段" }}</text>
          <text
            class="subtitle"
            v-if="clickSelectionMode === 'start' && !isSelecting"
            >点击选择开始时间</text
          >
          <text class="subtitle" v-else-if="isSelecting"
            >点击调整时段，【确认】添加或【取消】选择</text
          >
        </view>
        <view class="right-section">
          <!-- 移除右上角的完成和重置按钮 -->
        </view>
      </view>
    </view>

    <!-- 整体可滚动区域 -->
    <scroll-view
      scroll-y="true"
      class="main-scroll-view"
      :style="{ paddingTop: statusBarHeight + 90 + 'px' }"
      @scroll="onScroll"
    >
      <!-- 日历部分 -->
      <view class="calendar-container">
        <time-select-calendar
          :selected-day="selectedDay"
          :min-height="calendarMinHeight"
          :max-height="calendarMaxHeight"
          @select-day="selectCalendarDay"
        />
      </view>

      <!-- 时间段选择 -->
      <view class="time-slots-container">
        <!-- 时间段和标签 -->
        <view
          v-for="(slot, index) in timeSlots"
          :key="slot.time"
          class="time-slot-wrapper"
        >
          <!-- 时间标签线 -->
          <view class="time-label-line">
            <text class="time-label-text">{{ slot.time }}</text>
          </view>

          <!-- 移除拖拽杆 -->

          <!-- 时间段 -->
          <view
            class="time-slot"
            :class="{
              unavailable: slot.unavailable,
              selected: slot.selected,
              'start-time': slot.isStart,
              'end-time': slot.isEnd,
              'range-time': slot.isRange,
            }"
            @tap="selectTimeSlot(slot, index)"
            @longpress="longPressTimeSlot(slot, index)"
          >
            <view
              v-if="slot.unavailable && isFirstUnavailableInGroup(index)"
              class="unavailable-text"
              >在此时间段不支持预约</view
            >

            <view
              v-if="slot.selected && !slot.unavailable"
              class="time-range-block"
            >
              <text v-if="slot.isStart" class="time-range-text start-text"
                >开始</text
              >
              <text v-else-if="slot.isEnd" class="time-range-text end-text"
                >结束</text
              >
              <text
                v-else-if="isMiddleOfSelection(index)"
                class="time-range-text"
                >预约时段</text
              >

              <!-- 确认和取消按钮，只在当前正在选择的结束时间段显示 -->
              <view
                v-if="slot.isEnd && isSelecting && index === selectionEnd"
                class="action-buttons"
              >
                <view class="mini-cancel-btn" @tap.stop="clearCurrentSelection">
                  <text class="mini-cancel-text">取消</text>
                </view>
                <view
                  class="mini-confirm-btn"
                  @tap.stop="completeCurrentSelection"
                >
                  <text class="mini-confirm-text">确认</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 移除拖拽杆 -->
        </view>

        <!-- 最后一个时间标签 -->
        <view class="time-label-line">
          <text class="time-label-text">{{ getEndTimeLabel() }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部面板：已选时间 + 按钮 -->
    <view class="bottom-panel">
      <!-- 已选时间列表 -->
      <view class="selected-times-section" v-if="selectedTimes.length > 0">
        <view class="selected-header">
          <text class="selected-title">已选</text>
        </view>
        <view class="selected-list">
          <view
            class="time-item"
            v-for="(time, index) in selectedTimes"
            :key="index"
            @longpress="removeSelectedTime(index)"
          >
            <text class="time-text">{{ time }}</text>
            <text class="delete-btn" @tap="removeSelectedTime(index)">×</text>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="bottom-buttons-content">
        <view class="cancel-btn" @tap="handleCancel">取消</view>
        <view class="confirm-btn" @tap="handleConfirm">确认</view>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>

    <!-- 规则提示弹窗 -->
    <lk-confirm
      ref="rulesConfirm"
      :show="showRulesPopup"
      @close="closeRulesPopup"
      title="预约规则"
      :content="rulesContent"
      :centerButton="true"
      :showCancelButton="false"
      confirmButtonText="我知道了"
      width="600rpx"
    >
    </lk-confirm>
  </view>
</template>

<!-- 移除WXS拖拽处理代码 -->

<script>
import lkConfirm from "@/components/lk-confirm/lk-confirm.vue";
import TimeSelectCalendar from "@/components/time-select-calendar/time-select-calendar.vue";
import orderApi from "@/api/order.js";
import instrumentApi from "@/api/instrument.js";
import { ScheduleType, ScheduleStatus } from "@/constants/order.js";
import { mapState } from "vuex";

export default {
  components: {
    lkConfirm,
    TimeSelectCalendar,
  },

  data() {
    return {
      // 页面参数
      instrumentId: "",
      selectedDates: [], // 从query传入的已选日期

      // 数据源
      instrumentSchedules: [], // 普遍日期设定
      instrumentScheduleSpecificDates: [], // 特定日期设定
      holidayList: [], // 节假日列表
      bookedTimeSlots: [], // 当前仪器已预约时间段

      // 加载状态
      loading: false,
      dataLoading: false,

      selectedDate: "2025-03-28",
      selectedTimeRange: {
        start: "9:00",
        end: "11:00",
      },
      calendarDays: [
        { day: 6, available: true, active: false },
        { day: 7, available: true, active: false },
        { day: 8, available: true, active: false },
        { day: 9, available: true, active: true },
        { day: 10, available: true, active: true },
        { day: 11, available: true, active: false },
        { day: 12, available: true, active: false },
      ],
      // 预约配置
      minReservationMinutes: 60, // 最小预约时长：1小时 = 60分钟
      timeUnitMinutes: 15, // 最小计费单位：15分钟
      leadTime: 0, // 提前预约时间（小时）

      // 时间段数据：以15分钟为单位，从8:00到21:00
      timeSlots: [],
      selectedTimes: [],
      selectedTimeRanges: [], // 存储所有已选择的时间段范围 [{start: 0, end: 7}, {start: 20, end: 27}]
      selectionStart: null, // 当前选择开始的索引
      selectionEnd: null, // 当前选择结束的索引
      isSelecting: false, // 是否正在选择
      clickSelectionMode: "start", // 点击选择模式：'start' 表示下次点击选择开始时间，'end' 表示下次点击选择结束时间
      currentReservationMinutes: 0, // 当前预约时长（分钟）
      scrollViewHeight: 0,
      pageTitle: "选择预约时间",
      showRulesPopup: false,
      statusBarHeight: 0,
      calendarMaxHeight: 350,
      calendarMinHeight: 150,
      selectedDay: null,
    };
  },
  mounted() {
    // 默认选中今天
    const today = new Date();
    this.selectedDay = today.getDate();

    // 初始化时间段数据（15分钟为单位）
    this.initTimeSlots();

    // 不再默认选择时间段，等待用户点击
  },
  onLoad(option) {
    // 页面加载时的初始化
    console.log("页面加载参数:", option);

    // 设置状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    console.log("状态栏高度:", this.statusBarHeight);

    // 获取传入的参数
    if (option) {
      if (option.instrumentId) {
        this.instrumentId = option.instrumentId;
      }

      if (option.title) {
        // 解码URL参数
        const decodedTitle = decodeURIComponent(option.title);
        this.pageTitle = decodedTitle;
        console.log("设置页面标题:", this.pageTitle);
      }

      // 回显已选日期
      if (option.selectedDates) {
        try {
          this.selectedDates = JSON.parse(
            decodeURIComponent(option.selectedDates)
          );
          console.log("已选日期:", this.selectedDates);
        } catch (error) {
          console.error("解析已选日期失败:", error);
          this.selectedDates = [];
        }
      }
    }

    // 检查必要参数
    if (!this.instrumentId) {
      uni.showToast({
        title: "缺少仪器ID参数",
        icon: "none",
      });
      return;
    }

    // 初始化数据
    this.initPageData();

    // 延迟计算滚动视图高度，确保DOM已渲染
    setTimeout(() => {
      this.calcScrollViewHeight();
    }, 300);

    // 监听窗口大小变化
    uni.onWindowResize(() => {
      this.calcScrollViewHeight();
    });
  },
  computed: {
    ...mapState(["userInfo", "roleLevel"]),

    // 用户等级
    userLevel() {
      // 优先从 userInfo.roleType 获取，其次是 roleLevel，最后默认为 1
      return this.userInfo?.roleType || this.roleLevel || 1;
    },
    formattedSelectedTimes() {
      // 格式化已选时间
      return this.selectedTimes.map((time) => time);
    },
    currentTimeRange() {
      // 获取当前选择的时间范围字符串
      if (this.selectionStart === null || this.selectionEnd === null) {
        return "";
      }

      const startTime = this.timeSlots[this.selectionStart].time;
      const endSlot = this.timeSlots[this.selectionEnd];
      const endHour = endSlot.hour;
      const endMinute = endSlot.minute + this.timeUnitMinutes;
      const finalEndHour = endMinute >= 60 ? endHour + 1 : endHour;
      const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;
      const endTime = `${finalEndHour
        .toString()
        .padStart(2, "0")}:${finalEndMinute.toString().padStart(2, "0")}`;

      return `${startTime}-${endTime}`;
    },
    rulesContent() {
      return [
        {
          text: "● 需提前12小时预约",
          style: { marginBottom: "20rpx", lineHeight: "1.6" },
        },
        {
          text: `● 当前仪器最小预约时长${this.minReservationMinutes}分钟，最小计价单位为15分钟，预约时长需为最小时长+最小计价单位时长的倍数`,
          style: { marginBottom: "20rpx", lineHeight: "1.6" },
        },
      ];
    },
  },
  methods: {
    // 初始化页面数据
    async initPageData() {
      this.dataLoading = true;

      try {
        // 并行获取所有必要数据
        await Promise.all([
          this.fetchInstrumentSchedules(),
          this.fetchHolidayList(),
          this.fetchBookedTimeSlots(),
        ]);

        // 初始化时间段数据
        this.initTimeSlots();

        // 回显已选时间
        this.restoreSelectedTimes();
      } catch (error) {
        console.error("初始化页面数据失败:", error);
        uni.showToast({
          title: "数据加载失败",
          icon: "none",
        });
      } finally {
        this.dataLoading = false;
      }
    },

    // 获取仪器时间配置
    async fetchInstrumentSchedules() {
      try {
        // 从仪器详情接口获取时间配置数据
        const instrumentDetail = await instrumentApi.getInstrumentDetail({
          id: parseInt(this.instrumentId),
        });

        if (instrumentDetail) {
          // 获取提前预约时间
          this.leadTime = instrumentDetail.leadTime || 0;
          console.log("获取到的提前预约时间:", this.leadTime, "小时");

          // 获取普遍配置，过滤掉已删除的数据
          this.instrumentSchedules = (
            instrumentDetail.instrumentSchedules || []
          )
            .filter((schedule) => schedule.isDeleted === 0)
            .map((schedule) => ({
              ...schedule,
              groups: (schedule.groups || [])
                .filter((group) => group.isDeleted === 0)
                .map((group) => ({
                  ...group,
                  timeslots: (group.timeslots || []).filter(
                    (slot) => slot.isDeleted === 0
                  ),
                })),
            }));

          // 获取特定日期配置，过滤掉已删除的数据
          this.instrumentScheduleSpecificDates = (
            instrumentDetail.instrumentScheduleSpecificDates || []
          )
            .filter((config) => config.isDeleted === 0)
            .map((config) => ({
              ...config,
              timeslots: (config.timeslots || []).filter(
                (slot) => slot.isDeleted === 0
              ),
            }));

          console.log("获取到的普遍配置:", this.instrumentSchedules);
          console.log(
            "获取到的特定日期配置:",
            this.instrumentScheduleSpecificDates
          );
        } else {
          console.warn("未获取到仪器详情数据");
          this.instrumentSchedules = [];
          this.instrumentScheduleSpecificDates = [];
        }
      } catch (error) {
        console.error("获取仪器时间配置失败:", error);
        // 如果接口调用失败，使用空数组避免页面崩溃
        this.instrumentSchedules = [];
        this.instrumentScheduleSpecificDates = [];
        throw error;
      }
    },

    // 获取节假日列表
    async fetchHolidayList() {
      try {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 2, 0);

        const data = await orderApi.getHolidayList({
          startDate: this.formatDate(startDate),
          endDate: this.formatDate(endDate),
        });

        this.holidayList = data || [];
      } catch (error) {
        console.error("获取节假日列表失败:", error);
        throw error;
      }
    },

    // 获取当前仪器已预约时间段
    async fetchBookedTimeSlots() {
      try {
        // 获取当前选中日期的已预约时间段
        if (this.selectedDate) {
          const data = await orderApi.getInstrumentTimeSlots({
            instrumentId: parseInt(this.instrumentId),
            queryDate: this.selectedDate,
          });

          this.bookedTimeSlots = data || [];
        }
      } catch (error) {
        console.error("获取已预约时间段失败:", error);
        throw error;
      }
    },



    // 初始化时间段数据（15分钟为单位）
    initTimeSlots() {
      this.timeSlots = [];
      const startHour = 8; // 开始时间：8:00
      const endHour = 21; // 结束时间：21:00

      for (let hour = startHour; hour < endHour; hour++) {
        for (let minute = 0; minute < 60; minute += this.timeUnitMinutes) {
          const timeString = `${hour.toString().padStart(2, "0")}:${minute
            .toString()
            .padStart(2, "0")}`;
          const totalMinutes = hour * 60 + minute;

          // 判断时间段是否可用
          const isUnavailable = this.isTimeSlotUnavailable(
            this.selectedDate,
            timeString,
            totalMinutes
          );

          this.timeSlots.push({
            time: timeString,
            hour: hour,
            minute: minute,
            totalMinutes: totalMinutes, // 从0:00开始的总分钟数
            unavailable: isUnavailable,
            selected: false,
            isStart: false,
            isEnd: false,
            isRange: false,
          });
        }
      }
    },

    // 判断时间段是否不可用
    isTimeSlotUnavailable(date, timeString, totalMinutes) {
      // 1. 检查是否超过当前时间或小于提前预约时间
      if (this.isTimeSlotTooEarly(date, timeString)) {
        return true;
      }

      // 2. 检查是否为节假日且不允许预约
      if (this.isHolidayAndNotAllowed(date)) {
        return true;
      }

      // 3. 检查用户等级是否有权限预约此时间段
      if (!this.hasUserPermissionForTimeSlot(date, timeString)) {
        return true;
      }

      // 4. 检查是否已被预约
      if (this.isTimeSlotBooked(timeString)) {
        return true;
      }

      // 5. 检查特定日期配置
      const specificConfig = this.getSpecificDateConfig(date);
      if (specificConfig) {
        return !this.isTimeInSpecificConfig(timeString, specificConfig);
      }

      // 6. 检查普遍配置
      return !this.isTimeInGeneralConfig(date, timeString);
    },

    // 检查时间段是否太早（超过当前时间或小于提前预约时间）
    isTimeSlotTooEarly(date, timeString) {
      const now = new Date();
      const slotDateTime = new Date(`${date} ${timeString}:00`);

      // 计算最早可预约时间（当前时间 + 提前预约时间）
      const earliestBookingTime = new Date(now.getTime() + this.leadTime * 60 * 60 * 1000);

      // 如果时间段早于最早可预约时间，则不可用
      if (slotDateTime <= earliestBookingTime) {
        console.log(`时间段 ${date} ${timeString} 太早，最早可预约时间：${earliestBookingTime.toLocaleString()}`);
        return true;
      }

      return false;
    },

    // 检查是否为节假日且不允许预约
    isHolidayAndNotAllowed(date) {
      const holiday = this.holidayList.find((h) => h.holidayDate === date);
      if (!holiday) return false;

      // 如果是节假日且状态为禁用，则不允许预约
      if (
        holiday.type === ScheduleType.HOLIDAY &&
        holiday.status === ScheduleStatus.DISABLED
      ) {
        return true;
      }

      return false;
    },

    // 检查用户等级是否有权限预约此时间段
    hasUserPermissionForTimeSlot(date, timeString) {
      // 根据用户等级和时间段配置判断权限
      // 这里需要根据具体的业务逻辑实现
      // 暂时返回 true，表示有权限
      return true;
    },

    // 检查时间段是否已被当前仪器预约
    isTimeSlotBooked(timeString) {
      return this.bookedTimeSlots.some((slot) => {
        const startTime = slot.startTime.substring(11, 16); // 提取 HH:mm
        const endTime = slot.endTime.substring(11, 16);
        return timeString >= startTime && timeString < endTime;
      });
    },



    // 获取特定日期配置
    getSpecificDateConfig(date) {
      return this.instrumentScheduleSpecificDates.find(
        (config) =>
          config.specificDate === date &&
          config.roleType === this.userLevel &&
          config.instrumentId === parseInt(this.instrumentId) &&
          config.isDeleted === 0
      );
    },

    // 检查配置是否适用于当前用户等级
    isConfigForUserLevel(config) {
      // 根据用户等级判断配置是否适用
      // 这里需要根据具体的业务逻辑实现
      return true;
    },

    // 检查时间是否在特定配置中
    isTimeInSpecificConfig(timeString, config) {
      if (!config) {
        return false;
      }

      // 检查配置的日期类型
      if (config.dateType === 1) {
        // 1: 关闭整天，不可预约
        return false;
      }

      if (config.dateType === 2) {
        // 2: 自定义时间段，检查时间是否在配置的时间段内
        if (!config.timeslots || config.timeslots.length === 0) {
          return false;
        }

        // 过滤掉已删除的时间段
        const activeTimeslots = config.timeslots.filter(
          (slot) => slot.isDeleted === 0
        );
        if (activeTimeslots.length === 0) {
          return false;
        }

        return this.isTimeInSpecificTimeslots(timeString, activeTimeslots);
      }

      return false;
    },

    // 检查时间是否在特定时间段列表中（处理后台返回的复杂时间格式）
    isTimeInSpecificTimeslots(timeString, timeslots) {
      if (!timeslots || timeslots.length === 0) {
        return false;
      }

      // 将时间字符串转换为分钟数进行比较
      const timeMinutes = this.timeStringToMinutes(timeString);

      return timeslots.some((slot) => {
        // 处理后台返回的时间格式：{ hour: number, minute: number, second: number, nano: number }
        const startMinutes = this.timeObjectToMinutes(slot.startTime);
        const endMinutes = this.timeObjectToMinutes(slot.endTime);

        // 检查时间是否在时间段范围内
        return timeMinutes >= startMinutes && timeMinutes < endMinutes;
      });
    },

    // 将时间对象转换为分钟数
    timeObjectToMinutes(timeObj) {
      if (!timeObj) return 0;

      // 处理后台返回的时间格式
      if (
        typeof timeObj === "object" &&
        timeObj.hour !== undefined &&
        timeObj.minute !== undefined
      ) {
        return timeObj.hour * 60 + timeObj.minute;
      }

      // 如果是字符串格式，使用原有方法
      if (typeof timeObj === "string") {
        return this.timeStringToMinutes(timeObj);
      }

      return 0;
    },

    // 检查时间是否在时间段列表中（兼容旧格式）
    isTimeInTimeslots(timeString, timeslots) {
      if (!timeslots || timeslots.length === 0) {
        return false;
      }

      // 将时间字符串转换为分钟数进行比较
      const timeMinutes = this.timeStringToMinutes(timeString);

      return timeslots.some((slot) => {
        // 处理不同的时间格式
        const startTime = slot.startTime.includes(":")
          ? slot.startTime.substring(0, 5)
          : slot.startTime; // 提取 HH:mm
        const endTime = slot.endTime.includes(":")
          ? slot.endTime.substring(0, 5)
          : slot.endTime;

        const startMinutes = this.timeStringToMinutes(startTime);
        const endMinutes = this.timeStringToMinutes(endTime);

        // 检查时间是否在时间段范围内
        return timeMinutes >= startMinutes && timeMinutes < endMinutes;
      });
    },

    // 检查时间是否在普遍配置中
    isTimeInGeneralConfig(date, timeString) {
      // 获取当前用户等级的普遍配置
      const userSchedule = this.getUserLevelSchedule();
      if (
        !userSchedule ||
        !userSchedule.groups ||
        userSchedule.groups.length === 0
      ) {
        return false; // 没有配置则不可预约
      }


      // 遍历所有时间组，检查是否有匹配的配置
      for (const group of userSchedule.groups) {

        // 检查日期类型是否匹配
        if (this.isDateMatchGroup(date, group)) {

          // 检查时间段是否在配置范围内
          if (this.isTimeInGroupTimeslots(timeString, group.timeslots)) {

            return true;
          }
        }
      }

      return false;
    },

    // 获取当前用户等级的排期配置
    getUserLevelSchedule() {
      const userSchedule = this.instrumentSchedules.find(
        (schedule) => schedule.roleType == this.userLevel
      );

      return userSchedule;
    },

    // 检查日期是否匹配时间组的配置
    isDateMatchGroup(date, group) {
      const dateObj = new Date(date);
      const dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一, ..., 6=周六

      if (group.isWorkday === 1) {
        // 法定工作日（智能过滤节假日）
        return this.isWorkdayAndNotHoliday(date, dayOfWeek);
      } else {
        // 自定义星期
        if (!group.unfixDays) return false;

        // 将周日从0转换为7，以匹配后台配置（1-7表示周一到周日）
        const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
        const allowedDays = group.unfixDays
          .split(",")
          .map((day) => parseInt(day.trim()));

        return allowedDays.includes(adjustedDayOfWeek);
      }
    },

    // 检查是否为工作日且非节假日
    isWorkdayAndNotHoliday(date, dayOfWeek) {
      // 首先检查是否为周末（周六=6, 周日=0）
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

      // 检查节假日配置
      const holiday = this.holidayList.find((h) => h.holidayDate === date);

      if (holiday) {
        // 如果有节假日配置
        switch (holiday.type) {
          case ScheduleType.HOLIDAY:
            // 节假日，不可预约
            return false;
          case ScheduleType.WORKDAY:
            // 调休日（工作日），可预约
            return true;
          case ScheduleType.WEEKEND:
            // 正常周末，不可预约
            return false;
          default:
            return false;
        }
      } else {
        // 没有特殊配置，按正常工作日判断
        // 周一到周五为工作日
        return !isWeekend;
      }
    },

    // 检查时间是否在时间组的时间段内
    isTimeInGroupTimeslots(timeString, timeslots) {
      if (!timeslots || timeslots.length === 0) {
        return false;
      }

      // 将时间字符串转换为分钟数进行比较
      const timeMinutes = this.timeStringToMinutes(timeString);

      return timeslots.some((slot) => {
        // 处理后台返回的时间格式：{ hour: number, minute: number, second: number, nano: number }
        const startMinutes = this.timeObjectToMinutes(slot.startTime);
        const endMinutes = this.timeObjectToMinutes(slot.endTime);

        // 检查时间是否在时间段范围内
        return timeMinutes >= startMinutes && timeMinutes < endMinutes;
      });
    },

    // 时间字符串转换为分钟数
    timeStringToMinutes(timeString) {
      const [hours, minutes] = timeString.split(":").map(Number);
      return hours * 60 + minutes;
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 回显已选时间
    restoreSelectedTimes() {
      if (this.selectedDates && this.selectedDates.length > 0) {
        console.log('开始回显已选时间:', this.selectedDates);
        this.selectedTimes = [...this.selectedDates];

        // 解析已选时间并标记对应的时间段（只处理当前日期的）
        this.selectedDates.forEach((timeStr) => {
          this.parseAndMarkSelectedTime(timeStr);
        });

        // 应用所有选择状态到时间段
        this.applyAllSelections();
        console.log('应用选择状态，当前时间范围:', this.selectedTimeRanges);

        // 强制触发视图更新
        this.$forceUpdate();

        // 调试：检查时间段状态
        this.debugTimeSlotStates();

        // 如果当前日期没有已选时间，尝试切换到第一个有时间的日期
        if (this.selectedTimeRanges.length === 0 && this.selectedDates.length > 0) {
          const firstTimeStr = this.selectedDates[0];
          const match = firstTimeStr.match(/(\d{4}-\d{2}-\d{2})/);
          if (match) {
            const firstDate = match[1];
            console.log('切换到第一个有预约的日期:', firstDate);
            this.selectedDate = firstDate;
            // 重新初始化时间段并应用选择
            this.initTimeSlots();
            this.parseAndMarkSelectedTime(firstTimeStr);
            this.applyAllSelections();
          }
        }
      }
    },

    // 解析并标记已选时间
    parseAndMarkSelectedTime(timeStr) {
      console.log('解析时间字符串:', timeStr);
      // 解析时间字符串，例如："2023-05-09 09:00-11:00 (120分钟)"
      const match = timeStr.match(
        /(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2})-(\d{2}:\d{2})/
      );
      if (match) {
        const [, date, startTime, endTime] = match;
        console.log('解析结果:', { date, startTime, endTime, currentDate: this.selectedDate });

        if (date === this.selectedDate) {
          // 找到对应的时间段索引并标记为已确认
          const startIndex = this.findTimeSlotIndex(startTime);
          const endIndex = this.findTimeSlotIndex(endTime) - 1; // 结束时间不包含

          console.log('时间段索引:', { startIndex, endIndex });

          if (startIndex !== -1 && endIndex !== -1 && endIndex >= startIndex) {
            // 检查是否已经存在相同的时间范围
            const existingRange = this.selectedTimeRanges.find(
              range => range.start === startIndex && range.end === endIndex
            );

            if (!existingRange) {
              this.selectedTimeRanges.push({
                start: startIndex,
                end: endIndex,
              });
              console.log('添加时间范围:', { start: startIndex, end: endIndex });
            }
          }
        }
      } else {
        console.warn('无法解析时间字符串:', timeStr);
      }
    },

    // 查找时间段索引
    findTimeSlotIndex(timeString) {
      return this.timeSlots.findIndex((slot) => slot.time === timeString);
    },

    // 调试：检查时间段状态
    debugTimeSlotStates() {
      const selectedSlots = this.timeSlots.filter(slot => slot.selected);
      console.log('当前选中的时间段数量:', selectedSlots.length);
      selectedSlots.forEach((slot, index) => {
        console.log(`选中时间段 ${index}: ${slot.time}, isStart: ${slot.isStart}, isEnd: ${slot.isEnd}, isRange: ${slot.isRange}`);
      });
    },

    // 当日期改变时重新获取数据
    async onDateChange(newDate) {
      console.log('日期切换到:', newDate);
      this.selectedDate = newDate;

      // 清除当前的时间范围选择
      this.selectedTimeRanges = [];
      this.clearAllSelections();

      // 重新获取该日期的已预约时间段
      await this.fetchBookedTimeSlots();

      // 重新初始化时间段
      this.initTimeSlots();

      // 回显该日期的已选时间
      if (this.selectedDates && this.selectedDates.length > 0) {
        this.selectedDates.forEach((timeStr) => {
          this.parseAndMarkSelectedTime(timeStr);
        });
      }

      // 重新应用已选择的时间段
      this.applyAllSelections();

      // 强制触发视图更新
      this.$forceUpdate();
    },

    calcScrollViewHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;

      // 固定顶部区域高度估算
      const topFixedHeight = this.statusBarHeight + uni.upx2px(300);
      console.log("固定顶部高度估算:", topFixedHeight);

      // 底部区域高度
      const bottomHeight = uni.upx2px(120);

      // 已选时间区域高度
      const selectedTimesHeight =
        this.selectedTimes.length > 0 ? uni.upx2px(150) : 0;

      // 计算滚动区域高度
      this.scrollViewHeight = windowHeight - bottomHeight - selectedTimesHeight;
      console.log("滚动区域高度:", this.scrollViewHeight);
    },
    selectDay(day) {
      // 重置所有日期的活动状态
      this.calendarDays.forEach((d) => (d.active = false));
      // 设置选定的日期为活动状态
      day.active = true;
    },

    // 点击选择时间段
    selectTimeSlot(slot, index) {
      console.log(
        "点击时间段:",
        index,
        "选择模式:",
        this.clickSelectionMode,
        "当前选择:",
        this.selectionStart,
        "-",
        this.selectionEnd
      );

      if (slot.unavailable) {
        uni.showToast({
          title: "该时段不可用",
          icon: "none",
          duration: 1000,
        });
        return;
      }



      // 检查是否点击了已确认的时间段
      const clickedConfirmedRange = this.selectedTimeRanges.find(
        (range) => index >= range.start && index <= range.end
      );

      if (clickedConfirmedRange) {
        uni.showToast({
          title: "该时间段已确认，请选择其他时间段",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      // 如果点击的是当前正在选择的时间段，也允许调整（不直接返回）
      // 这样用户可以点击选中区域内的任何位置来缩小选择范围

      // 根据当前选择模式处理点击
      if (this.clickSelectionMode === "start") {
        // 选择开始时间
        this.selectStartTime(index);
      } else if (this.clickSelectionMode === "end") {
        // 选择结束时间或调整当前选择
        this.selectEndTime(index);
      }
    },

    // 选择开始时间
    selectStartTime(index) {
      console.log("选择开始时间:", index);

      this.selectionStart = index;
      this.isSelecting = true;
      this.clickSelectionMode = "end";

      // 计算最小预约时长对应的结束索引
      const minSlotsCount = this.minReservationMinutes / this.timeUnitMinutes; // 60/15 = 4
      const endIndex = index + minSlotsCount - 1;

      // 检查是否超出范围或包含不可用时段
      if (
        endIndex >= this.timeSlots.length ||
        this.hasUnavailableInRange(index, endIndex)
      ) {
        uni.showToast({
          title: "该位置无法满足最小预约时长要求",
          icon: "none",
          duration: 2000,
        });
        this.clearCurrentSelection();
        return;
      }

      // 自动设置结束时间为最小预约时长
      this.selectionEnd = endIndex;
      this.currentReservationMinutes = this.minReservationMinutes;

      // 更新选择状态
      this.updateSelection(index, endIndex);

      uni.showToast({
        title: `已选择${this.currentTimeRange}，可调整时段或确认添加`,
        icon: "none",
        duration: 2000,
      });
    },

    // 选择结束时间
    selectEndTime(index) {
      console.log(
        "选择结束时间:",
        index,
        "当前开始:",
        this.selectionStart,
        "当前结束:",
        this.selectionEnd
      );

      if (this.selectionStart === null) {
        // 如果没有开始时间，将点击的位置设为开始时间
        this.selectStartTime(index);
        return;
      }

      // 如果点击的位置在当前开始时间之前，重新选择开始时间
      if (index < this.selectionStart) {
        this.selectStartTime(index);
        return;
      }

      // 如果点击的位置等于开始时间，选择最小时长
      if (index === this.selectionStart) {
        const minSlotsCount = this.minReservationMinutes / this.timeUnitMinutes;
        const endIndex = this.selectionStart + minSlotsCount - 1;

        if (
          endIndex >= this.timeSlots.length ||
          this.hasUnavailableInRange(this.selectionStart, endIndex)
        ) {
          uni.showToast({
            title: "该位置无法满足最小预约时长要求",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        this.selectionEnd = endIndex;
        this.currentReservationMinutes = this.minReservationMinutes;
        this.updateSelection(this.selectionStart, endIndex);
        return;
      }

      // 检查时间段长度是否满足最小预约时长
      const totalSlots = index - this.selectionStart + 1;
      const totalMinutes = totalSlots * this.timeUnitMinutes;
      const minSlotsCount = this.minReservationMinutes / this.timeUnitMinutes;

      if (totalSlots < minSlotsCount) {
        uni.showToast({
          title: `最小预约时长为${this.minReservationMinutes}分钟`,
          icon: "none",
          duration: 2000,
        });
        return;
      }

      // 检查范围内是否有不可用时段
      if (this.hasUnavailableInRange(this.selectionStart, index)) {
        uni.showToast({
          title: "选择范围内包含不可用时段",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      this.selectionEnd = index;
      this.currentReservationMinutes = totalMinutes;

      // 更新选择状态
      this.updateSelection(this.selectionStart, index);

      // 显示当前选择，但不自动添加，让用户可以继续调整
      uni.showToast({
        title: `已选择${this.currentTimeRange}，可继续调整或确认添加`,
        icon: "none",
        duration: 1500,
      });
    },

    // 更新选择状态
    updateSelection(start, end) {
      // 只清除当前正在选择的状态，保留已确认的选择
      this.clearCurrentSelectionOnly();

      this.selectionStart = start;
      this.selectionEnd = end;

      // 重新应用所有已确认的选择
      this.applyAllSelections();

      // 应用当前正在选择的状态
      for (let i = start; i <= end; i++) {
        this.timeSlots[i].selected = true;
        if (i === start) {
          this.timeSlots[i].isStart = true;
        } else if (i === end) {
          this.timeSlots[i].isEnd = true;
        } else {
          this.timeSlots[i].isRange = true;
        }
      }
    },

    // 清除所有选择状态
    clearAllSelections() {
      this.timeSlots.forEach((s) => {
        s.selected = false;
        s.isStart = false;
        s.isEnd = false;
        s.isRange = false;
      });
    },

    // 只清除当前正在选择的状态，保留已确认的选择
    clearCurrentSelectionOnly() {
      this.timeSlots.forEach((s) => {
        s.selected = false;
        s.isStart = false;
        s.isEnd = false;
        s.isRange = false;
      });
    },

    // 应用所有已确认的选择
    applyAllSelections() {
      console.log('应用所有选择状态，时间范围数量:', this.selectedTimeRanges.length);
      this.selectedTimeRanges.forEach((range, rangeIndex) => {
        console.log(`应用时间范围 ${rangeIndex}:`, range);
        for (let i = range.start; i <= range.end && i < this.timeSlots.length; i++) {
          if (this.timeSlots[i]) {
            this.timeSlots[i].selected = true;
            if (i === range.start) {
              this.timeSlots[i].isStart = true;
            } else if (i === range.end) {
              this.timeSlots[i].isEnd = true;
            } else {
              this.timeSlots[i].isRange = true;
            }
            console.log(`设置时间段 ${i} (${this.timeSlots[i].time}) 为选中状态`);
          }
        }
      });
    },

    // 检查范围内是否有不可用时段或已确认的时间段
    hasUnavailableInRange(start, end) {
      for (let i = start; i <= end && i < this.timeSlots.length; i++) {
        // 检查是否是不可用时段
        if (this.timeSlots[i].unavailable) {
          return true;
        }



        // 检查是否与已确认的时间段重叠
        const hasOverlap = this.selectedTimeRanges.some(
          (range) => i >= range.start && i <= range.end
        );
        if (hasOverlap) {
          return true;
        }
      }
      return false;
    },
    handleCancel() {
      // 返回上一页
      uni.navigateBack();
    },
    handleConfirm() {
      if (this.selectedTimes.length === 0) {
        uni.showToast({ title: "请选择时间段", icon: "none" });
        return;
      }

      // 返回所有已选择的时间段
      const result = {
        selectedTimes: this.selectedTimes,
        selectedTimeRanges: this.selectedTimeRanges,
      };

      // 通过事件发送选中的时间
      uni.$emit("selectReserveTime", result);

      // 返回上一页
      uni.navigateBack();
    },

    // 完成当前选择并添加到已选列表
    completeCurrentSelection() {
      if (this.selectionStart === null || this.selectionEnd === null) return;

      const startTime = this.timeSlots[this.selectionStart].time;
      const endSlot = this.timeSlots[this.selectionEnd];
      const endHour = endSlot.hour;
      const endMinute = endSlot.minute + this.timeUnitMinutes;
      const finalEndHour = endMinute >= 60 ? endHour + 1 : endHour;
      const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;
      const endTime = `${finalEndHour
        .toString()
        .padStart(2, "0")}:${finalEndMinute.toString().padStart(2, "0")}`;

      // 使用当前年月
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, "0");
      const day = this.selectedDay.toString().padStart(2, "0");
      const selectedDate = `${year}-${month}-${day}`;
      const timeRange = `${selectedDate} ${startTime}-${endTime} (${this.currentReservationMinutes}分钟)`;

      // 检查是否已经存在相同的时间段
      if (!this.selectedTimes.includes(timeRange)) {
        this.selectedTimes.push(timeRange);

        // 将当前选择添加到已确认的时间段范围
        this.selectedTimeRanges.push({
          start: this.selectionStart,
          end: this.selectionEnd,
        });
      } else {
        uni.showToast({
          title: "该时间段已存在",
          icon: "none",
          duration: 1000,
        });
      }

      // 重置当前选择状态，但保持已确认的选择显示
      this.selectionStart = null;
      this.selectionEnd = null;
      this.isSelecting = false;
      this.clickSelectionMode = "start";
      this.currentReservationMinutes = 0;

      // 重新应用所有选择状态
      this.applyAllSelections();

      // 重新计算滚动视图高度，不自动选择下一个时间段
      this.$nextTick(() => {
        this.calcScrollViewHeight();
      });
    },
    onScroll() {
      // 实现滚动事件处理逻辑
    },
    onReady() {
      // 页面渲染完成
      if (this.pageTitle) {
        uni.setNavigationBarTitle({
          title: this.pageTitle,
        });

        // 延迟执行确保标题更新
        setTimeout(() => {
          this.calcScrollViewHeight();
        }, 100);
      }
    },
    showRuleInfo() {
      // 显示规则信息弹窗
      this.showRulesPopup = true;
    },
    closeRulesPopup() {
      // 关闭规则信息弹窗
      this.showRulesPopup = false;
    },
    async selectCalendarDay(day) {
      this.selectedDay = day;

      // 构建完整的日期字符串
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, "0");
      const dayStr = day.toString().padStart(2, "0");
      const newDate = `${year}-${month}-${dayStr}`;

      // 调用日期改变处理
      await this.onDateChange(newDate);
    },

    // 删除已选时间
    removeSelectedTime(index) {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除这个时间段吗？",
        success: (res) => {
          if (res.confirm) {
            // 同时删除时间段文本和对应的范围
            this.selectedTimes.splice(index, 1);
            this.selectedTimeRanges.splice(index, 1);

            // 重新应用所有选择状态
            this.clearAllSelections();
            this.applyAllSelections();

            this.$nextTick(() => {
              this.calcScrollViewHeight();
            });
            uni.showToast({
              title: "已删除",
              icon: "success",
              duration: 1000,
            });
          }
        },
      });
    },

    // 清除当前选择状态
    clearCurrentSelection() {
      this.isSelecting = false;
      this.selectionStart = null;
      this.selectionEnd = null;
      this.currentReservationMinutes = 0;
      this.clickSelectionMode = "start"; // 重置为选择开始时间模式

      // 清除所有选择状态，然后重新应用已确认的选择
      this.clearAllSelections();
      this.applyAllSelections();
    },

    // 获取结束时间标签
    getEndTimeLabel() {
      if (this.timeSlots.length === 0) return "21:00";
      const lastSlot = this.timeSlots[this.timeSlots.length - 1];
      const endHour =
        lastSlot.minute + this.timeUnitMinutes >= 60
          ? lastSlot.hour + 1
          : lastSlot.hour;
      const endMinute =
        lastSlot.minute + this.timeUnitMinutes >= 60
          ? lastSlot.minute + this.timeUnitMinutes - 60
          : lastSlot.minute + this.timeUnitMinutes;
      return `${endHour.toString().padStart(2, "0")}:${endMinute
        .toString()
        .padStart(2, "0")}`;
    },

    // 判断是否是不可用时段组的第一个
    isFirstUnavailableInGroup(index) {
      if (!this.timeSlots[index]?.unavailable) return false;
      // 如果是第一个时段，或者前一个时段不是不可用的，则显示文字
      return index === 0 || !this.timeSlots[index - 1]?.unavailable;
    },



    // 判断是否是选中范围的中间位置（用于显示"预约时段"文字）
    isMiddleOfSelection(index) {
      if (this.selectionStart === null || this.selectionEnd === null)
        return false;
      // 只在选中范围的中间位置显示一次"预约时段"
      const middleIndex = Math.floor(
        (this.selectionStart + this.selectionEnd) / 2
      );
      return index === middleIndex;
    },

    // 默认选中下一个可用的最小预约时段
    selectDefaultTimeSlot(showToast = true) {
      console.log("选择默认时间段");

      const minSlotsCount = this.minReservationMinutes / this.timeUnitMinutes; // 120/15 = 8

      // 找到下一个可用的时间段（不与已确认的时间段重叠）
      let firstAvailableIndex = -1;
      for (let i = 0; i < this.timeSlots.length - minSlotsCount + 1; i++) {
        // 检查这个位置开始的最小时长是否可用
        if (!this.hasUnavailableInRange(i, i + minSlotsCount - 1)) {
          firstAvailableIndex = i;
          break;
        }
      }

      if (firstAvailableIndex === -1) {
        console.log("没有找到可用的时间段");
        if (showToast) {
          uni.showToast({
            title: "没有更多可用时间段",
            icon: "none",
            duration: 2000,
          });
        }
        return;
      }

      const endIndex = firstAvailableIndex + minSlotsCount - 1;

      // 设置默认选择
      this.selectionStart = firstAvailableIndex;
      this.selectionEnd = endIndex;
      this.isSelecting = true;
      this.clickSelectionMode = "end"; // 设置为可以选择结束时间的模式
      this.currentReservationMinutes = this.minReservationMinutes;

      // 更新选择状态
      this.updateSelection(firstAvailableIndex, endIndex);

      console.log(
        `默认选择时间段: ${
          this.timeSlots[firstAvailableIndex].time
        }-${this.getEndTimeFromIndex(endIndex)}`
      );

      // 只在首次加载时显示提示
      if (showToast) {
        uni.showToast({
          title: `已默认选择${this.currentTimeRange}，点击调整或确认添加`,
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 长按时间段处理
    longPressTimeSlot(_, index) {
      console.log("长按时间段:", index);

      // 检查是否是已确认的时间段
      const confirmedRangeIndex = this.selectedTimeRanges.findIndex(
        (range) => index >= range.start && index <= range.end
      );

      if (confirmedRangeIndex !== -1) {
        // 长按已确认的时间段，删除整个时间段
        uni.showModal({
          title: "删除时间段",
          content: "确定要删除这个预约时间段吗？",
          success: (res) => {
            if (res.confirm) {
              // 删除对应的时间段
              this.selectedTimes.splice(confirmedRangeIndex, 1);
              this.selectedTimeRanges.splice(confirmedRangeIndex, 1);

              // 重新应用所有选择状态
              this.clearAllSelections();
              this.applyAllSelections();

              this.$nextTick(() => {
                this.calcScrollViewHeight();
              });
            }
          },
        });
      } else if (
        this.isSelecting &&
        index >= this.selectionStart &&
        index <= this.selectionEnd
      ) {
        // 长按当前正在选择的时间段，取消选择
        uni.showModal({
          title: "取消选择",
          content: "确定要取消当前选择吗？",
          success: (res) => {
            if (res.confirm) {
              this.clearCurrentSelection();
              uni.showToast({
                title: "已取消选择",
                icon: "none",
                duration: 1000,
              });
            }
          },
        });
      } else {
        // 长按其他区域，提示功能
        uni.showToast({
          title: "长按已选时间段可删除",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 移除拖拽相关方法

    // 根据索引获取结束时间
    getEndTimeFromIndex(index) {
      if (index >= this.timeSlots.length) return "";
      const slot = this.timeSlots[index];
      const endHour =
        slot.minute + this.timeUnitMinutes >= 60 ? slot.hour + 1 : slot.hour;
      const endMinute =
        slot.minute + this.timeUnitMinutes >= 60
          ? slot.minute + this.timeUnitMinutes - 60
          : slot.minute + this.timeUnitMinutes;
      return `${endHour.toString().padStart(2, "0")}:${endMinute
        .toString()
        .padStart(2, "0")}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.time-select-container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

/* 固定顶部区域 */
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: 100;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  height: 90rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  z-index: 100;
}

.left-section {
  display: flex;
  align-items: center;
  z-index: 102;
  width: 200rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  padding-right: 15rpx;
}

.back-arrow {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.info-btn {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
  padding: 8rpx 20rpx;
  border: 1rpx solid #e3e3e3;
  border-radius: 20rpx;
  background-color: #fff;
}

.info-icon {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.9);
  margin-right: 12rpx;
}

.info-text {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.9);
}

.center-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #26d1cb;
  margin-top: 4rpx;
}

.right-section {
  display: flex;
  align-items: center;
  z-index: 102;
  width: 160rpx;
  justify-content: flex-end;
}

.action-buttons {
  display: flex;
  gap: 10rpx;
}

.complete-btn,
.reset-btn {
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ddd;
}

.complete-btn {
  background-color: #26d1cb;
  border-color: #26d1cb;
}

.reset-btn {
  background-color: #f5f5f5;
}

.complete-text {
  font-size: 24rpx;
  color: #fff;
}

.reset-text {
  font-size: 24rpx;
  color: #666;
}

.more-icon,
.setting-icon {
  font-size: 30rpx;
  color: #333;
  margin-left: 20rpx;
}

/* 规则提示样式 */
.rule-tips {
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: flex-start;
}

.rule-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  margin-top: 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-icon {
  color: #2ac6c8;
  font-size: 36rpx;
}

.rule-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.rule-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 日历容器 */

/* 可滚动内容区域 */
.calendar-scroll {
  padding-top: 350rpx; /* 根据固定顶部区域的高度调整，建议比实际高度稍大一些 */
  flex: 1;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  padding-bottom: 300rpx; /* 为底部按钮和已选时间预留空间 */
}

/* 时间段样式 */
.time-slots-container {
  padding: 0;
  background-color: #ffffff;
  flex: 1;
  position: relative;
}

.time-slot-wrapper {
  position: relative;
}

.time-label-line {
  height: 1px;
  background-color: #f0f0f0;
  position: relative;
  margin-left: 100rpx;
}

.time-label-text {
  position: absolute;
  left: -100rpx;
  top: -12rpx;
  width: 100rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  padding: 0 8rpx;
}

.time-slot {
  height: 60rpx;
  position: relative;
  margin-left: 100rpx;
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}

.time-slot.unavailable {
  background-color: #f5f5f5;
}



.time-slot.selected {
  background-color: rgba(38, 209, 203, 0.1);
}

.time-slot.start-time {
  background-color: rgba(38, 209, 203, 0.2);
  border-left: 3px solid #26d1cb;
}

.time-slot.end-time {
  background-color: rgba(38, 209, 203, 0.2);
  border-right: 3px solid #26d1cb;
}

.time-slot.range-time {
  background-color: rgba(38, 209, 203, 0.15);
}

/* 移除拖拽相关样式 */

.unavailable-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx;
  color: #999;
  width: 100%;
  text-align: center;
}



.time-range-block {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-range-text {
  color: #26d1cb;
  font-size: 24rpx;
}

.start-text {
  color: #26d1cb;
  font-weight: bold;
  font-size: 26rpx;
}

.end-text {
  color: #26d1cb;
  font-weight: bold;
  font-size: 26rpx;
}

/* 操作按钮容器 */
.action-buttons {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  display: flex;
  gap: 6rpx;
  z-index: 10;
}

/* 右下角小取消按钮 */
.mini-cancel-btn {
  width: 56rpx !important;
  height: 26rpx !important;
  min-width: 56rpx !important;
  max-width: 56rpx !important;
  background-color: #f5f5f5 !important;
  border: 1rpx solid #ddd !important;
  border-radius: 13rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1) !important;
  padding: 0 !important;
  margin: 0 !important;
}

.mini-cancel-text {
  color: #666 !important;
  font-size: 16rpx !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  white-space: nowrap !important;
}

/* 右下角小确认按钮 */
.mini-confirm-btn {
  width: 56rpx !important;
  height: 26rpx !important;
  min-width: 56rpx !important;
  max-width: 56rpx !important;
  background-color: #26d1cb !important;
  border: none !important;
  border-radius: 13rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2rpx 4rpx rgba(38, 209, 203, 0.3) !important;
  padding: 0 !important;
  margin: 0 !important;
}

.mini-confirm-text {
  color: #fff !important;
  font-size: 16rpx !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  white-space: nowrap !important;
}

/* 底部面板 */
.bottom-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

/* 已选时间区域 */
.selected-times-section {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  max-height: 300rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.selected-header {
  margin-bottom: 10rpx;
}

.selected-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.selected-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.time-item {
  background-color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-text {
  flex: 1;
}

.delete-btn {
  color: #ff4757;
  font-size: 36rpx;
  font-weight: bold;
  padding: 0 10rpx;
  line-height: 1;
}

/* 底部按钮内容 */
.bottom-buttons-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20rpx;
  background-color: #ffffff;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.cancel-btn {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.9);
  border: 1rpx solid #e5e6eb;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #26d1cb;
  color: #fff;
}

.main-scroll-view {
  flex: 1;
  height: 100vh;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  background-color: #fff;
  padding-bottom: 300rpx; /* 为底部按钮和已选时间预留空间 */
}

.calendar-container {
  background-color: #fff;
}

/* 移除所有拖拽相关样式 */
</style>
