<template>
  <view class="reserve-container">
    <!-- 自定义导航栏 -->
    <u-navbar
      :title="instrumentName || '仪器预约'"
      :autoBack="false"
      @leftClick="handleNavbarBack"
      leftIcon="arrow-left"
      bgColor="#FFFFFF"
      :titleStyle="{ color: '#333', fontSize: '36rpx', fontWeight: 'bold' }"
    ></u-navbar>

    <!-- 内容区 -->
    <scroll-view
      scroll-y
      class="content-scroll"
      :style="{ paddingTop: uNavbarHeight + 'px' }"
    >
      <!-- 选项卡 -->
      <view class="tab-header">
        <view
          class="tab-item"
          :class="{ active: tabIndex === 0 }"
          @tap="switchTab(0)"
          >送样申请</view
        >
        <view
          class="tab-item"
          :class="{ active: tabIndex === 1 }"
          @tap="switchTab(1)"
          >预约检测</view
        >
      </view>

      <!-- 提示文本 -->
      <view class="tip-text">
        注：样品审核通过后会到打印清单页面中填写快递信息
      </view>

      <view class="form-content">
        <view class="form-module">
          <!-- 预约检测特有字段 -->
          <view v-if="tabIndex === 1" class="form-item required">
            <text class="form-label">操作形式</text>
            <view class="operation-mode-container">
              <view
                class="operation-mode"
                :class="{ active: formData.operationMode === 'engineer' }"
                @tap="selectOperationMode('engineer')"
              >
                <text>工程师操作</text>
              </view>
              <view
                class="operation-mode"
                :class="{ active: formData.operationMode === 'self' }"
                @tap="selectOperationMode('self')"
              >
                <text>自主操作</text>
              </view>
            </view>
          </view>

          <!-- 共享字段：实验名称 -->
          <view class="form-item required">
            <text class="form-label">实验名称</text>
            <input
              type="text"
              placeholder="请输入名称"
              class="form-input"
              v-model="formData.name"
            />
            <text v-if="tabIndex === 1" class="input-tip"
              >默认：客户手机号+序号 例：18812341234-1</text
            >
          </view>

          <!-- 共享字段：实验类型 -->
          <view class="form-item required">
            <text class="form-label">实验类型</text>
            <lk-list-select
              v-model="formData.type"
              :list="experimentTypeList || fallbackExperimentTypeList"
              :customStyle="{
                backgroundColor: '#F9FAFB',
                height: '96rpx',
                borderRadius: '16rpx',
              }"
              placeholder="请选择类型"
            ></lk-list-select>
            <text
              v-if="tabIndex === 1 && formData.operationMode === 'self'"
              class="type-warning"
              >当前仪器为首次自主操作，需培训通过后自由操作</text
            >
          </view>

          <!-- 预约检测特有字段：样品来源 -->
          <view v-if="tabIndex === 1" class="form-item required">
            <text class="form-label">样品</text>
            <view class="radio-group">
              <view
                class="radio-item"
                :class="{ active: !formData.sampleMailed }"
                @tap="handleSampleMailed(false)"
              >
                <view class="radio-dot">
                  <view
                    class="radio-inner"
                    v-if="!formData.sampleMailed"
                  ></view>
                </view>
                <text class="radio-text">自带</text>
              </view>
              <view
                class="radio-item"
                :class="{ active: formData.sampleMailed }"
                @tap="handleSampleMailed(true)"
              >
                <view class="radio-dot">
                  <view class="radio-inner" v-if="formData.sampleMailed"></view>
                </view>
                <text class="radio-text">邮寄</text>
              </view>
            </view>
            <text class="type-warning"
              >邮寄样品需审核通过后到订单详情页面中填写邮寄信息</text
            >
          </view>

          <!-- 共享字段：样品寄回 -->
          <view class="form-item required">
            <text class="form-label">样品寄回</text>
            <view class="radio-group">
              <view
                class="radio-item"
                :class="{ active: !formData.returnSample }"
                @tap="handleSampleReturn(false)"
              >
                <view class="radio-dot">
                  <view
                    class="radio-inner"
                    v-if="!formData.returnSample"
                  ></view>
                </view>
                <text class="radio-text">不寄回</text>
              </view>
              <view
                class="radio-item"
                :class="{ active: formData.returnSample }"
                @tap="handleSampleReturn(true)"
              >
                <view class="radio-dot">
                  <view class="radio-inner" v-if="formData.returnSample"></view>
                </view>
                <text class="radio-text">寄回</text>
              </view>
            </view>
          </view>
        </view>

        <view class="form-module">
          <!-- 共享字段：预约申请单 -->
          <view class="form-item">
            <text class="form-label">预约申请单</text>
            <view class="form-button-container">
              <view
                class="form-button"
                @tap="showApplicationForm"
                @click="showApplicationForm"
              >
                <text>点击查看/填写</text>
              </view>
              <view v-if="formData.appointForm !== instrumentDetail.appointForm" class="status-indicator filled">
                <text class="status-text">已填写</text>
              </view>
              <view v-else class="status-indicator empty">
                <text class="status-text">未填写</text>
              </view>
            </view>
          </view>

          <!-- 预约检测特有字段：预约时间 -->
          <view v-if="tabIndex === 1" class="form-item required">
            <text class="form-label">预约时间</text>
            <view class="form-button" @tap="goToTimeSelect">
              <text>点击查看/选择</text>
            </view>
          </view>

          <!-- 已选时间展示 -->
          <view
            v-if="
              tabIndex === 1 &&
              formData.appointmentTimes &&
              formData.appointmentTimes.length > 0
            "
            class="selected-times"
          >
            <text class="selected-title">已选</text>
            <view
              class="time-item"
              v-for="(time, timeIdx) in formData.appointmentTimes"
              :key="timeIdx"
            >
              <text>{{ time }}</text>
            </view>
            <view :style="{ height: '1px', backgroundColor: '#E5E5E5' }"></view>
            <view class="usage-time">
              <view class="usage-time-label">
                <text>实际消耗时间</text>
                <view class="info-icon" @tap="showTimeTooltip">
                  <lk-svg
                    src="/static/svg/info.svg"
                    width="32rpx"
                    height="32rpx"
                  ></lk-svg>
                </view>
              </view>
              <text class="time-value"
                >{{ actualConsumptionTime || 0 }} 小时</text
              >
            </view>
          </view>

          <!-- 共享字段：备注 -->
          <view class="form-item">
            <text class="form-label">备注</text>
            <textarea
              placeholder="请输入备注"
              class="form-textarea"
              v-model="formData.remarks"
            ></textarea>
          </view>
        </view>

        <!-- 材料和服务部分 -->
        <view class="form-module">
          <!-- 材料部分（仅预约检测显示） -->
          <view v-if="tabIndex === 1" class="form-label">耗材</view>
          <view
            v-if="tabIndex === 1"
            class="service-item"
            v-for="(material, index) in formData.materials"
            :key="index"
          >
            <view class="service-info">
              <text class="service-name">
                {{ material.name }} {{ material.price }}元/{{ material.unit }}
                <text v-if="material.isRequired" class="required-tag"
                  >必选</text
                >
              </text>
            </view>
            <view class="service-counter">
              <view
                class="counter-btn minus"
                :class="{
                  disabled: material.isRequired && material.count <= 1,
                }"
                @tap="handleMaterialMinus(index)"
                >-</view
              >
              <text class="counter-num">{{ material.count }}</text>
              <view class="counter-btn plus" @tap="handleMaterialPlus(index)"
                >+</view
              >
            </view>
          </view>
          <!-- 增值服务部分 -->
          <view class="form-label">增值服务</view>
          <view
            class="service-item"
            v-for="(service, index) in formData.services"
            :key="index"
          >
            <view class="service-info">
              <text class="service-name">
                {{ service.name }} {{ service.price }}元/{{ service.unit }}
                <text v-if="service.isRequired" class="required-tag">必选</text>
              </text>
            </view>
            <view class="service-counter">
              <view
                class="counter-btn minus"
                :class="{ disabled: service.isRequired && service.count <= 1 }"
                @tap="handleServiceMinus(index)"
                >-</view
              >
              <text class="counter-num">{{ service.count }}</text>
              <view class="counter-btn plus" @tap="handleServicePlus(index)"
                >+</view
              >
            </view>
          </view>
        </view>

        <!-- 价格信息部分 -->
        <view class="form-module" v-if="tabIndex === 0">
          <view class="order-info">
            <view class="info-item">
              <text class="info-label">预计交付时间</text>
              <text class="info-value">{{ getDeliveryTimeText() }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">仪器收费方式</text>
              <text class="info-value">{{ getInstrumentFeeText() }}</text>
            </view>
          </view>
        </view>
        <view class="form-module" v-if="tabIndex === 1">
          <view class="price-info">
            <view class="price-item">
              <text class="form-label">培训收费方式</text>
              <text class="price-value">{{ getTrainingFeeText() }}</text>
            </view>
          </view>
        </view>

        <view class="form-module">
          <view class="price-info">
            <view class="price-item">
              <view class="price-label-container">
                <text class="form-label">预估金额</text>
                <lk-svg
                  src="/static/svg/info.svg"
                  width="44rpx"
                  height="44rpx"
                ></lk-svg>
              </view>
              <text class="price-value">¥{{ originalPrice.toFixed(1) }}</text>
            </view>
            <view class="price-item">
              <view class="price-label-container">
                <text class="form-label">折扣金额</text>
                <lk-svg
                  src="/static/svg/info.svg"
                  width="44rpx"
                  height="44rpx"
                ></lk-svg>
              </view>
              <text class="price-value discount"
                >¥{{ discountedPrice.toFixed(1) }}</text
              >
            </view>
          </view>

          <view class="total-price">
            <text class="subtotal-label">小计:</text>
            <text class="subtotal-value"
              >¥{{ discountedPrice.toFixed(1) }}</text
            >
          </view>
        </view>

        <!-- 支付方式部分 -->
        <view class="form-module" v-if="!fromDemand">
          <view class="payment-method required">
            <text class="form-label">支付方式</text>
            <view class="payment-options">
              <!-- 动态循环支付方式 -->
              <view
                v-for="(method, index) in paymentMethods"
                :key="index"
                class="payment-option"
                :class="{ active: formData.paymentMethodIndex === index }"
                @tap="selectPayment(index)"
              >
                <view
                  class="check-icon"
                  v-if="formData.paymentMethodIndex === index"
                >
                  <lk-svg
                    src="/static/svg/pay_check.svg"
                    width="28rpx"
                    height="28rpx"
                  ></lk-svg>
                </view>
                <view class="payment-left">
                  <view class="payment-tag">
                    <text>{{ getPaymentMethodName(method) }}</text>
                  </view>
                  <view class="balance-text">
                    当前可用余额: {{ method.balance.toFixed(2) }} 元
                  </view>
                </view>
                <view class="payment-right">
                  <text
                    class="discount-text"
                    :class="{ discount: method.discount < 1 }"
                  >
                    {{ getDiscountText(method.discount) }}
                  </text>
                </view>
              </view>

              <!-- 无支付方式时的提示 -->
              <view v-if="paymentMethods.length === 0" class="no-payment-tip">
                <text>暂无可用的支付方式</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons" :class="{ 'demand-mode': fromDemand }">
      <view class="cancel-btn" @tap="handleCancel">取消</view>
      <!-- 需求订单模式下不显示暂时保存按钮 -->
      <view v-if="!fromDemand" class="save-btn" @tap="handleSave">
        <text>暂时保存</text>
        <view v-if="hasTemporarySaved" class="save-status">
          <text class="save-status-text">✓ 已保存</text>
        </view>
      </view>
      <view class="submit-btn" @tap="handleSubmit">
        {{ fromDemand ? '确定' : '提交' }}
      </view>
    </view>

    <!-- 开发测试按钮（可在生产环境移除） -->
    <!-- <view class="test-buttons" style="position: fixed; top: 200rpx; right: 20rpx; z-index: 999;">
      <view class="test-btn" @tap="testSnapshot" style="background: #ff6b6b; color: white; padding: 10rpx; margin: 5rpx; border-radius: 8rpx; font-size: 24rpx;">
        测试快照
      </view>
    </view> -->
  </view>
</template>

<script>
// 引入uView的组件和项目组件
import lkSelect from "@/components/lk-select/lk-select.vue";
import lkListSelect from "@/components/lk-list-select/lk-list-select.vue";
import lkSvg from "@/components/lk-svg/lk-svg.vue";

// 引入接口和常量
import instrumentApi from "@/api/instrument.js";
import orderApi from "@/api/order.js";
import { OrderType, PayType, SubmitStatus } from "@/constants/order.js";
import { mapState } from "vuex";

export default {
  components: {
    lkSelect,
    lkListSelect,
    lkSvg,
  },

  data() {
    return {
      tabIndex: 0, // 默认选中送样申请
      instrumentId: "",
      instrumentName: "",
      fromDemand: false, // 是否来自需求订单

      // 加载状态
      loading: false,
      instrumentLoading: false,
      paymentLoading: false,
      trainingCheckLoading: false,

      // 培训状态
      needTraining: false,
      trainingReason: "",

      // 备用实验类型列表（防止计算属性失败）
      fallbackExperimentTypeList: [
        {
          label: "试测",
          value: 2,
        },
        {
          label: "正式",
          value: 3,
        },
      ],

      // 仪器详情数据
      instrumentDetail: null,

      // 支付方式列表
      paymentMethods: [],

      // 可用的订单类型
      availableOrderTypes: [],
      // 统一的表单数据，去除冗余
      formData: {
        // 共享字段
        name: "", // 实验名称
        type: "", // 实验类型
        returnSample: false, // 样品寄回
        remarks: "", // 备注
        paymentMethodIndex: -1, // 支付方式索引

        // 预约检测特有字段
        operationMode: "self", // 操作形式
        sampleMailed: false, // 样品邮寄（预约检测中的"样品"字段）
        appointmentTimes: [], // 预约时间
        estimatedTime: 2, // 实验预计时间

        // 材料（仅预约检测）
        materials: [],

        // 增值服务（两种模式不同）
        services: [],

        // 预约申请单（富文本内容）
        appointForm: "",
      },

      // 价格相关
      originalPrice: 2000, // 原价
      discountedPrice: 1400, // 折扣后价格

      // 余额信息
      personalBalance: "00.00", // 个人余额
      teamCreditBalance: "00.00", // 松山湖团队额度
      teamBalance: "00.00", // 松山湖团队余额
      otherTeamCreditBalance: "00.00", // XXX团队额度
      otherTeamBalance: "00.00", // XXX团队余额

      // 快照相关
      formSnapshot: null, // 表单快照
      hasTemporarySaved: false, // 是否已暂时保存
      lastSaveTime: null, // 最后保存时间

      // 导航栏高度
    };
  },
  onLoad(options) {
    if (options.instrumentId) {
      this.instrumentId = options.instrumentId;
    }
    if (options.instrumentName) {
      // 解码URL编码的仪器名称
      this.instrumentName = decodeURIComponent(options.instrumentName);
      // 设置页面标题
      uni.setNavigationBarTitle({
        title: this.instrumentName,
      });
    }
    if (options.fromDemand) {
      this.fromDemand = options.fromDemand === '1';
      console.log('来自需求订单模式:', this.fromDemand);
    }

    // 检查必要参数
    if (!this.instrumentId) {
      uni.showToast({
        title: "缺少仪器ID参数",
        icon: "none",
      });
      return;
    }

    // 初始化数据
    this.initData();

    // 创建初始快照
    this.createFormSnapshot();

    // 获取导航栏高度

    // 添加测试按钮（开发时使用）
    console.log("页面加载完成，可以开始测试快照功能");
  },
  onShow() {
    // 监听从timeSelect返回的预约时间
    uni.$off("selectReserveTime"); // 避免重复监听
    uni.$on("selectReserveTime", (result) => {
      if (result && result.selectedTimes) {
        // 更新预约时间列表
        this.formData.appointmentTimes = [...result.selectedTimes];

        // 重新计算价格
        this.recalculatePrice();

        uni.showToast({
          title: `已选择${result.selectedTimes.length}个时间段`,
          icon: "success",
        });
      }
    });

    // 监听从申请单编辑器返回的内容
    uni.$off("saveAppointForm"); // 避免重复监听
    uni.$on("saveAppointForm", (result) => {
      if (result && result.content !== undefined) {
        // 更新申请单内容
        this.formData.appointForm = result.content;

        uni.showToast({
          title: "申请单已保存",
          icon: "success",
        });
      }
    });
  },
  onBackPress() {
    // 拦截返回操作
    console.log("onBackPress 被触发");
    this.handleBackPress();
    return true; // 阻止默认返回行为
  },
  methods: {
    async initData() {
      // 获取仪器详情，需求订单模式下不获取支付方式
      const promises = [this.fetchInstrumentDetail()];

      if (!this.fromDemand) {
        promises.push(this.fetchPaymentMethods());
      }

      await Promise.all(promises);

      // 根据仪器详情初始化可用的订单类型和服务
      this.initAvailableOrderTypes();
      this.updateServicesForTab();

      // 检查是否需要培训（仅自主操作时）
      this.checkTrainingIfNeeded();

      // 生成默认实验名称
      await this.generateDefaultExperimentName();

      // 初始化计算价格
      this.recalculatePrice();
    },

    // 获取仪器详情
    async fetchInstrumentDetail() {
      try {
        this.instrumentLoading = true;

        const data = await instrumentApi.getInstrumentDetail({
          id: parseInt(this.instrumentId),
          tmbId: 0,
        });

        if (data) {
          this.instrumentDetail = data;

          // 更新页面标题
          if (data.name && !this.instrumentName) {
            this.instrumentName = data.name;
            uni.setNavigationBarTitle({
              title: data.name,
            });
          }
        }
      } catch (error) {
        console.error("获取仪器详情失败:", error);
        uni.showToast({
          title: error.msg || "获取仪器详情失败",
          icon: "none",
        });
      } finally {
        this.instrumentLoading = false;
      }
    },

    // 获取支付方式列表
    async fetchPaymentMethods() {
      try {
        this.paymentLoading = true;

        const data = await orderApi.getUserPayment();

        if (data && Array.isArray(data)) {
          this.paymentMethods = data;

          // 更新余额显示
          this.updateBalanceDisplay(data);
        }
      } catch (error) {
        console.error("获取支付方式失败:", error);
        uni.showToast({
          title: error.msg || "获取支付方式失败",
          icon: "none",
        });
      } finally {
        this.paymentLoading = false;
      }
    },

    // 更新余额显示（现在不需要单独的余额变量，直接使用 paymentMethods 数组）
    updateBalanceDisplay(paymentMethods) {
      // 如果有支付方式，默认选择第一个
      if (
        paymentMethods.length > 0 &&
        this.formData.paymentMethodIndex === -1
      ) {
        this.formData.paymentMethodIndex = 0;
      }
    },

    // 初始化可用的订单类型
    initAvailableOrderTypes() {
      if (!this.instrumentDetail || !this.instrumentDetail.orderTypes) {
        return;
      }

      // orderTypes 是逗号分隔的字符串，如 "1,2,3"
      const orderTypeIds = this.instrumentDetail.orderTypes
        .split(",")
        .map((id) => parseInt(id.trim()));

      this.availableOrderTypes = orderTypeIds.filter((id) => {
        return (
          id === OrderType.SAMPLE_SUBMISSION ||
          id === OrderType.INSTRUMENT_RESERVATION_ENGINEER ||
          id === OrderType.INSTRUMENT_RESERVATION_SELF
        );
      });

      // 根据可用的订单类型调整标签页显示
      this.updateTabVisibility();
    },

    // 更新标签页可见性
    updateTabVisibility() {
      // 如果只支持送样申请，隐藏预约检测标签
      if (
        this.availableOrderTypes.length === 1 &&
        this.availableOrderTypes[0] === OrderType.SAMPLE_SUBMISSION
      ) {
        // 可以在这里添加逻辑来隐藏预约检测标签
      }
      // 如果只支持预约检测，隐藏送样申请标签
      else if (
        this.availableOrderTypes.length > 0 &&
        !this.availableOrderTypes.includes(OrderType.SAMPLE_SUBMISSION)
      ) {
        this.tabIndex = 1; // 默认选中预约检测
      }
    },

    updateServicesForTab() {
      // 使用仪器详情中的真实数据
      if (this.instrumentDetail) {
        // 更新增值服务
        if (this.instrumentDetail.incrementServices) {
          this.formData.services = this.instrumentDetail.incrementServices.map(
            (service) => ({
              id: service.id,
              name: service.name,
              price: service.price,
              unit: service.unit,
              count: service.isRequired ? 1 : 0, // 必选服务默认数量为1
              isRequired: service.isRequired,
            })
          );
        }

        // 更新材料（仅预约检测）
        if (this.tabIndex === 1 && this.instrumentDetail.consumables) {
          this.formData.materials = this.instrumentDetail.consumables.map(
            (material) => ({
              id: material.id,
              name: material.name,
              price: material.price,
              unit: material.unit,
              count: material.isRequired ? 1 : 0, // 必选材料默认数量为1
              isRequired: material.isRequired,
            })
          );
        } else {
          this.formData.materials = [];
        }

        // 表单
        this.formData.appointForm = this.instrumentDetail.appointForm;
      } else {
        // 如果没有仪器详情，使用默认数据
        if (this.tabIndex === 0) {
          // 送样申请的服务
          this.formData.services = [
            {
              id: 1,
              name: "服务必选",
              price: 500,
              unit: "次",
              count: 1,
              isRequired: true,
            },
            {
              id: 2,
              name: "服务2",
              price: 500,
              unit: "个",
              count: 0,
              isRequired: false,
            },
            {
              id: 3,
              name: "服务3",
              price: 500,
              unit: "个",
              count: 0,
              isRequired: false,
            },
          ];
        } else {
          // 预约检测的服务
          this.formData.services = [
            {
              id: 1,
              name: "培训服务",
              price: 1000,
              unit: "次",
              count: 0,
              isRequired: false,
            },
            {
              id: 2,
              name: "加急服务",
              price: 800,
              unit: "次",
              count: 0,
              isRequired: false,
            },
          ];
        }
      }
    },

    switchTab(tab) {
      this.tabIndex = tab;
      this.formData.type = undefined;
      // 切换标签时更新服务列表
      this.updateServicesForTab();
      // 重新计算价格
      this.recalculatePrice();
      this.checkTrainingIfNeeded();
    },

    // 操作模式选择
    async selectOperationMode(mode) {
      this.formData.operationMode = mode;

      // 如果切换到自主操作，检查是否需要培训
      if (mode === "self") {
        await this.checkTrainingIfNeeded();
      } else {
        // 切换到工程师操作时，重置培训状态
        this.needTraining = false;
        this.trainingReason = "";
      }

      // 重置实验类型选择
      this.formData.type = "";
    },

    // 样品寄回处理
    handleSampleReturn(value) {
      this.formData.returnSample = value;
    },

    // 样品邮寄处理（预约检测特有）
    handleSampleMailed(value) {
      this.formData.sampleMailed = value;
    },

    // 材料数量处理
    handleMaterialMinus(index) {
      const materials = this.formData.materials;
      if (!materials || materials.length <= index) return;

      const material = materials[index];
      if (material && material.count > 0) {
        // 如果是必选项目，不能减少到0
        if (material.isRequired && material.count <= 1) {
          uni.showToast({
            title: "必选项目数量不能少于1",
            icon: "none",
          });
          return;
        }
        material.count--;
        this.recalculatePrice();
      }
    },

    handleMaterialPlus(index) {
      const materials = this.formData.materials;
      if (!materials || materials.length <= index) return;

      const material = materials[index];
      if (material) {
        material.count++;
        this.recalculatePrice();
      }
    },

    // 服务数量处理
    handleServiceMinus(index) {
      const services = this.formData.services;
      if (!services || services.length <= index) return;

      const service = services[index];
      if (service && service.count > 0) {
        // 如果是必选项目，不能减少到0
        if (service.isRequired && service.count <= 1) {
          uni.showToast({
            title: "必选项目数量不能少于1",
            icon: "none",
          });
          return;
        }
        service.count--;
        this.recalculatePrice();
      }
    },

    handleServicePlus(index) {
      const services = this.formData.services;
      if (!services || services.length <= index) return;

      const service = services[index];
      if (service) {
        service.count++;
        this.recalculatePrice();
      }
    },

    // 支付方式选择
    selectPayment(index) {
      console.log("选择支付方式:", index, this.paymentMethods[index]);
      this.formData.paymentMethodIndex = index;
      this.recalculatePrice();
    },

    // 获取支付方式名称
    getPaymentMethodName(method) {
      // 根据支付类型和团队名称生成显示名称
      switch (method.payType) {
        case PayType.PERSONAL_ACCOUNT:
          return "个人账户余额";
        case PayType.TEAM_BALANCE:
          return `${method.teamName}余额`;
        case PayType.TEAM_CREDIT:
          return `${method.teamName}额度`;
        default:
          return "未知支付方式";
      }
    },

    // 获取折扣文本
    getDiscountText(discount) {
      if (discount === 1 || !discount) {
        return "无折扣";
      } else {
        // 将折扣转换为百分比显示
        const percentage = Math.round(discount * 100);
        return `可打${percentage}折`;
      }
    },

    // 检查是否需要培训（仅自主操作时）
    async checkTrainingIfNeeded() {
      // 只有在预约检测且选择自主操作时才需要检查培训

      if (this.tabIndex === 1 && this.formData.operationMode === "self") {
        try {
          this.trainingCheckLoading = true;

          const userId = this.getCurrentUserId();
          if (!userId) {
            console.warn("用户ID不存在，跳过培训检查");
            return;
          }

          const result = await orderApi.checkTrainingNeeded({
            instrumentId: parseInt(this.instrumentId),
            userId: userId,
          });

          if (result) {
            this.needTraining = result.needTraining || false;
            this.trainingReason = result.reason || "";

            // 如果需要培训，自动设置实验类型为培训
            if (this.needTraining) {
              this.formData.type = 1; // 培训类型值为1
            }
          }
        } catch (error) {
          console.error("检查培训状态失败:", error);
          // 检查失败时，为安全起见，假设需要培训
          this.needTraining = true;
          this.trainingReason = "无法获取培训状态，请联系管理员";
        } finally {
          this.trainingCheckLoading = false;
        }
      } else {
        // 非自主操作时，不需要培训
        this.needTraining = false;
        this.trainingReason = "";
      }
    },

    // 获取当前订单类型
    getCurrentOrderType() {
      if (this.tabIndex === 0) {
        return 1; // 1 = SAMPLE_SUBMISSION
      } else {
        return this.formData.operationMode === "engineer"
          ? 2 // 2 = INSTRUMENT_RESERVATION_ENGINEER
          : 3; // 3 = INSTRUMENT_RESERVATION_SELF
      }
    },

    // 获取当前用户ID
    getCurrentUserId() {
      // 从store中获取用户ID，如果没有则返回默认值
      return this.userInfo?.id || this.userInfo?.userId || 1;
    },

    // 价格重新计算
    async recalculatePrice() {
      // 调用预估价格接口
      await this.fetchEstimatePrice();
    },

    // 生成默认实验名称
    async generateDefaultExperimentName() {
      try {
        const userId = this.getCurrentUserId();
        if (!userId || !this.instrumentId) {
          console.warn("缺少必要参数，跳过生成实验名称");
          return;
        }

        const result = await orderApi.generateExperimentName({
          instrumentId: parseInt(this.instrumentId),
          userId: userId,
        });

        if (result && result.experimentName) {
          // 只有当前实验名称为空时才设置默认名称
          if (!this.formData.name) {
            this.formData.name = result.experimentName;
          }
        }
      } catch (error) {
        console.error("生成默认实验名称失败:", error);
        // 生成失败不影响正常流程，只是不设置默认名称
      }
    },

    // 获取预估价格
    async fetchEstimatePrice() {
      try {
        const userId = this.getCurrentUserId();
        if (!userId || !this.instrumentId) {
          console.warn("缺少必要参数，跳过价格估算");
          return;
        }

        // 构建预估价格请求参数
        const estimateRequest = {
          instrumentId: parseInt(this.instrumentId),
          userId: userId,
          type: this.getCurrentOrderType(),
          actualDuration: this.actualConsumptionTime || 0,

          // 耗材列表（仅预约检测）
          consumables:
            this.tabIndex === 1 && this.formData.materials
              ? this.formData.materials
                  .filter((material) => material.count > 0)
                  .map((material) => ({
                    id: material.id,
                    name: material.name,
                    num: material.count,
                    orderId: 0, // 新订单暂时为0
                    price: material.price,
                    unit: material.unit,
                  }))
              : [],

          // 增值服务列表
          incrementServices: this.formData.services
            ? this.formData.services
                .filter((service) => service.count > 0)
                .map((service) => ({
                  incrementServiceId: service.id,
                  name: service.name,
                  num: service.count,
                  price: service.price,
                  unit: service.unit,
                }))
            : [],
        };

        // 添加团队ID（个人账户传0，团队账户传实际teamId）
        const teamId = this.getTeamId();
        estimateRequest.teamId = teamId || 0;

        console.log("预估价格请求参数:", estimateRequest);

        const result = await orderApi.estimatePrice(estimateRequest);

        if (result) {
          // 更新价格显示
          this.originalPrice = result.estimatePrice || 0;
          this.discountedPrice =
            result.discountPrice || result.estimatePrice || 0;

          console.log("预估价格结果:", {
            原价: this.originalPrice,
            折扣后: this.discountedPrice,
            折扣率: result.discountRate,
          });
        }
      } catch (error) {
        console.error("获取预估价格失败:", error);
        // 价格估算失败时，使用本地计算作为备用
        this.fallbackPriceCalculation();
      }
    },

    // 备用价格计算（当接口调用失败时使用）
    fallbackPriceCalculation() {
      let basePrice = 0;
      let serviceTotal = 0;
      let materialTotal = 0;

      // 获取当前订单类型
      const currentOrderType = this.getCurrentOrderType();

      // 检查是否是培训模式
      const isTrainingMode =
        this.needTraining &&
        currentOrderType === 3 && // 3 = INSTRUMENT_RESERVATION_SELF
        this.formData.type === 1; // 培训类型值为1

      if (this.tabIndex === 0) {
        // 送样申请价格计算
        basePrice = 2000;

        // 计算服务费用
        if (this.formData.services) {
          this.formData.services.forEach((service) => {
            serviceTotal += service.price * service.count;
          });
        }

        this.originalPrice = basePrice + serviceTotal;
      } else {
        // 预约检测价格计算

        // 如果是培训模式，使用培训费用
        if (
          isTrainingMode &&
          this.instrumentDetail &&
          this.instrumentDetail.trainFee
        ) {
          basePrice = this.instrumentDetail.trainFee;
        } else {
          basePrice = 1000; // 默认基础价格
        }

        // 计算服务费用
        if (this.formData.services) {
          this.formData.services.forEach((service) => {
            serviceTotal += service.price * service.count;
          });
        }

        // 计算材料费用
        if (this.formData.materials) {
          this.formData.materials.forEach((material) => {
            materialTotal += material.price * material.count;
          });
        }

        this.originalPrice = basePrice + serviceTotal + materialTotal;
      }

      // 根据选择的支付方式计算折扣
      let discount = 1.0;

      // 查找选中的支付方式
      if (
        this.formData.paymentMethodIndex >= 0 &&
        this.paymentMethods.length > this.formData.paymentMethodIndex
      ) {
        const selectedMethod =
          this.paymentMethods[this.formData.paymentMethodIndex];

        if (selectedMethod && selectedMethod.discount) {
          discount = selectedMethod.discount;
        }
      }

      this.discountedPrice = this.originalPrice * discount;
    },

    // 显示申请表单
    showApplicationForm() {
      console.log("点击了预约申请单按钮");
      console.log("当前申请单内容:", this.formData.appointForm);
      console.log("仪器ID:", this.instrumentId);

      // 将当前的申请单内容保存到本地缓存
      uni.setStorageSync("appointForm", this.formData.appointForm || "");

      // 同时保存一些基础信息供编辑器使用
      uni.setStorageSync("appointFormContext", {
        instrumentId: this.instrumentId,
        instrumentName: this.instrumentName,
        experimentName: this.formData.name,
        experimentType: this.formData.type,
      });

      // 直接跳转到 H5 富文本编辑器页面，不传递 URL 参数
      this.navTo('/pages/instrument/appointFormEditor')

    },

    // 跳转到时间选择页面
    goToTimeSelect() {
      const instrumentName = this.instrumentName || "仪器预约";

      // 构建跳转参数
      const params = {
        title: encodeURIComponent(instrumentName),
        instrumentId: this.instrumentId,
      };

      // 如果有已选时间，传递给时间选择页面
      if (
        this.formData.appointmentTimes &&
        this.formData.appointmentTimes.length > 0
      ) {
        console.log('传递已选时间到时间选择页面:', this.formData.appointmentTimes);
        params.selectedDates = encodeURIComponent(
          JSON.stringify(this.formData.appointmentTimes)
        );
      }

      // 构建URL参数
      const queryString = Object.keys(params)
        .map((key) => `${key}=${params[key]}`)
        .join("&");

      uni.navigateTo({
        url: `/pages/instrument/timeSelect?${queryString}`,
      });
    },

    // 显示时间提示
    showTimeTooltip() {
      uni.showToast({
        title:
          "默认为预约时长，后续负责人将按照实际使用时间修改，如有疑问请联系负责人",
        icon: "none",
        duration: 3000,
      });
    },

    // 导航栏返回按钮处理
    handleNavbarBack() {
      console.log("导航栏返回按钮被点击");
      this.handleBackPress();
    },

    // 底部按钮处理
    handleCancel() {
      this.handleBackPress();
    },

    // 处理返回操作
    handleBackPress() {
      console.log("🔙 处理返回操作");

      // 检查是否有未保存的更改
      const hasChanges = this.hasUnsavedChanges();
      console.log("📝 是否有未保存的更改:", hasChanges);

      if (hasChanges) {
        // 如果有未保存的更改，提示用户
        console.log("💾 显示保存提示对话框");
        uni.showModal({
          title: "提示",
          content: "您有未保存的更改，是否要暂时保存？",
          confirmText: "暂时保存",
          cancelText: "直接离开",
          success: (res) => {
            if (res.confirm) {
              // 用户选择暂时保存
              console.log("✅ 用户选择暂时保存");
              this.handleSave()
                .then(() => {
                  uni.navigateBack();
                })
                .catch(() => {
                  // 保存失败也允许返回
                });
            } else {
              // 用户选择直接离开
              console.log("❌ 用户选择直接离开");
              uni.navigateBack();
            }
          },
        });
      } else {
        // 没有未保存的更改，直接返回
        console.log("✨ 没有未保存的更改，直接返回");
        uni.navigateBack();
      }
    },

    // 检查是否有未保存的更改
    hasUnsavedChanges() {
      console.log("🔍 检查是否有未保存的更改");

      if (!this.formSnapshot) {
        console.log("📷 没有快照，返回 false");
        return false;
      }

      const currentFormData = this.getCurrentFormData();
      const snapshotData = this.formSnapshot;

      // 简单比较关键字段
      const keyFields = ["name", "type", "remarks", "appointForm"];
      let hasChanges = false;

      for (let field of keyFields) {
        if (currentFormData[field] !== snapshotData[field]) {
          console.log(`🔄 字段 ${field} 发生变化:`, {
            当前: currentFormData[field],
            快照: snapshotData[field],
          });
          hasChanges = true;
        }
      }

      // 检查数组字段
      if (
        currentFormData.appointmentTimes.length !==
        snapshotData.appointmentTimes.length
      ) {
        console.log("🕐 预约时间数量发生变化");
        hasChanges = true;
      }

      console.log("📊 最终结果 - 是否有更改:", hasChanges);
      return hasChanges;
    },

    // 获取当前表单数据
    getCurrentFormData() {
      return {
        name: this.formData.name,
        type: this.formData.type,
        returnSample: this.formData.returnSample,
        remarks: this.formData.remarks,
        appointForm: this.formData.appointForm,
        appointmentTimes: [...this.formData.appointmentTimes],
        services: this.formData.services.map((service) => ({
          id: service.id,
          count: service.count,
        })),
        paymentMethodIndex: this.formData.paymentMethodIndex,
      };
    },

    // 创建表单快照
    createFormSnapshot() {
      this.formSnapshot = this.getCurrentFormData();
      console.log("创建表单快照:", this.formSnapshot);
      console.log("快照创建时间:", new Date().toLocaleTimeString());
    },

    // 深度比较两个对象
    deepEqual(obj1, obj2) {
      if (obj1 === obj2) {
        return true;
      }

      if (obj1 == null || obj2 == null) {
        return obj1 === obj2;
      }

      if (typeof obj1 !== typeof obj2) {
        return false;
      }

      if (typeof obj1 !== "object") {
        return obj1 === obj2;
      }

      if (Array.isArray(obj1) !== Array.isArray(obj2)) {
        return false;
      }

      if (Array.isArray(obj1)) {
        if (obj1.length !== obj2.length) {
          return false;
        }
        for (let i = 0; i < obj1.length; i++) {
          if (!this.deepEqual(obj1[i], obj2[i])) {
            return false;
          }
        }
        return true;
      }

      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);

      if (keys1.length !== keys2.length) {
        return false;
      }

      for (let key of keys1) {
        if (!keys2.includes(key)) {
          return false;
        }
        if (!this.deepEqual(obj1[key], obj2[key])) {
          return false;
        }
      }

      return true;
    },

    // 暂时保存（草稿）- 不需要校验
    async handleSave() {
      try {
        this.loading = true;

        // 构建订单数据（草稿状态，不需要校验）
        const orderData = this.buildOrderData(SubmitStatus.DRAFT);

        // 调用创建订单接口
        const result = await orderApi.createOrder(orderData);

        if (result) {
          // 暂时保存成功，更新快照和状态
          this.createFormSnapshot();
          this.hasTemporarySaved = true;
          this.lastSaveTime = new Date();

          uni.showToast({
            title: "暂时保存成功",
            icon: "success",
          });
        }
      } catch (error) {
        console.error("暂时保存失败:", error);
        uni.showToast({
          title: error.msg || "保存失败，请重试",
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },

    // 提交订单
    async handleSubmit() {
      if (this.fromDemand) {
        // 需求订单模式：返回预约数据
        this.returnToDemandOrder();
      } else {
        // 普通模式：提交订单
        await this.submitOrder(SubmitStatus.SUBMIT);
      }
    },

    // 返回需求订单页面
    returnToDemandOrder() {
      // 表单验证
      const validationResult = this.validateForm();
      if (!validationResult.isValid) {
        uni.showToast({
          title: validationResult.message,
          icon: "none",
        });
        return;
      }

      // 构建预约数据
      const reservationData = this.buildReservationData();

      console.log('返回需求订单页面，预约数据:', reservationData);

      // 通过事件总线传递预约数据
      uni.$emit('addDemandInstrument', reservationData);

      // 显示成功提示
      uni.showToast({
        title: '预约信息已添加',
        icon: 'success',
        duration: 1500
      });

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        // 连续返回两页：先返回到仪器选择页面，再返回到需求页面
        const pages = getCurrentPages();
        if (pages.length >= 3) {
          // 如果页面栈中有足够的页面，直接返回到需求页面（跳过仪器选择页面）
          uni.navigateBack({
            delta: 2
          });
        } else {
          // 否则只返回一页
          uni.navigateBack();
        }
      }, 1500);
    },

    // 构建预约数据（用于需求订单）
    buildReservationData() {
      const orderType =
        this.tabIndex === 0
          ? OrderType.SAMPLE_SUBMISSION
          : this.formData.operationMode === "engineer"
          ? OrderType.INSTRUMENT_RESERVATION_ENGINEER
          : OrderType.INSTRUMENT_RESERVATION_SELF;

      return {
        // 基础信息
        instrumentId: parseInt(this.instrumentId),
        instrumentName: this.instrumentName,
        experimentName: this.formData.name,
        experimentType: this.getExperimentTypeValue(),
        type: orderType,
        remark: this.formData.remarks,

        // 时间相关
        appointmentTimes: [...this.formData.appointmentTimes],
        actualDuration: this.actualConsumptionTime,

        // 服务和材料
        services: this.formData.services ? this.formData.services
          .filter(service => service.count > 0)
          .map(service => ({
            id: service.id,
            name: service.name,
            num: service.count,
            price: service.price,
            unit: service.unit
          })) : [],

        materials: this.formData.materials ? this.formData.materials
          .filter(material => material.count > 0)
          .map(material => ({
            id: material.id,
            name: material.name,
            num: material.count,
            price: material.price,
            unit: material.unit
          })) : [],

        // 价格信息
        totalAmount: this.originalPrice,
        amount: this.discountedPrice,

        // 其他信息
        sampleReturnable: this.formData.returnSample ? 1 : 0,
        appointForm: this.formData.appointForm || '',

        // 预约时间段（用于API）
        instruments: this.buildInstrumentData()
      };
    },

    // 统一的订单提交方法
    async submitOrder(status) {
      // 表单验证
      const validationResult = this.validateForm();
      if (!validationResult.isValid) {
        uni.showToast({
          title: validationResult.message,
          icon: "none",
        });
        return;
      }

      try {
        this.loading = true;

        // 构建订单数据
        const orderData = this.buildOrderData(status);

        // 调用创建订单接口
        const result = await orderApi.createOrder(orderData);

        if (status === SubmitStatus.SUBMIT) {
            // 正式提交成功
            uni.showToast({
              title: "提交成功",
              icon: "success",
            });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }

          return true; // 返回成功状态
      } catch (error) {
        console.error("提交订单失败:", error);
        uni.showToast({
          title: error.msg || "提交失败，请重试",
          icon: "none",
        });
        return false;
      } finally {
        this.loading = false;
      }
    },

    // 表单验证
    validateForm() {
      // 共享字段验证
      if (!this.formData.name) {
        return { isValid: false, message: "请输入实验名称" };
      }

      if (!this.formData.type) {
        return { isValid: false, message: "请选择实验类型" };
      }

      // 预约检测特有验证
      if (this.tabIndex === 1 && this.formData.appointmentTimes.length === 0) {
        return { isValid: false, message: "请选择预约时间" };
      }

      // 支付方式验证（需求订单模式下不验证）
      if (!this.fromDemand) {
        console.log("验证支付方式索引:", this.formData.paymentMethodIndex);
        console.log("可用支付方式:", this.paymentMethods);
        if (
          this.formData.paymentMethodIndex === -1 ||
          this.paymentMethods.length === 0
        ) {
          return { isValid: false, message: "请选择支付方式" };
        }
      }

      return { isValid: true };
    },

    // 构建订单数据
    buildOrderData(status) {
      const orderType =
        this.tabIndex === 0
          ? OrderType.SAMPLE_SUBMISSION
          : this.formData.operationMode === "engineer"
          ? OrderType.INSTRUMENT_RESERVATION_ENGINEER
          : OrderType.INSTRUMENT_RESERVATION_SELF;

      // 基础订单数据
      const orderData = {
        experimentName: this.formData.name,
        experimentType: this.getExperimentTypeValue(),
        instrumentId: this.instrumentId,
        type: orderType,
        payType: this.getPayTypeValue(),
        sampleReturnable: this.formData.returnSample ? 1 : 0,
        status: status,
        totalAmount: this.originalPrice || this.discountedPrice, // 原价
        amount: this.discountedPrice, // 折后价格
        remark: this.formData.remarks || "",
        userId: this.getCurrentUserId(),
        appointForm: this.formData.appointForm || "", // 预约申请单
        actualDuration: this.actualConsumptionTime || 0, // 实际消耗时长
        shipStatus:this.formData.sampleMailed?1:0,
        // 增值服务
        incrementServices: this.formData.services
          .filter((service) => service.count > 0)
          .map((service) => ({
            incrementServiceId: service.id,
            name: service.name,
            num: service.count,
            price: service.price,
            unit: service.unit,
          })),

        // 仪器信息（预约检测需要）
        instruments: this.tabIndex === 1 ? this.buildInstrumentData() : [],
      };

      // 添加团队ID（个人账户传0，团队账户传实际teamId）
      const teamId = this.getTeamId();
      orderData.teamId = teamId || 0;

      // 耗材（仅预约检测）
      if (this.tabIndex === 1 && this.formData.materials) {
        orderData.consumables = this.formData.materials
          .filter((material) => material.count > 0)
          .map((material) => ({
            id: material.id,
            name: material.name,
            price: material.price,
            unit: material.unit,
            num: material.count, // 使用num字段
          }));
      }

      console.log('🚀 构建的订单数据:', JSON.stringify(orderData, null, 2));
      return orderData;
    },

    // 构建仪器数据
    buildInstrumentData() {
      console.log(
        "构建仪器数据 - appointmentTimes:",
        this.formData.appointmentTimes
      );
      console.log("仪器ID:", this.instrumentId);

      // 如果没有预约时间，返回空数组
      if (
        !this.formData.appointmentTimes ||
        this.formData.appointmentTimes.length === 0
      ) {
        console.log("没有预约时间，返回空数组");
        return [];
      }

      // 根据预约时间构建仪器数据
      const instruments = this.formData.appointmentTimes.map((timeSlot) => {
        console.log("处理时间段:", timeSlot);

        // 这里需要根据实际的时间格式来解析
        // 假设 timeSlot 是一个包含开始和结束时间的对象或字符串
        let startTime, endTime;

        if (typeof timeSlot === "string") {
          // 如果是字符串格式，需要解析
          // 例如: "2024-01-15 09:00-11:00"
          const parts = timeSlot.split(" ");
          if (parts.length >= 2) {
            const date = parts[0];
            const timeRange = parts[1];
            const [start, end] = timeRange.split("-");
            startTime = `${date} ${start}:00`;
            endTime = `${date} ${end}:00`;
          }
        } else if (
          typeof timeSlot === "object" &&
          timeSlot.startTime &&
          timeSlot.endTime
        ) {
          // 如果是对象格式
          startTime = timeSlot.startTime;
          endTime = timeSlot.endTime;
        }

        // 如果解析失败，使用默认值
        if (!startTime || !endTime) {
          console.warn("时间解析失败，使用默认值:", timeSlot);
          startTime = new Date().toISOString();
          endTime = new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString();
        }

        return {
          instrumentId: parseInt(this.instrumentId),
          startTime: startTime,
          endTime: endTime,
        };
      });

      console.log("构建的仪器数据:", instruments);
      return instruments;
    },

    // 获取实验类型值
    getExperimentTypeValue() {
      // 直接返回选择的实验类型值
      console.log("getExperimentTypeValue:", this.formData.type);
      return this.formData.type || 2; // 默认为试测(2)
    },

    // 获取支付方式值
    getPayTypeValue() {
      if (
        this.formData.paymentMethodIndex >= 0 &&
        this.paymentMethods.length > this.formData.paymentMethodIndex
      ) {
        const selectedMethod =
          this.paymentMethods[this.formData.paymentMethodIndex];
        return selectedMethod.payType;
      }
      return PayType.PERSONAL_ACCOUNT; // 默认个人账户
    },

    // 获取团队ID（如果是团队支付方式）
    getTeamId() {
      if (
        this.formData.paymentMethodIndex >= 0 &&
        this.paymentMethods.length > this.formData.paymentMethodIndex
      ) {
        const selectedMethod =
          this.paymentMethods[this.formData.paymentMethodIndex];
        return selectedMethod.teamId || null;
      }
      return null;
    },

    // 将时间字符串转换为分钟数（从00:00开始计算）
    timeToMinutes(timeStr) {
      if (!timeStr) return 0;

      // 提取时间部分，支持 "HH:mm" 或 "YYYY-MM-DD HH:mm:ss" 格式
      let time = timeStr;
      if (timeStr.includes(" ")) {
        time = timeStr.split(" ")[1]; // 提取时间部分
      }
      if (time.includes(":")) {
        time = time.substring(0, 5); // 提取 HH:mm 部分
      }

      const [hours, minutes] = time.split(":").map((num) => parseInt(num) || 0);
      return hours * 60 + minutes;
    },

    // 获取当前用户ID
    getCurrentUserId() {
      // 从store中获取用户ID，如果没有则返回默认值
      return this.userInfo?.id || this.userInfo?.userId || 1;
    },

    // 获取预计交付时间文本
    getDeliveryTimeText() {
      if (this.instrumentDetail && this.instrumentDetail.deliveryTime) {
        return this.instrumentDetail.deliveryTime;
      }
      return "7个工作日";
    },

    // 获取仪器收费方式文本
    getInstrumentFeeText() {
      if (!this.instrumentDetail) {
        return "按机时 1000元/小时";
      }

      try {
        const feeMethod = this.instrumentDetail.feeMethod;
        const price = this.instrumentDetail.price;
        const feeUnit = this.instrumentDetail.feeUnit;

        const feeMethodText = feeMethod === 1 ? "按机时" : "按样品";
        const unitText = feeMethod === 1 ? "小时" : "样品";
        const timeText = feeUnit ? "/" + Math.round(feeUnit / 60) : "";

        return feeMethodText + " " + price + "元" + timeText + unitText;
      } catch (error) {
        console.error("获取仪器收费方式文本失败:", error);
        return "按机时 1000元/小时";
      }
    },

    // 获取培训收费方式文本
    getTrainingFeeText() {
      if (!this.instrumentDetail) {
        return "按次数 1000元/次";
      }

      try {
        const trainFeeMethod = this.instrumentDetail.trainFeeMethod;
        const trainFee = this.instrumentDetail.trainFee;
        const trainFeeUnit = this.instrumentDetail.trainFeeUnit;

        if (!trainFeeMethod || !trainFee) {
          return "暂无培训收费";
        }

        const feeMethodText = trainFeeMethod === 1 ? "按机时" : "按次数";
        const unitText = trainFeeMethod === 1 ? "小时" : "次";
        const timeText =
          trainFeeUnit && trainFeeMethod === 1
            ? "/" + Math.round(trainFeeUnit / 60)
            : "1";

        return feeMethodText + " " + trainFee + "元" + timeText + unitText;
      } catch (error) {
        console.error("获取培训收费方式文本失败:", error);
        return "按次数 1000元/次";
      }
    },
  },

  computed: {
    // 计算类型变化
    typeChanged() {
      return this.formData.type;
    },
    ...mapState(["userInfo"]),

    // 计算实际消耗时间（根据选择的预约时间段）
    actualConsumptionTime() {
      if (
        !this.formData.appointmentTimes ||
        this.formData.appointmentTimes.length === 0
      ) {
        return 0;
      }

      let totalMinutes = 0;
      console.log(
        "计算实际消耗时间 - 预约时间段:",
        this.formData.appointmentTimes
      );

      this.formData.appointmentTimes.forEach((timeSlot) => {
        if (typeof timeSlot === "string") {
          // 解析字符串格式的时间段
          // 格式可能是: "2024-01-15 09:00-11:00 (120分钟)" 或 "2024-01-15 09:00-11:00"

          // 先尝试从括号中提取分钟数
          const minutesMatch = timeSlot.match(/\((\d+)分钟\)/);
          if (minutesMatch) {
            totalMinutes += parseInt(minutesMatch[1]);
            return;
          }

          // 如果没有括号，则解析时间范围
          const parts = timeSlot.split(" ");
          if (parts.length >= 2) {
            const timeRange = parts[1];
            const [startTime, endTime] = timeRange.split("-");

            if (startTime && endTime) {
              const startMinutes = this.timeToMinutes(startTime);
              const endMinutes = this.timeToMinutes(endTime);
              totalMinutes += endMinutes - startMinutes;
            }
          }
        } else if (
          typeof timeSlot === "object" &&
          timeSlot.startTime &&
          timeSlot.endTime
        ) {
          // 处理对象格式
          const startMinutes = this.timeToMinutes(timeSlot.startTime);
          const endMinutes = this.timeToMinutes(timeSlot.endTime);
          totalMinutes += endMinutes - startMinutes;
        }
      });

      // 转换为小时，保留一位小数
      const hours = Math.round((totalMinutes / 60) * 10) / 10;
      console.log(
        "计算实际消耗时间 - 总分钟数:",
        totalMinutes,
        "总小时数:",
        hours
      );
      return hours;
    },

    // 根据当前订单类型和培训状态动态生成实验类型列表
    experimentTypeList() {
      // 直接在计算属性中计算订单类型，避免调用methods
      let currentOrderType;
      if (this.tabIndex === 0) {
        currentOrderType = 1; // 1 = SAMPLE_SUBMISSION
      } else {
        currentOrderType =
          this.formData && this.formData.operationMode === "engineer"
            ? 2 // 2 = INSTRUMENT_RESERVATION_ENGINEER
            : 3; // 3 = INSTRUMENT_RESERVATION_SELF
      }

      // 自主操作且需要培训时，只能选择培训
      if (currentOrderType === 3 && this.needTraining) {
        return [
          {
            label: "培训",
            value: 1,
          },
        ];
      }

      // 其他情况可选择试测和正式
      return [
        {
          label: "试测",
          value: 2,
        },
        {
          label: "正式",
          value: 3,
        },
      ];
    },
  },

  watch: {
    // 当类型变化时重新计算价格
    typeChanged() {
      this.recalculatePrice();
    },

    // 监听预约时间变化，触发界面更新
    "formData.appointmentTimes": {
      handler(newTimes) {
        console.log("预约时间发生变化:", newTimes);
        // 计算属性会自动重新计算，这里只是为了调试
        this.$nextTick(() => {
          console.log("实际消耗时间已更新为:", this.actualConsumptionTime);
        });
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.reserve-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
}

/* 选项卡样式 */
.tab-header {
  display: flex;
  background-color: #f0f1f3;
  margin: 20rpx 30rpx;
  border-radius: 100rpx;
  padding: 3px;
}

.tab-item {
  text-align: center;
  flex: 1;
  font-size: 28rpx;
  color: #666;
  background-color: #f0f1f3;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 100rpx;
}

.tab-item:first-child {
  margin-right: 10rpx;
}

.tab-item.active {
  color: #fff;
  background-color: #26d1cb;
  font-weight: bold;
}

.tab-item.active::after {
  display: none;
}

/* 提示文本 */
.tip-text {
  font-size: 24rpx;
  color: #e37318;
  background-color: #fff1e9;
  padding: 16rpx 30rpx;
  line-height: 1.4;
  font-weight: 400;
  border-radius: 8rpx;
  margin: 22rpx 32rpx 4rpx 32rpx;
}

/* 内容区滚动 */
.content-scroll {
  flex: 1;
}

/* 表单内容区 */
.form-content {
  padding-bottom: 120rpx;
  background-color: #f4f7fa;
}

.form-module {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 24rpx;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-item.required .form-label::after {
  content: "*";
  color: #ff5151;
  margin-left: 4rpx;
}

.form-label {
  font-size: 32rpx;
  color: #4e5969;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 400;
}

.form-input {
  border: 1rpx solid #e5e6eb;
  background-color: #f9fafb;
  height: 96rpx;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.form-textarea {
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.form-button-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.form-button {
  height: 66rpx;
  background-color: #fff;
  border-radius: 12rpx;
  border: 2rpx solid #00c0d4;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #00c0d4;
  width: 262rpx;
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-indicator.filled {
  background-color: #e8f5e8;
  border: 1rpx solid #4caf50;
}

.status-indicator.filled .status-text {
  color: #4caf50;
}

.status-indicator.empty {
  background-color: #fff3e0;
  border: 1rpx solid #ff9800;
}

.status-indicator.empty .status-text {
  color: #ff9800;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
}

.radio-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  margin-right: 10rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #26d1cb;
}

.radio-item.active .radio-dot {
  border-color: #26d1cb;
}

.radio-text {
  font-size: 28rpx;
  color: #333;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  /* border-bottom: 1px solid #ebedf0; */
  background: #f9f9f9;
  border-radius: 8px;
}

.service-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 400;
}

.service-counter {
  display: flex;
  align-items: center;
}

.counter-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

.counter-btn.minus {
  background-color: #eaeaea;
  color: rgba(0, 0, 0, 0.9);
}

.counter-btn.plus {
  background-color: #2ac6c8;
  color: #fff;
}

.counter-num {
  width: 60rpx;
  text-align: center;
  margin: 4px;
  font-size: 28rpx;
  background: #fff;
  border-radius: 4px;
  padding: 1px 1px;
}

/* 价格信息 */
.order-info {
  padding: 14rpx 0;
  border-radius: 8rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.info-label {
  font-size: 32rpx;
  color: #4e5969;
  line-height: 1.5;
  font-weight: 400;
}

.info-value {
  font-size: 32rpx;
  color: #101217;
  line-height: 1.5;
  font-weight: 500;
}

.price-info {
  margin: 30rpx 0;
}

.price-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  .form-label {
    margin-bottom: 0;
  }
}

.price-label-container {
  display: flex;
  align-items: center;
  gap: 10rpx;
  .form-label {
    margin-bottom: 0;
  }
}

.price-label {
  font-size: 32rpx;
  color: #4e5969;
  font-weight: 400;
}

.price-value {
  font-size: 36rpx;
  color: #101217;
  font-weight: bold;
}

.price-value.discount {
  color: #e04e51;
  font-weight: bold;
  font-size: 36rpx;
}

.total-price {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 20rpx 0 10rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #ebedf0;
}

.subtotal-label {
  font-size: 32rpx;
  color: #4e5969;
  margin-right: 10rpx;
}

.subtotal-value {
  font-size: 48rpx;
  color: #101217;
  font-weight: bold;
}

/* 支付方式 */
.payment-method {
  margin-top: 30rpx;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.payment-option {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  border: 1px solid #dcdcdc;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.payment-option.active {
  border-color: #26d1cb;
  background-color: rgba(38, 209, 203, 0.05);
}

.payment-option.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border-top: 40rpx solid #26d1cb;
  border-right: 40rpx solid transparent;
  z-index: 1;
}

.check-icon {
  position: absolute;
  left: 0rpx;
  top: -6rpx;
  width: 28rpx;
  height: 28rpx;
  z-index: 2;
}

.payment-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: flex-start;
}

.payment-tag {
  font-size: 32rpx;
  color: #101217;
  font-weight: 400;
  margin-bottom: 10rpx;
}

.balance-text {
  font-size: 28rpx;
  color: #4e5969;
  font-weight: 400;
}

.payment-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.discount-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
  text-decoration: line-through;
}

.discount-text.discount {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 136, 0, 0.9);
  text-decoration: none;
}

.no-payment-tip {
  width: 100%;
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

/* 操作模式样式 */
.operation-mode-container {
  display: flex;
  gap: 20rpx;
}

.operation-mode {
  height: 80rpx;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f3f3;
  border-radius: 200rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.operation-mode.active {
  background-color: #f1ffff;
  color: #26d1cb;
  border: 2rpx solid #26d1cb;
}

.type-warning {
  font-size: 24rpx;
  color: #e37318;
  font-weight: 400;
  margin-top: 20rpx;
  background-color: #fff1e9;
  padding: 8rpx 48rpx;
  border-radius: 8rpx;
  display: block;
}

/* 已选时间展示样式 */
.selected-times {
  margin-bottom: 30rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.selected-title {
  font-size: 28rpx;
  font-weight: 400;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.time-item {
  background-color: #ffffff;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
}
.time-item:last-child {
  margin-bottom: 20rpx;
}

.usage-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding: 16rpx;
  border-radius: 6rpx;
}

.usage-time-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #4e5969;
  font-weight: 400;
}

.info-icon {
  margin-left: 8rpx;
  display: flex;
  align-items: center;
}

.time-value {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.input-tip {
  font-size: 28rpx;
  color: #4e5969;
  margin-top: 8rpx;
  display: block;
  font-weight: 400;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 20rpx 60rpx 20rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  justify-content: space-between;
  z-index: 3;
}

.cancel-btn,
.save-btn,
.submit-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin: 0 10rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #ffffff;
  color: #4e5969;
  border: 1px solid #e5e6eb;
}

.save-btn {
  background-color: #fff;
  color: #26d1cb;
  border: 1px solid #26d1cb;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.save-status {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #52c41a;
  border-radius: 20rpx;
  padding: 4rpx 8rpx;
  min-width: 60rpx;
}

.save-status-text {
  font-size: 20rpx;
  color: #fff;
  line-height: 1;
}

.submit-btn {
  background-color: #26d1cb;
  color: #fff;
  border: none;
}

/* 需求订单模式下的按钮布局 */
.bottom-buttons.demand-mode {
  gap: 20rpx;
}

.bottom-buttons.demand-mode .cancel-btn,
.bottom-buttons.demand-mode .submit-btn {
  flex: 1;
}

/* 必选标签样式 */
.required-tag {
  background-color: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

/* 禁用状态的按钮样式 */
.counter-btn.disabled {
  background-color: #f5f5f5 !important;
  color: #ccc !important;
  cursor: not-allowed;
}
</style>
