<template>
  <view class="h5-container">
    <!-- 自定义导航栏 -->

    <!-- 测试按钮 -->
    <!-- <view class="test-bar" style="position: fixed; top: 0; left: 0; right: 0; z-index: 9999; background: #f0f0f0; padding: 10px; display: flex; justify-content: space-between;">
      <text style="font-size: 14px;">通信测试</text>
      <view style="display: flex; gap: 10px;">
        <button size="mini" type="primary" @tap="testPostMessage">测试通信</button>
        <button size="mini" type="default" @tap="navigateToTest">跳转测试</button>
      </view>
    </view> -->

    <!-- H5 页面容器 -->
    <web-view
      :src="h5Url"
      @message="handleMessage"
      @load="handleLoad"
      @error="handleError"
    ></web-view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" color="#26D1CB" size="60"></u-loading-icon>
      <text class="loading-text">正在加载编辑器...</text>
    </view>

    <!-- 错误状态 -->
    <view v-if="error" class="error-container">
      <text class="error-text">页面加载失败</text>
      <view class="retry-btn" @tap="retryLoad">
        <text>重试</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "AppointFormEditor",
  data() {
    return {
      pageTitle: "预约申请单",
      instrumentId: "",
      instrumentName: "",
      content: "",
      from: "",
      h5Url: "https://www.tool.yellowcloud.top/tool-bucket/tools/projects/7b9a4453-d1f6-40e2-89dd-c5295b695660/ueditor/index.html",
      loading: true,
      error: false,
      hasUnsavedChanges: false
    };
  },
  onLoad() {
    console.log('H5页面加载，从本地存储获取数据');

    // 从本地存储获取编辑器数据
    const editorContext = uni.getStorageSync('appointFormContext') || {};
    const content = uni.getStorageSync('appointForm') || '';

    console.log('从存储获取的上下文:', editorContext);
    console.log('从存储获取的内容:', content);

    // 设置页面数据
    this.instrumentId = editorContext.instrumentId || '';
    this.instrumentName = editorContext.instrumentName || '仪器预约';
    this.content = content;
    this.from = 'reserve';

    // 设置页面标题
    // this.pageTitle = this.instrumentName + " - 申请单";

    // 构建 H5 页面 URL
    this.buildH5Url();
  },
  onBackPress() {
    // 拦截返回操作
    this.handleBack();
    return true; // 阻止默认返回行为
  },
  methods: {
    // 构建 H5 页面 URL
    buildH5Url() {
      // 使用本地UEditor文件
      const baseUrl = 'https://www.tool.yellowcloud.top/tool-bucket/tools/projects/7b9a4453-d1f6-40e2-89dd-c5295b695660/ueditor/index.html';

      const params = {
        instrumentId: this.instrumentId,
        instrumentName: encodeURIComponent(this.instrumentName),
        content: encodeURIComponent(this.content),
        from: this.from,
        // 添加一些配置参数
        theme: 'light',
        lang: 'zh-CN',
        // 添加调试模式
        debug: 'true',
        timestamp: Date.now()
      };

      const queryString = Object.keys(params)
        .map(key => `${key}=${params[key]}`)
        .join('&');

      this.h5Url = `${baseUrl}?${queryString}`;
      console.log('🔗 构建的H5页面URL:', this.h5Url);
    },

    // 处理来自 H5 的消息
    handleMessage(event) {
      console.log('📨 接收到H5消息，数据长度:', event.detail.data ? event.detail.data.length : 0);

      if (event.detail && event.detail.data) {
        if (Array.isArray(event.detail.data)) {
          // 处理数组中的所有消息，优先处理最新的save消息
          console.log('📦 处理消息数组，共', event.detail.data.length, '条消息');

          // 先查找save消息（最重要）
          let saveMessage = null;
          let readyMessage = null;
          let otherMessages = [];

          for (let i = event.detail.data.length - 1; i >= 0; i--) {
            const msg = event.detail.data[i];
            console.log(`� 消息${i}:`, msg);

            if (msg && msg.type) {
              if (msg.type === 'save' && !saveMessage) {
                saveMessage = msg;
                console.log('💾 找到保存消息');
              } else if (msg.type === 'ready' && !readyMessage) {
                readyMessage = msg;
              } else {
                otherMessages.push(msg);
              }
            }
          }

          // 优先处理save消息
          if (saveMessage) {
            this.processMessage(saveMessage);
          } else if (readyMessage) {
            this.processMessage(readyMessage);
          } else if (otherMessages.length > 0) {
            this.processMessage(otherMessages[0]);
          }

        } else if (typeof event.detail.data === 'object') {
          console.log('📦 处理单个消息对象');
          this.processMessage(event.detail.data);
        } else {
          console.log('⚠️ 数据格式未知:', event.detail.data);
        }
      } else {
        console.log('❌ 没有找到valid data');
      }
    },

    // 处理单个消息
    processMessage(messageData) {
      if (messageData && messageData.type) {
        console.log('🎪 处理消息类型:', messageData.type);

        switch (messageData.type) {
          case 'contentChange':
            // 内容发生变化
            this.hasUnsavedChanges = messageData.hasUnsavedChanges || true;
            console.log('📝 内容发生变化，未保存状态:', this.hasUnsavedChanges);
            break;

          case 'save':
            // H5 页面请求保存
            console.log('💾 H5页面请求保存，内容长度:', messageData.content ? messageData.content.length : 0);
            this.handleSave(messageData.content);
            break;

          case 'ready':
            // H5 页面加载完成
            this.loading = false;
            this.error = false;
            console.log('✅ H5页面加载完成');
            break;

          case 'error':
            // H5 页面发生错误
            console.log('❌ H5页面发生错误');
            this.handleError();
            break;

          default:
            console.log('❓ 未知消息类型:', messageData.type);
        }
      } else {
        console.log('⚠️ 消息格式不正确，缺少type字段:', messageData);
      }
    },

    // 处理保存
    handleSave(content) {
      console.log('💾 开始保存申请单内容，长度:', content ? content.length : 0);

      try {
        // 保存到本地缓存
        uni.setStorageSync('appointForm', content);
        console.log('✅ 内容已保存到本地缓存');

        // 设置返回标识，表示内容已更新
        uni.setStorageSync('appointFormUpdated', true);
        console.log('✅ 更新标识已设置');

        // 通过事件传递给预约页面（保留原有方式作为备用）
        uni.$emit('saveAppointForm', {
          content: content
        });
        console.log('✅ 事件已发送');

        this.hasUnsavedChanges = false;

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });

        console.log('✅ 保存完成，H5页面已经触发了返回操作');
      } catch (error) {
        console.error('❌ 保存失败:', error);
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    },

    // 处理返回
    handleBack() {
      if (this.hasUnsavedChanges) {
        uni.showModal({
          title: '提示',
          content: '您有未保存的更改，是否要保存？',
          showCancel: true,
          cancelText: '不保存',
          confirmText: '返回保存',
          success: (res) => {
            if (res.confirm) {
              // 用户选择返回保存
              console.log('🔄 用户选择返回保存');
              // 不执行返回操作，让用户手动保存
            } else if (res.cancel) {
              // 用户选择不保存，直接返回
              console.log('🔄 用户选择不保存，直接返回');
              this.hasUnsavedChanges = false;
              uni.navigateBack();
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    },

    // H5 页面加载完成
    handleLoad() {
      console.log('H5页面加载完成');
      this.loading = false;
      this.error = false;

      // 页面加载完成后，可以尝试向H5页面发送消息确认内容
      // 但由于web-view的限制，主要还是依赖URL参数传递
    },

    // H5 页面加载错误
    handleError() {
      console.log('H5页面加载失败');
      this.loading = false;
      this.error = true;

      uni.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    },

    // 重试加载
    retryLoad() {
      this.loading = true;
      this.error = false;
      this.buildH5Url();
    },

    // 测试发送消息到 H5 页面
    testPostMessage() {
      console.log('🔗 尝试向 H5 页面发送消息...');
      const message = {
        type: 'testMessage',
        data: {
          message: 'Hello from native app!',
          timestamp: Date.now()
        }
      };
      uni.postMessage(message);
      console.log('🔗 消息已发送，请在 H5 页面控制台查看。');
    },

    // 跳转到测试页面
    navigateToTest() {
      uni.navigateTo({
        url: '/pages/test/test'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.h5-container {
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
}

.error-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #26D1CB;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.retry-btn:active {
  background-color: #1fb5b0;
}
</style>
