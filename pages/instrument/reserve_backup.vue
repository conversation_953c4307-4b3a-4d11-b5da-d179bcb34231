<template>
  <view class="reserve-container">
    <!-- 内容区 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 选项卡 -->
      <view class="tab-header">
        <view 
          class="tab-item" 
            :class="{ active: tabIndex === 0 }"
            @tap="switchTab(0)"
        >送样申请</view>
        <view 
          class="tab-item" 
            :class="{ active: tabIndex === 1 }"
            @tap="switchTab(1)"
        >预约检测</view>
      </view>
    
    <!-- 提示文本 -->
    <view class="tip-text">
      注：样品审核通过后会到打印清单页面中填写快递信息
    </view>
    
      <view v-if="tabIndex === 0" class="form-content">
        <!-- 送样申请表单内容 -->
        <view class="form-item required">
          <text class="form-label">实验名称</text>
          <input type="text" placeholder="请输入名称" class="form-input" v-model="formData.sample.name" />
        </view>
        
        <view class="form-item required">
          <text class="form-label">实验类型</text>
          <lk-list-select 
            v-model="formData.sample.type"
            :list="experimentTypeList"
            :customStyle="{
              backgroundColor: '#F9FAFB',
              height: '96rpx',
              borderRadius: '16rpx',
            }"
            placeholder="请选择类型"
          ></lk-list-select>
        </view>
        
        <view class="form-item required">
          <text class="form-label">样品寄回</text>
          <view class="radio-group">
            <view 
              class="radio-item" 
              :class="{ active: !formData.sample.returnSample }" 
              @tap="handleSampleReturn(false)"
            >
              <view class="radio-dot">
                <view class="radio-inner" v-if="!formData.sample.returnSample"></view>
              </view>
              <text class="radio-text">不寄回</text>
            </view>
            <view 
              class="radio-item" 
              :class="{ active: formData.sample.returnSample }" 
              @tap="handleSampleReturn(true)"
            >
              <view class="radio-dot">
                <view class="radio-inner" v-if="formData.sample.returnSample"></view>
              </view>
              <text class="radio-text">寄回</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">预约申请单</text>
          <view class="form-button" @tap="showApplicationForm">
            <text>点击查看/填写</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">备注</text>
          <textarea placeholder="请输入备注" class="form-textarea" v-model="formData.sample.remarks"></textarea>
        </view>
        
        <!-- 增值服务部分 - 大盒子开始 -->
        <view class="service-box">
          <view class="form-section-title">增值服务</view>
          
          <view class="service-item" v-for="(service, index) in formData.sample.services" :key="index">
            <view class="service-info">
              <text class="service-name">{{ service.name }} {{ service.price }}元/{{ service.unit }}</text>
            </view>
            <view class="service-counter">
              <view class="counter-btn minus" @tap="handleSampleServiceMinus(index)">-</view>
              <text class="counter-num">{{ service.count }}</text>
              <view class="counter-btn plus" @tap="handleSampleServicePlus(index)">+</view>
            </view>
          </view>
          
          <view class="order-info">
            <view class="info-item">
              <text class="info-label">预计交付时间</text>
              <text class="info-value">7个工作日</text>
            </view>
            <view class="info-item">
              <text class="info-label">仪器收费方式</text>
              <text class="info-value">按机时 1000元/小时</text>
            </view>
          </view>
          
          <view class="price-info">
            <view class="price-item">
              <text class="price-label">预估金额</text>
              <lk-svg src="/static/svg/info.svg" width="44rpx" height="44rpx"></lk-svg>
              <text class="price-value">¥{{ originalPrice.toFixed(1) }}</text>
            </view>
            <view class="price-item">
              <text class="price-label">折扣金额</text>
              <text class="price-value discount">¥{{ discountedPrice.toFixed(1) }}</text>
            </view>
          </view>
          
          <view class="total-price">
            <text class="subtotal-label">小计:</text>
            <text class="subtotal-value">¥{{ discountedPrice.toFixed(1) }}</text>
          </view>
        </view>
        <!-- 大盒子结束 -->
        
        <!-- 支付方式部分 -->
        <view class="payment-method required">
          <text class="method-title">支付方式</text>
          <view class="payment-options">
            <!-- 个人账户余额 -->
            <view class="payment-option" :class="{'active': selectedPayment === 1}" @tap="selectPayment(0)">
              <view class="check-icon" v-if="selectedPayment === 1">
                <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
              </view>
              <view class="payment-left">
                <view class="payment-tag">
                  <text>个人账户余额</text>
                </view>
                <view class="balance-text">当前可用余额: {{paymentMethods[0].balance}} 元</view>
              </view>
              <view class="payment-right">
                <text class="discount-text">无折扣</text>
              </view>
            </view>
            
            <!-- 松山湖团队额度 -->
            <view class="payment-option" :class="{'active': selectedPayment === 2}" @tap="selectPayment(1)">
              <view class="check-icon" v-if="selectedPayment === 2">
                <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
              </view>
              <view class="payment-left">
                <view class="payment-tag">
                  <text>松山湖团队额度</text>
                </view>
                <view class="balance-text">当前可用余额: {{paymentMethods[1].balance}} 元</view>
              </view>
              <view class="payment-right">
                <text class="discount-text discount">可打5折</text>
              </view>
            </view>
            
            <!-- 松山湖团队余额 -->
            <view class="payment-option" :class="{'active': selectedPayment === 3}" @tap="selectPayment(2)">
              <view class="check-icon" v-if="selectedPayment === 3">
                <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
              </view>
              <view class="payment-left">
                <view class="payment-tag">
                  <text>松山湖团队余额</text>
                </view>
                <view class="balance-text">当前可用余额: {{paymentMethods[2].balance}} 元</view>
              </view>
              <view class="payment-right">
                <text class="discount-text discount">可打5折</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view v-if="tabIndex === 1" class="form-content">
        <!-- 预约检测表单内容 -->
        <view class="form-item required">
          <text class="form-label">操作形式</text>
          <view class="operation-mode-container">
            <view 
              class="operation-mode" 
              :class="{ active: operationMode === 'self' }" 
              @tap="selectOperationMode('self')"
            >
              <text>自主操作</text>
            </view>
            <view 
              class="operation-mode" 
              :class="{ active: operationMode === 'engineer' }" 
              @tap="selectOperationMode('engineer')"
            >
              <text>工程师操作</text>
            </view>
          </view>
        </view>
        
        <view class="form-item required">
          <text class="form-label">实验名称</text>
          <input type="text" placeholder="请输入名称" class="form-input" v-model="formData.test.name" />
        </view>
        
        <view class="form-item required">
          <text class="form-label">实验类型</text>
          <lk-list-select 
            v-model="formData.test.type"
            :list="experimentTypeList"
            placeholder="请选择类型"
          ></lk-list-select>
            <text class="type-warning">新样只能选择测试，正式品需提前与管理员确认</text>
        </view>
        
        <view class="form-item required">
          <text class="form-label">样品寄回</text>
          <view class="radio-group">
            <view 
              class="radio-item" 
              :class="{ active: !formData.test.returnSample }" 
              @tap="handleTestReturn(false)"
            >
              <view class="radio-dot">
                <view class="radio-inner" v-if="!formData.test.returnSample"></view>
              </view>
              <text class="radio-text">不寄回</text>
            </view>
            <view 
              class="radio-item" 
              :class="{ active: formData.test.returnSample }" 
              @tap="handleTestReturn(true)"
            >
              <view class="radio-dot">
                <view class="radio-inner" v-if="formData.test.returnSample"></view>
              </view>
              <text class="radio-text">寄回</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">预约申请单</text>
          <view class="form-button" @tap="showApplicationForm">
            <text>点击查看/填写</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">预约时间</text>
          <view class="form-button" @tap="goToTimeSelect">
            <text>点击查看/选择</text>
          </view>
        </view>
        
        <!-- 已选时间展示 -->
        <view class="selected-times" v-if="formData.test.appointmentTimes && formData.test.appointmentTimes.length > 0">
          <view class="time-item" v-for="(time, timeIdx) in formData.test.appointmentTimes" :key="timeIdx">
            <text>{{time}}</text>
          </view>
          <view class="time-notice">
            <text>到达预约时段后，请联系负责人协商具体使用时间。如有特殊情况请备注具体原因。</text>
          </view>
          <view class="usage-time">
            <text>实验预计时间</text>
            <text class="time-value">{{formData.test.estimatedTime}} 小时</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">备注</text>
          <textarea placeholder="请输入备注" class="form-textarea" v-model="formData.test.remarks"></textarea>
        </view>
        
        <!-- 材料部分 -->
        <view class="form-section-title">材料</view>
        
        <view class="service-item" v-for="(material, index) in formData.test.materials" :key="index">
          <view class="service-info">
            <text class="service-name">{{ material.name }} {{ material.price }}元/{{ material.unit }}</text>
          </view>
          <view class="service-counter">
            <view class="counter-btn minus" @tap="handleMaterialMinus(index)">-</view>
            <text class="counter-num">{{ material.count }}</text>
            <view class="counter-btn plus" @tap="handleMaterialPlus(index)">+</view>
          </view>
        </view>
        
        <!-- 增值服务部分 -->
        <view class="form-section-title">增值服务</view>
        
        <view class="service-item" v-for="(service, index) in formData.test.services" :key="index">
          <view class="service-info">
            <text class="service-name">{{ service.name }} {{ service.price }}元/{{ service.unit }}</text>
          </view>
          <view class="service-counter">
            <view class="counter-btn minus" @tap="handleTestServiceMinus(index)">-</view>
            <text class="counter-num">{{ service.count }}</text>
            <view class="counter-btn plus" @tap="handleTestServicePlus(index)">+</view>
          </view>
        </view>
        
        <!-- 服务协议提示 -->
        <view class="service-agreement">
          <text>服务参加培训需进行课程学习,考核及格后方可预约,订单提交前请确认持证者在有效期内,非持证者只能进行培训,不可操作.</text>
        </view>
        
        <!-- 价格信息 -->
        <view class="price-info">
          <view class="price-item">
            <text class="price-label">预估金额</text>
            <text class="price-value">¥{{ originalPrice.toFixed(1) }}</text>
          </view>
          <view class="price-item">
            <text class="price-label">折扣金额</text>
            <text class="price-value discount">¥{{ discountedPrice.toFixed(1) }}</text>
          </view>
        </view>
        
        <view class="total-price">
          <text class="subtotal-label">小计:</text>
          <text class="subtotal-value">¥{{ discountedPrice.toFixed(1) }}</text>
        </view>
        
        <!-- 支付方式 */
        <view class="payment-method">
          <text class="method-title">支付方式</text>
          <view class="method-options">
            <view 
              v-for="(method, idx) in paymentMethods" 
              :key="idx"
              class="method-item" 
              :class="{ active: selectedPayment === method.id }"
              @tap="selectPayment(idx)"
            >
              <view class="method-check"></view>
              <view class="method-info">
                <text class="method-name">{{ method.name }}</text>
                <text class="method-balance">当前可用余额: {{ method.balance }} 元</text>
              </view>
              <text class="method-tag">{{ method.discount }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <view class="cancel-btn" @tap="handleCancel">取消</view>
      <view class="save-btn" @tap="handleSave">暂时保存</view>
      <view class="submit-btn" @tap="handleSubmit">提交</view>
    </view>
  </view>
</template>

<script>
// 引入uView的组件和项目组件
import lkSelect from "@/components/lk-select/lk-select.vue";
import lkListSelect from "@/components/lk-list-select/lk-list-select.vue";
import lkSvg from "@/components/lk-svg/lk-svg.vue";

export default {
  components: {
    lkSelect,
    lkListSelect,
    lkSvg
  },
  data() {
    return {
      tabIndex: 0, // 默认选中送样申请
      instrumentId: '',
      instrumentName: '',
      showTypeSelectorFlag: false, // 控制送样申请中实验类型选择器的显示
      showTestTypeSelectorFlag: false, // 控制预约检测中实验类型选择器的显示
      experimentTypeList: [
        {
          label: '试测',
          value: '试测'
        },
        {
          label: '正式',
          value: '正式'
        }
      ],
      formData: {
        sample: {
          name: '', // 实验名称
          type: '', // 实验类型
          returnSample: false, // 样品寄回
          remarks: '', // 备注
          services: [
            { id: 1, name: '服务必选', price: 500, unit: '次', count: 3 },
            { id: 2, name: '服务2', price: 500, unit: '个', count: 0 },
            { id: 3, name: '服务3', price: 500, unit: '个', count: 0 }
          ]
        },
        test: {
          name: '', // 实验名称
          type: '', // 实验类型
          returnSample: false, // 样品寄回
          remarks: '', // 备注
          appointmentTimes: [], // 预约时间
          estimatedTime: 2, // 实验预计时间
          materials: [
            { id: 1, name: '标准材料1', price: 300, unit: '个', count: 0 },
            { id: 2, name: '标准材料2', price: 200, unit: '个', count: 0 }
          ],
          services: [
            { id: 1, name: '培训服务', price: 1000, unit: '次', count: 0 },
            { id: 2, name: '加急服务', price: 800, unit: '次', count: 0 }
          ]
        }
      },
      paymentMethods: [
        { id: 1, name: '个人账户余额', balance: '00.00', discount: '无折扣', rate: 1 },
        { id: 2, name: '松山湖团队额度', balance: '00.00', discount: '可打5折', rate: 0.5 },
        { id: 3, name: '松山湖团队余额', balance: '00.00', discount: '可打5折', rate: 0.5 }
      ],
      selectedPayment: 1, // 选中的支付方式ID
      originalPrice: 2000, // 原价
      discountedPrice: 1400, // 折扣后价格
      operationMode: 'self', // 新增的操作模式
      otherTeamBalance: 0, // 其他团队余额
    };
  },
  onLoad(options) {
    if (options.instrumentId) {
      this.instrumentId = options.instrumentId;
      // 实际应用中应该根据ID获取仪器信息
    }
    if (options.instrumentName) {
      this.instrumentName = options.instrumentName;
      // 设置页面标题
      uni.setNavigationBarTitle({
        title: this.instrumentName
      });
    }
    // 初始化数据
    this.initData();
    // 初始化计算价格
    this.recalculatePrice();
  },
  onShow() {
    // 监听从timeSelect返回的预约时间
    uni.$off('selectReserveTime'); // 避免重复监听
    uni.$on('selectReserveTime', (str) => {
      if (str && !this.formData.test.appointmentTimes.includes(str)) {
        this.formData.test.appointmentTimes.push(str);
      }
    });
  },
  methods: {
    initData() {
      // 初始化数据，模拟一些预约时间
      this.formData.test.appointmentTimes = [
        '2023-12-25 周一 上午 (9:00-12:00)',
        '2023-12-26 周二 下午 (14:00-17:00)'
      ];
    },
    switchTab(tab) {
      this.tabIndex = tab;
    },
    handleSampleReturn(value) {
      this.formData.sample.returnSample = value;
    },
    handleTestReturn(value) {
      this.formData.test.returnSample = value;
    },
    handleSampleServiceMinus(index) {
      const services = this.formData.sample.services;
      if (services && services.length > index) {
        const service = services[index];
        if (service.count > 0) {
          service.count--;
          this.recalculatePrice();
        }
      }
    },
    handleSampleServicePlus(index) {
      const services = this.formData.sample.services;
      if (services && services.length > index) {
        const service = services[index];
          service.count++;
        this.recalculatePrice();
      }
    },
    handleMaterialMinus(index) {
      const materials = this.formData.test.materials;
      if (!materials || materials.length <= index) return;
      
      const material = materials[index];
      if (material && material.count > 0) {
        material.count--;
        this.recalculatePrice();
      }
    },
    handleMaterialPlus(index) {
      const materials = this.formData.test.materials;
      if (!materials || materials.length <= index) return;
      
      const material = materials[index];
      if (material) {
          material.count++;
        this.recalculatePrice();
      }
    },
    handleTestServiceMinus(index) {
      const services = this.formData.test.services;
      if (!services || services.length <= index) return;
      
      const service = services[index];
      if (service && service.count > 0) {
        service.count--;
        this.recalculatePrice();
      }
    },
    handleTestServicePlus(index) {
      const services = this.formData.test.services;
      if (!services || services.length <= index) return;
      
      const service = services[index];
      if (service) {
        service.count++;
      this.recalculatePrice();
      }
    },
    selectPayment(idx) {
      if (idx >= 0 && idx < this.paymentMethods.length) {
        this.selectedPayment = this.paymentMethods[idx].id;
        this.recalculatePrice();
      }
    },
    recalculatePrice() {
      // 根据选择的表单类型计算价格
      if (this.tabIndex === 0) {
        // 送样申请价格计算
        let basePrice = 2000;
        let serviceTotal = 0;
        
        // 计算服务费用
        if (this.formData.sample.services) {
          this.formData.sample.services.forEach(service => {
            serviceTotal += service.price * service.count;
          });
        }
        
        this.originalPrice = basePrice + serviceTotal;
      } else {
        // 预约检测价格计算
        let basePrice = 1000;
        let serviceTotal = 0;
        let materialTotal = 0;
        
        // 计算服务费用
        if (this.formData.test.services) {
          this.formData.test.services.forEach(service => {
            serviceTotal += service.price * service.count;
          });
        }
        
        // 计算材料费用
        if (this.formData.test.materials) {
          this.formData.test.materials.forEach(material => {
            materialTotal += material.price * material.count;
          });
        }
        
        this.originalPrice = basePrice + serviceTotal + materialTotal;
      }
      
      // 根据选择的支付方式计算折扣
      const selectedMethod = this.paymentMethods.find(m => m.id === this.selectedPayment);
      if (selectedMethod) {
        this.discountedPrice = Math.round(this.originalPrice * selectedMethod.rate);
      } else {
        this.discountedPrice = this.originalPrice;
      }
    },
    showApplicationForm() {
      uni.showToast({
        title: '查看/填写申请单',
        icon: 'none'
      });
    },
    goToTimeSelect() {
      // 获取当前仪器名称
      const instrumentName = this.instrumentName || '仪器预约';
      
      // 跳转到时间选择页面，并传递仪器名称
      uni.navigateTo({
        url: `/pages/instrument/timeSelect?title=${encodeURIComponent(instrumentName)}`
      });
    },
    addAppointmentTime(time) {
      // 添加预约时间到列表
      if (!this.formData.test.appointmentTimes) {
        this.formData.test.appointmentTimes = [];
      }
      // 避免重复添加
      if (!this.formData.test.appointmentTimes.includes(time)) {
        this.formData.test.appointmentTimes.push(time);
      }
      // 重新计算价格
      this.recalculatePrice();
    },
    handleCancel() {
      uni.navigateBack();
    },
    handleSave() {
      uni.showToast({
        title: '已保存',
        icon: 'success'
      });
    },
    handleSubmit() {
      // 表单验证
      let isValid = true;
      let message = '';
      
      if (this.tabIndex === 0) {
        // 送样申请表单验证
        if (!this.formData.sample.name) {
          isValid = false;
          message = '请输入实验名称';
        } else if (!this.formData.sample.type) {
          isValid = false;
          message = '请选择实验类型';
        }
      } else {
        // 预约检测表单验证
        if (!this.formData.test.name) {
          isValid = false;
          message = '请输入实验名称';
        } else if (!this.formData.test.type) {
          isValid = false;
          message = '请选择实验类型';
        } else if (this.formData.test.appointmentTimes.length === 0) {
          isValid = false;
          message = '请选择预约时间';
        }
      }
      
      if (!isValid) {
        uni.showToast({
          title: message,
          icon: 'none'
        });
        return;
      }
      
      // 提交表单
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
    },
    selectOperationMode(mode) {
      this.operationMode = mode;
    }
  },
  computed: {
    // 计算样本类型变化
    sampleTypeChanged() {
      return this.formData.sample.type;
    },
    // 计算测试类型变化
    testTypeChanged() {
      return this.formData.test.type;
        }
  },
  watch: {
    // 当类型变化时重新计算价格
    sampleTypeChanged() {
      this.recalculatePrice();
    },
    testTypeChanged() {
      this.recalculatePrice();
    }
  }
};
</script>

<style lang="scss" scoped>
.reserve-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

/* 选项卡样式 */
.tab-header {
  display: flex;
  background-color: #f8f8f8;
  margin: 20rpx 30rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 100rpx;
}

.tab-item:first-child {
  margin-right: 10rpx;
}

.tab-item.active {
  color: #fff;
  background-color: #26D1CB;
  font-weight: bold;
}

.tab-item.active::after {
  display: none;
}

/* 提示文本 */
.tip-text {
  font-size: 24rpx;
  color: #E37318;
  background-color: #FFF1E9;
  padding: 16rpx 30rpx;
  line-height: 1.4;
  font-weight: 400;
  border-radius: 8rpx;
  margin: 22rpx 32rpx 4rpx 32rpx;
}

/* 内容区滚动 */
.content-scroll {
  flex: 1;
}

/* 表单内容区 */
.form-content {
  padding: 20rpx 30rpx;
  padding-bottom: 120rpx;
  background-color: #fff;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-item.required .form-label::before {
  content: '*';
  color: #FF5151;
  margin-right: 4rpx;
}

.form-label {
  font-size: 32rpx;
  color: #4E5969;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 400;
}

.form-input {
  border: 1rpx solid #E5E6EB;
  background-color: #F9FAFB;
  height: 96rpx;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.form-textarea {
  height: 160rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.form-button {
  height: 96rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 2rpx solid #00C0D4;
  display: flex;
  width: 262rpx;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #00C0D4;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
}

.radio-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  margin-right: 10rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #26D1CB;
}

.radio-item.active .radio-dot {
  border-color: #26D1CB;
}

.radio-text {
  font-size: 28rpx;
  color: #333;
}

/* 增值服务部分 */
.form-section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 24rpx;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #EBEDF0;
}

.service-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 400;
}

.service-counter {
  display: flex;
  align-items: center;
}

.counter-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

.counter-btn.minus {
  background-color: #f5f5f5;
  color: #999;
  border: 1rpx solid #EBEDF0;
}

.counter-btn.plus {
  background-color: #2AC6C8;
  color: #fff;
}

.counter-num {
  width: 60rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 价格信息 */
.order-info {
  margin: 30rpx 0;
  background-color: #f9f9f9;
  padding: 20rpx 0;
  border-radius: 8rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.info-label{
  font-size: 32rpx;
  color: #4E5969;
  line-height: 1.5;
  font-weight: 400;
}
.info-value {
  font-size: 32rpx;
  color: #101217;
  line-height: 1.5;
  font-weight: 500;
}
.price-info {
  margin: 30rpx 0;
}

.price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.price-label {
  font-size: 32rpx;
  color: #4E5969;
  font-weight: 400;
}

.price-value {
  font-size: 36rpx;
  color: #101217;
  font-weight: bold;
}

.price-value.discount {
  color: #E04E51;
  font-weight: bold;
  font-size: 36rpx;
}

.total-price {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 20rpx 0 10rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #EBEDF0;
}

.subtotal-label {
  font-size: 32rpx;
  color: #4E5969;
  margin-right: 10rpx;
}

.subtotal-value {
  font-size: 48rpx;
  color: #101217;
  font-weight: bold;
}

/* 支付方式 */
.payment-method {
  margin-top: 30rpx;
}

.method-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.payment-option {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  border: 1px solid #DCDCDC;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.payment-option.active {
  border-color: #26D1CB;
  background-color: rgba(38, 209, 203, 0.05);
}

.payment-option.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border-top: 40rpx solid #26D1CB;
  border-right: 40rpx solid transparent;
  z-index: 1;
}

.check-icon {
  position: absolute;
  left: 0rpx;
  top: -6rpx;
  width: 28rpx;
  height: 28rpx;
  z-index: 2;
}

.payment-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: flex-start;
}

.payment-tag {
  font-size: 32rpx;
  color: #101217;
  font-weight: 400;
  margin-bottom: 10rpx;
}

.balance-text {
  font-size: 28rpx;
  color: #4E5969;
  font-weight: 400;
}

.payment-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.discount-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.60);
  font-weight: 400;
  text-decoration: line-through;
}

.discount-text.discount {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 136, 0, 0.90);
  text-decoration: none;
}



/* 预约检测特有样式 */
.operation-types {
  display: flex;
  gap: 20rpx;
}

.operation-type {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.operation-type.active {
  background-color: #e6f7f7;
  color: #2AC6C8;
  position: relative;
}

.operation-type.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #2AC6C8;
  border-radius: 2rpx;
}

/* 类型选择特有样式 */
.type-options {
  display: flex;
  gap: 20rpx;
}

.type-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.type-option.active {
  background-color: #e6f7f7;
  color: #2AC6C8;
  position: relative;
}

.type-option.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #2AC6C8;
  border-radius: 2rpx;
}

.type-warning {
  font-size: 24rpx;
  color: #FF5151;
  margin-top: 10rpx;
}

/* 已选时间展示样式 */
.selected-times {
  margin-bottom: 30rpx;
}

.time-item {
  background-color: #f9f9f9;
  padding: 10rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.time-notice {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.usage-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.time-value {
  font-size: 36rpx;
  color: #FF5151;
  font-weight: bold;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 101;
}

.cancel-btn, .save-btn, .submit-btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.cancel-btn {
  flex: 1;
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.save-btn {
  flex: 1;
  background-color: #e6f7f7;
  color: #2AC6C8;
  margin-right: 20rpx;
}

.submit-btn {
  flex: 1;
  background-color: #2AC6C8;
  color: #fff;
}

.service-agreement {
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: #fff9f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #FF9400;
}

.service-agreement text {
  font-size: 24rpx;
  color: #FF9400;
  line-height: 1.4;
}

/* 新增的操作模式样式 */
.operation-mode-container {
  display: flex;
  gap: 20rpx;
}

.operation-mode {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.operation-mode.active {
  background-color: #e6f7f7;
  color: #2AC6C8;
  position: relative;
}

.operation-mode.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #2AC6C8;
  border-radius: 2rpx;
}

/* 服务大盒子 */
.service-box {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
</style>
