<template>
  <view class="instrument-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-center">分类</view>
    </view>

    <!-- 搜索框 -->
    <view class="search-box">
      <lk-input type="text" shape="square" v-model="searchKeyword" placeholder="请输入关键词搜索"
        placeholderStyle="color: rgba(0, 0, 0, 0.40);" class="search-input" prefixIcon="search"
        :prefixIconStyle="{ color: 'rgba(0, 0, 0, 0.40)' }" :customStyle="{
          'border-radius': '23rpx',
          'font-size': '28rpx',
          background: '#f3f3f3',
          'color': '#000!important'
        }" @confirm="handleSearch" />
    </view>

    <!-- 主要内容区 -->
    <view class="main-content">
      <!-- 左侧分类栏 -->
      <scroll-view scroll-y class="side-categories">
        <view class="side-category-item" v-for="(category, index) in sideCategories" :key="index"
          :class="{ 'side-category-active': activeSideCategory === index }" @click="handleSideCategory(index)">
          <text>{{ category.name }}</text>
        </view>
      </scroll-view>

      <!-- 右侧内容 -->
      <scroll-view scroll-y class="right-content">
        <!-- 分类标签栏 -->
        <scroll-view scroll-x class="category-scroll" :show-scrollbar="false" @scroll="onCategoryScroll">
          <view class="category-list">
            <view class="category-item" :class="{ 'category-active': activeCategory === index }"
              v-for="(category, index) in categories" :key="index" @click="handleCategoryClick(index)"
              @touchstart="onTouchStart(index)">
              <text>{{ category }}</text>
            </view>
          </view>
        </scroll-view>

        <!-- 仪器列表 -->
        <lk-page-list ref="pageList" embed @mounted="onPageListMounted" :show-more-status="true"
          :custom-style="{ flex: 1 }" @showListChange="onShowListChange">
          <view class="instrument-grid">
            <view class="instrument-item" v-for="item in instrumentList" :key="item.id"
              @click="handleInstrumentDetail(item)">
              <image class="instrument-image" :src="item.picImg || '/static/none.png'" mode="aspectFill"></image>
              <view class="instrument-info">
                <view class="instrument-name">{{ item.name }}</view>
                <view class="instrument-desc">{{ item.description }}</view>
              </view>
            </view>
          </view>
        </lk-page-list>
      </scroll-view>
    </view>

    <!-- 浮动聊天按钮 -->
    <view class="floating-chat-btn" @tap="openCustomerService">
      <lk-svg src="/static/svg/message.svg" width="48rpx" height="48rpx"></lk-svg>
      <text>在线咨询</text>
    </view>

    <!-- 底部选项卡 -->
    <custom-tab-bar :selected="1" />
  </view>
</template>

<script>
import customTabBar from '@/components/custom-tab-bar/custom-tab-bar.vue';
import api from '@/api/instrument';

export default {
  components: {
    customTabBar
  },
  data() {
    return {
      searchKeyword: '',
      activeCategory: 0, // 默认选中第一个子分类
      activeSideCategory: 0, // 默认选中第一个主分类
      categories: ['全部'], // 分类标签，默认只有"全部"
      sideCategories: [], // 主分类列表
      subCategories: [], // 当前主分类的子分类列表
      instrumentList: [], // 仪器列表
      currentCategoryId: null // 当前选中的分类ID
    };
  },
  created() {
    console.log('页面创建，初始 categories:', this.categories);
    this.fetchInstrumentCategories();
  },

  mounted() {
    console.log('页面挂载完成');
    console.log('当前 categories:', this.categories);
    console.log('当前 activeCategory:', this.activeCategory);
  },
  methods: {
    // 获取仪器分类列表
    async fetchInstrumentCategories() {
      try {
        const data = await api.getInstrumentCategoryList();
        if (data.length > 0) {
          console.log(data, 'data');
          this.sideCategories = data;
          // 默认选中第一个主分类
          this.handleSideCategory(0);
        }
      } catch (error) {
        console.error('获取分类失败:', error);
        uni.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      }
    },

    // 分页列表组件挂载完成
    onPageListMounted() {
      console.log('分页组件已挂载，开始配置');
      this.configPageList();
    },

    // 配置分页列表
    configPageList() {
      if (!this.$refs.pageList) {
        console.log('pageList ref 不存在');
        return;
      }

      console.log('开始配置分页列表');
      const self = this;

      this.instrumentList = this.$refs.pageList.config({
        request: async (params) => {
          console.log('分页请求参数:', params);

          // 如果有选中的分类ID，添加到参数中
          if (self.currentCategoryId) {
            params.categoryId = self.currentCategoryId;
          }

          try {
            // 调用API获取数据
            const result = await api.getInstrumentPage(params);
            console.log('分页请求结果:', result);
            console.log('result.records:', result.records);
            console.log('result.total:', result.total);

            return result;
          } catch (error) {
            console.error('API调用失败:', error);
            throw error;
          }
        },
        onRefreshFinish: () => {
          console.log('刷新完成回调');
          console.log('pageData.list:', self.$refs.pageList?.pageData?.list);
          console.log('当前 instrumentList:', self.instrumentList);

          // 手动同步数据
          if (self.$refs.pageList?.pageData?.list) {
            self.instrumentList = self.$refs.pageList.pageData.list;
            console.log('手动同步后的 instrumentList:', self.instrumentList);
          }
        },
        onLoadMoreFinish: () => {
          console.log('加载更多完成回调');
          console.log('pageData.list:', self.$refs.pageList?.pageData?.list);

          // 手动同步数据
          if (self.$refs.pageList?.pageData?.list) {
            self.instrumentList = self.$refs.pageList.pageData.list;
          }
        }
      });

      console.log('config返回的instrumentList:', this.instrumentList);

      // 初始加载数据
      console.log('开始调用refresh');
      this.$refs.pageList.refresh(true);
    },
    // 处理搜索
    handleSearch() {
      const keyword = this.searchKeyword.trim();
      if (!keyword) {
        uni.showToast({
          title: '请输入搜索关键词',
          icon: 'none'
        });
        return;
      }

      // 跳转到搜索页面
      uni.navigateTo({
        url: `/pages/search/index?searchKey=${encodeURIComponent(keyword)}`
      });
    },

    // 切换子分类
    switchCategory(index) {
      console.log('switchCategory 被调用，index:', index);
      console.log('当前 categories:', this.categories);
      console.log('当前 activeCategory:', this.activeCategory);

      this.activeCategory = index;

      // 获取当前选中的子分类ID
      if (index === 0) {
        // 如果选择的是"全部"，则使用主分类ID
        this.currentCategoryId = this.sideCategories[this.activeSideCategory].id;
        console.log('选择全部，使用父分类ID:', this.currentCategoryId);
        console.log('父分类信息:', this.sideCategories[this.activeSideCategory]);
      } else {
        // 否则使用子分类ID
        this.currentCategoryId = this.subCategories[index - 1].id;
        console.log('选择子分类，使用子分类ID:', this.currentCategoryId);
        console.log('子分类信息:', this.subCategories[index - 1]);
      }

      console.log('切换到分类ID:', this.currentCategoryId);
      console.log('新的 activeCategory:', this.activeCategory);

      // 刷新列表
      if (this.$refs.pageList) {
        this.$refs.pageList.refresh(true);
      }
    },

    // 切换主分类
    handleSideCategory(index) {
      if (index < 0 || index >= this.sideCategories.length) return;

      this.activeSideCategory = index;
      const currentMainCategory = this.sideCategories[index];

      // 更新子分类列表
      this.subCategories = currentMainCategory.children || [];

      // 更新分类标签
      this.categories = ['全部'];
      this.subCategories.forEach(item => {
        this.categories.push(item.name);
      });

      console.log('更新后的 categories:', this.categories);
      console.log('subCategories:', this.subCategories);

      // 默认选中"全部"
      this.activeCategory = 0;

      // 设置当前分类ID为主分类ID（默认选中"全部"）
      this.currentCategoryId = currentMainCategory.id;
      console.log('切换主分类，默认选中全部，使用父分类ID:', this.currentCategoryId);
      console.log('当前主分类信息:', currentMainCategory);

      // 刷新列表
      if (this.$refs.pageList) {
        this.$refs.pageList.refresh(true);
      }
    },

    // 处理仪器详情点击
    handleInstrumentDetail(item) {
      uni.navigateTo({
        url: `/pages/instrument/detail?id=${item.id}`
      });
    },

    // 打开客服
    openCustomerService() {
      uni.showToast({
        title: '正在连接客服...',
        icon: 'none'
      });
    },

    // 列表数据变化回调
    onShowListChange(list) {
      console.log('onShowListChange 被调用，数据:', list);
      // 更新仪器列表数据
      this.instrumentList = list;
      console.log('更新后的 instrumentList:', this.instrumentList);
    },

    // 测试滚动事件
    onCategoryScroll(e) {
      console.log('分类栏滚动事件:', e.detail);
    },

    // 测试触摸事件
    onTouchStart(index) {
      console.log('触摸开始，index:', index);
    },

    // 新的点击处理方法
    handleCategoryClick(index) {
      console.log('=== handleCategoryClick 被调用 ===');
      console.log('点击的 index:', index);
      console.log('当前 categories 数组:', this.categories);
      console.log('categories 长度:', this.categories.length);
      console.log('点击的分类名称:', this.categories[index]);

      // 调用原来的方法
      this.switchCategory(index);
    }
  }
};
</script>

<style lang="scss" scoped>
.instrument-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 150rpx;
  /* 为底部导航腾出更多空间 */
  position: relative;
  box-sizing: border-box;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  background-color: #fff;
  position: relative;
}

.nav-center {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-actions {
  position: absolute;
  right: 30rpx;
  display: flex;
  align-items: center;
}

.nav-icon {
  width: 44rpx;
  height: 44rpx;
  margin-left: 30rpx;
}

.nav-scan {
  background-color: #000;
  border-radius: 50%;
  padding: 10rpx;
  width: 30rpx;
  height: 30rpx;
}

/* 搜索框 */
.search-box {
  padding: 50rpx 30rpx 20rpx 30rpx;
  background-color: #fff;
}

.search-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #999;
}

/* 主内容区 */
.main-content {
  display: flex;
  position: relative;
  height: calc(100vh - 220rpx - var(--status-bar-height) - 150rpx);
  /* 减去底部导航栏高度 */
}

/* 左侧分类栏 */
.side-categories {
  width: 180rpx;
  height: 100%;
  background-color: #f8f8f8;
}

.side-category-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90rpx;
  text-align: center;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.90);
  padding: 0 20rpx;
  box-sizing: border-box;
  width: 100%;
}

.side-category-item text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.side-category-active {
  color: #26D1CB;
  position: relative;
  font-weight: 600;
}

.side-category-active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 30rpx;
  width: 6rpx;
  height: 30rpx;
  background-color: #26D1CB;
}

/* 右侧内容 */
.right-content {
  flex: 1;
  background-color: #fff;
  border-top-left-radius: 30rpx;
  overflow: hidden;
}

/* 分类标签栏 */
.category-scroll {
  background-color: #fff;
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 20rpx;
  padding-left: 0;
  white-space: nowrap;
}

.category-item {
  padding: 12rpx 30rpx;
  margin-right: 10rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  white-space: nowrap;
  flex-shrink: 0;
}

.category-active {
  color: #fff;
  background-color: #26c7c4;
}

/* 仪器列表 */
.instrument-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  padding-bottom: 100rpx;
  /* 确保底部内容不被遮挡 */
}

.instrument-item {
  background-color: #fff;
  overflow: hidden;
  border-radius: 8rpx;
}

.instrument-image {
  width: 100%;
  height: 240rpx;
  background-color: #f0f0f0;
}

.instrument-info {
  padding: 15rpx 10rpx;
}

.instrument-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
}

.instrument-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-top: 6rpx;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 浮动聊天按钮 */
.floating-chat-btn {
  position: fixed;
  right: 30rpx;
  bottom: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #26c7c4;
  border-radius: 16rpx;
  padding: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(38, 199, 196, 0.3);
  z-index: 100;
}

.chat-icon {
  width: 44rpx;
  height: 44rpx;
}

.chat-text {
  font-size: 24rpx;
  color: #fff;
  margin-top: 8rpx;
}

.floating-chat-btn {
  position: fixed;
  right: 18rpx;
  top: 80%;
  transform: translateY(-50%);
  background-color: #26D1CB;
  width: 90rpx;
  height: 90rpx;
  border-radius: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 28rpx 4rpx rgba(0, 0, 0, 0.05), 0px 16rpx 20rpx 2rpx rgba(0, 0, 0, 0.06), 0px 10rpx 10rpx -6rpx rgba(0, 0, 0, 0.10);
  padding: 10rpx;
  z-index: 999;

  image {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 4rpx;
  }

  text {
    font-size: 20rpx;
    color: #ffffff;
    font-weight: 500;
  }
}
</style>