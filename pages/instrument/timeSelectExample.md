# 智能时间选择功能使用示例

## 功能概述

智能时间选择功能根据以下数据源来判断用户可选的时间段：

1. **instrumentSchedules** - 普遍日期设定（按用户等级配置）
2. **instrumentScheduleSpecificDates** - 特定日期设定
3. **holidayList** - 节假日列表
4. **bookedTimeSlots** - 已预约时间段
5. **userLevel** - 用户等级（从store获取）

## 配置数据结构

### 1. 普遍配置 (instrumentSchedules) - 从仪器详情接口获取

```javascript
// 后台返回的实际数据格式
[
  {
    id: 1,
    instrumentId: 123,
    roleType: 1, // 1级用户
    groups: [
      {
        id: 1,
        instrumentScheduleId: 1,
        isWorkday: 1, // 1=法定工作日, 0=自定义星期
        unfixDays: '', // 自定义星期时使用，如 '1,2,3,4,5'
        timeslots: [
          {
            id: 1,
            scheduleGroupId: 1,
            startTime: { hour: 9, minute: 0, second: 0, nano: 0 },
            endTime: { hour: 12, minute: 0, second: 0, nano: 0 },
            createTime: "2024-01-01T00:00:00",
            updateTime: "2024-01-01T00:00:00",
            isDeleted: 0
          },
          {
            id: 2,
            scheduleGroupId: 1,
            startTime: { hour: 14, minute: 0, second: 0, nano: 0 },
            endTime: { hour: 17, minute: 0, second: 0, nano: 0 },
            createTime: "2024-01-01T00:00:00",
            updateTime: "2024-01-01T00:00:00",
            isDeleted: 0
          }
        ],
        createTime: "2024-01-01T00:00:00",
        updateTime: "2024-01-01T00:00:00",
        isDeleted: 0
      }
    ],
    createTime: "2024-01-01T00:00:00",
    updateTime: "2024-01-01T00:00:00",
    isDeleted: 0
  }
]
```

### 2. 特定日期配置 (instrumentScheduleSpecificDates) - 从仪器详情接口获取

```javascript
// 后台返回的实际数据格式
[
  {
    id: 1,
    instrumentId: 123,
    roleType: 1,
    specificDate: '2025-01-01',
    dateType: 1, // 1:关闭整天 2:自定义时间段
    timeslots: [],
    createTime: "2024-01-01T00:00:00",
    updateTime: "2024-01-01T00:00:00",
    isDeleted: 0
  },
  {
    id: 2,
    instrumentId: 123,
    roleType: 2,
    specificDate: '2025-01-01',
    dateType: 2, // 自定义时间段
    timeslots: [
      {
        id: 1,
        specificDateId: 2,
        startTime: { hour: 10, minute: 0, second: 0, nano: 0 },
        endTime: { hour: 15, minute: 0, second: 0, nano: 0 },
        createTime: "2024-01-01T00:00:00",
        updateTime: "2024-01-01T00:00:00",
        isDeleted: 0
      }
    ],
    createTime: "2024-01-01T00:00:00",
    updateTime: "2024-01-01T00:00:00",
    isDeleted: 0
  }
]
```

### 3. 节假日配置 (holidayList)

```javascript
[
  {
    id: 1,
    year: 2025,
    month: 1,
    day: 1,
    type: 1, // 1=节假日, 2=工作日, 3=正常周末
    status: 1, // 1=正常, 2=禁用
    holidayDate: '2025-01-01'
  }
]
```

## 判断逻辑

### 时间段可用性判断流程

1. **节假日检查** - 如果是节假日且状态为禁用，则不可预约
2. **用户权限检查** - 检查用户等级是否有权限预约此时间段
3. **已预约检查** - 检查时间段是否已被其他用户预约
4. **特定日期配置** - 优先检查是否有特定日期的配置
5. **普遍配置检查** - 根据用户等级和日期类型检查普遍配置

### 工作日判断逻辑

```javascript
// 法定工作日判断
if (group.isWorkday === 1) {
  // 检查节假日配置
  const holiday = this.holidayList.find(h => h.holidayDate === date);
  if (holiday) {
    switch (holiday.type) {
      case 1: return false; // 节假日，不可预约
      case 2: return true;  // 调休日，可预约
      case 3: return false; // 正常周末，不可预约
    }
  } else {
    // 周一到周五为工作日
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  }
}

// 自定义星期判断
if (group.isWorkday === 0) {
  const allowedDays = group.unfixDays.split(',').map(Number);
  const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
  return allowedDays.includes(adjustedDayOfWeek);
}
```

## 使用示例

### 1. 从预约页面跳转

```javascript
// 在预约页面中
goToTimeSelect() {
  const params = {
    title: encodeURIComponent('仪器名称'),
    instrumentId: this.instrumentId,
    selectedDates: encodeURIComponent(JSON.stringify(this.formData.appointmentTimes))
  };
  
  const queryString = Object.keys(params)
    .map(key => `${key}=${params[key]}`)
    .join('&');
    
  uni.navigateTo({
    url: `/pages/instrument/timeSelect?${queryString}`
  });
}
```

### 2. 监听返回结果

```javascript
// 在预约页面的 onShow 中
uni.$on("selectReserveTime", (result) => {
  if (result && result.selectedTimes) {
    this.formData.appointmentTimes = [...result.selectedTimes];
    this.recalculatePrice();
  }
});
```

## 配置示例场景

### 场景1：1级用户只能工作日预约
- 配置工作日时间段：09:00-12:00, 14:00-17:00
- 自动过滤节假日和周末

### 场景2：2级用户可以预约工作日和周末
- 工作日：08:00-18:00
- 周末：10:00-16:00

### 场景3：3级用户全天候预约
- 全周：00:00-23:59

### 场景4：特殊日期配置
- 元旦：1级用户全天关闭，2级用户10:00-15:00可预约

## 扩展功能

1. **权限控制** - 可根据用户等级设置不同的预约权限
2. **时间限制** - 支持最小预约时长和时间间隔控制
3. **冲突检测** - 自动检测已预约时间段冲突
4. **节假日智能** - 自动识别法定节假日和调休日
5. **配置灵活** - 支持特定日期覆盖普遍配置

这个智能时间选择功能为用户提供了精确的时间段控制，确保预约系统的高效运行。
