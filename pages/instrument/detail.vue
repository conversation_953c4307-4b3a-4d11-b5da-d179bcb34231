<template>
  <view class="detail-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-text">{{ error }}</view>
      <view class="retry-btn" @click="fetchInstrumentDetail">重试</view>
    </view>

    <!-- 正常内容 -->
    <view v-else>
      <!-- 顶部图片 -->
      <view class="banner-wrapper">
        <image :src="displayImage" mode="aspectFill" class="banner-image"></image>

        <!-- 返回按钮 -->
        <view class="back-btn" @click="goBack">
          <image src="/static/icons/back.png" mode="aspectFit"></image>
        </view>

        <!-- 详情文字 -->
        <view class="detail-text">详情</view>
      </view>

      <view class="content-wrapper">
        <!-- 仪器标题和简介整体卡片 -->
        <view class="content-card">
          <view class="instrument-header">
            <view class="instrument-title">
              <text>{{ instrument.name || '仪器名称' }}</text>
              <view class="status-tag" :class="'status-' + instrument.status">{{ displayStatus }}</view>
            </view>

            <!-- 基本信息 -->
            <view class="info-list">
              <view class="info-item">
                <view class="info-icon">
                  <lk-svg src="/static/svg/instrument_detail2.svg" width="40rpx" height="40rpx"></lk-svg>
                </view>
                <text class="info-label">型号</text>
                <text class="info-value">{{ instrument.model || '暂无' }}</text>
              </view>

              <view class="info-item">
                <view class="info-icon">
                  <lk-svg src="/static/svg/instrument_detail1.svg" width="40rpx" height="40rpx"></lk-svg>
                </view>
                <text class="info-label">负责人</text>
                <text class="info-value">{{ displayManager.phone }} ({{ displayManager.name }})</text>
              </view>
            </view>
          </view>

          <!-- 仪器简介 -->
          <view class="instrument-intro">
            <view class="section-title">仪器简介</view>
            <view class="section-content">
              <text class="content-text">{{ instrument.introduction || instrument.description || '暂无仪器简介' }}</text>
            </view>
          </view>

        </view>

        <!-- 负责人信息 -->
        <view class="content-card">
          <view class="section-box">
            <view class="section-title">负责人</view>
            <view class="staff-card">
              <view class="staff-info">
                <text class="staff-name">{{ displayOperator.name }}</text>
                <text class="staff-title">{{ displayOperator.title }}</text>
              </view>
              <view class="staff-desc" v-if="displayOperator.description">
                <text>{{ displayOperator.description }}</text>
              </view>
              <view class="staff-desc" v-else-if="instrument.curatorIntroduction">
                <text>{{ instrument.curatorIntroduction }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 样品要求 -->
        <view class="content-card">
          <view class="section-box">
            <view class="section-title">样品要求</view>
            <!-- 显示 detailIntroduction 字段内容 -->
            <view v-if="instrument.detailIntroduction" class="section-content">
              <text class="content-text">{{ instrument.detailIntroduction }}</text>
            </view>

            <!-- 如果没有 detailIntroduction，显示默认的样品要求 -->
            <view v-else class="requirements-list">
              <view class="requirement-item" v-for="(requirement, index) in instrument.requirements" :key="index">
                <text class="item-number">{{ index + 1 }}.</text>
                <text class="item-text">{{ requirement }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部操作栏 -->
        <view class="action-bar">
          <view class="action-btn consult-btn" @click="handleConsult">
            <text>专业咨询</text>
          </view>
          <view class="action-btn reserve-btn" @click="handleReserve">
            <text>立刻预约</text>
          </view>
        </view>
      </view>
    </view>
    <verification-popup
			:show="showVerificationPopup"
			@cancel="hideVerificationPopup"
			@confirm="goToVerification"
		></verification-popup>
  </view>
</template>

<script>
import instrumentApi from '@/api/instrument.js'
import { InstrumentStatus, InstrumentStatusText, FeeMethod, FeeMethodText } from '@/constants/instrument.js'
import verificationPopup from '../user/component/verification-popup.vue';

export default {
  components: {
    verificationPopup,
  },
  data() {
    return {
      showVerificationPopup: false, // 控制实名认证提示弹框显示
      instrumentId: null,
      tmbId: null,
      loading: true,
      error: null,
      instrument: {
        // 默认数据，将被API数据覆盖
        id: null,
        name: '',
        status: InstrumentStatus.ONLINE,
        model: '',
        description: '',
        introduction: '',
        detailIntroduction: '',
        picImgUrl: '',
        qrCodeImgUrl: '',
        curatorImgUrl: '',
        curatorIntroduction: '',
        curatorResponseList: [],
        operatorResponseList: [],
        feeMethod: FeeMethod.BY_HOUR,
        price: 0,
        feeUnit: 0,
        trainFeeMethod: null,
        trainFee: 0,
        deliveryTime: '',
        consumables: [],
        incrementServices: [],
        // 保留一些默认的展示数据作为fallback
        requirements: [
          '固体粉末样品需提供至少50mg，液体样品至少5ml',
          '按照元素个数收费，消解额外计算费用',
          'ICP 是一种消耗性测试，仅能回收未进行测试的剩余样品',
          '不接受有毒、有害、易燃、易挥发的样品',
          '由于复杂组分样品（如矿石类），测试的可能会收到光谱干扰（尤其稀土元素），因此建议使用ICP-MS进行测试'
        ]
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.instrumentId = parseInt(options.id);
      this.tmbId = options.tmbId ? parseInt(options.tmbId) : 0;
      // 获取仪器详情
      this.fetchInstrumentDetail();
    } else {
      this.error = '缺少仪器ID参数';
      this.loading = false;
    }
  },
  computed: {
    // 显示的仪器状态文本
    displayStatus() {
      return this.instrument.statusText || InstrumentStatusText[this.instrument.status] || '未知状态';
    },

    // 显示的负责人信息
    displayManager() {
      return this.instrument.managerInfo || {
        name: '暂无',
        phone: ''
      };
    },

    // 显示的操作员信息
    displayOperator() {
      return this.instrument.operatorInfo || {
        name: '暂无',
        title: '实验室技术人员',
        description: ''
      };
    },

    // 显示的仪器图片
    displayImage() {
      return this.instrument.picImgUrl || '/static/instrument/detail_banner.png';
    },

    // 显示的二维码图片
    displayQrCode() {
      return this.instrument.qrCodeImgUrl || '';
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    hideVerificationPopup() {
			this.showVerificationPopup = false;
		},
    goToVerification() {
			this.hideVerificationPopup();
			uni.navigateTo({
				url: '/pages/user/verification'
			});
		},
    async fetchInstrumentDetail() {
      try {
        this.loading = true;
        this.error = null;

        // 调用仪器详情接口，直接获取 data 内容
        const data = await instrumentApi.getInstrumentDetail({
          id: this.instrumentId,
          tmbId: this.tmbId
        });

        if (data) {
          // 更新仪器数据
          this.instrument = {
            ...this.instrument, // 保留默认数据
            ...data,           // 覆盖API返回的数据
            // 处理状态文本显示
            statusText: InstrumentStatusText[data.status] || '未知状态',
            // 处理收费方式文本
            feeMethodText: FeeMethodText[data.feeMethod] || '未知收费方式',
            // 处理负责人信息
            managerInfo: this.formatManagerInfo(data.curatorResponseList),
            // 处理操作员信息
            operatorInfo: this.formatOperatorInfo(data.operatorResponseList)
          };

          // 设置页面标题
          if (data.name) {
            uni.setNavigationBarTitle({
              title: data.name
            });
          }
        } else {
          this.error = '获取仪器详情失败';
        }
      } catch (error) {
        // error 对象包含 code, msg, data 字段
        this.error = error.msg || '网络错误，请稍后重试';

        // 显示错误提示
        uni.showToast({
          title: error.msg || '获取仪器详情失败',
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.loading = false;
      }
    },

    // 格式化负责人信息
    formatManagerInfo(curatorList) {
      if (!curatorList || curatorList.length === 0) {
        return { name: '暂无', phone: '' };
      }
      const curator = curatorList[0]; // 取第一个负责人
      return {
        name: curator.name || '暂无',
        phone: curator.phone || ''
      };
    },

    // 格式化操作员信息
    formatOperatorInfo(operatorList) {
      if (!operatorList || operatorList.length === 0) {
        return { name: '暂无', title: '实验室技术人员', description: '' };
      }
      const operator = operatorList[0]; // 取第一个操作员
      return {
        name: operator.name || '暂无',
        title: '实验室技术人员',
        description: '具有丰富的实验室仪器使用和维护经验'
      };
    },
    handleConsult() {
      uni.navigateTo({
        url: '/pages/chat/support?instrumentId=' + this.instrumentId
      });
    },
    handleReserve() {
      if(this.$store.state.userInfo.identityAuthenticationId == 0){
         this.showVerificationPopup = true;
        return;
      }
      uni.navigateTo({
        url: '/pages/instrument/reserve?instrumentId=' + this.instrumentId + '&instrumentName=' + encodeURIComponent(this.instrument.name)
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-container {
  height: 88vh;
  background-color: #f8f8f8;
}

/* 顶部图片 */
.banner-wrapper {
  position: relative;
  width: 100%;
  height: 420rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 返回按钮 */
.back-btn {
  position: absolute;
  left: 30rpx;
  top: calc(var(--status-bar-height) + 20rpx);
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.back-btn image {
  width: 40rpx;
  height: 40rpx;
}

/* 详情文字 */
.detail-text {
  position: absolute;
  top: calc(var(--status-bar-height) + 20rpx);
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

/* 内容卡片 - 将标题和简介合并为一个整体 */
.content-card {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 仪器标题部分 */
.instrument-header {
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f5f5f5;
}

.instrument-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.instrument-title text {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  flex: 1;
}

.status-tag {
  font-size: 24rpx;
  color: #FF8A00;
  font-weight: 500;
  padding: 4rpx 16rpx;
  background-color: rgba(255, 138, 0, 0.1);
  border-radius: 200rpx;
}

/* 基本信息 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-label {
  font-size: 28rpx;
  color: #000000;
  margin-right: 16rpx;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

/* 仪器简介部分 */
.instrument-intro {
  padding: 30rpx;
  background-color: #fff;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #040000;
  margin-bottom: 24rpx;
  position: relative;
}

.section-content {
  padding: 0;
}

.content-text {
  font-size: 28rpx;
  color: #4E5969;
  line-height: 1.6;
  text-align: justify;
  font-weight: 400;
}

/* 其他部分公用样式 */
.section-box {
  padding: 30rpx;
  background-color: #fff;
}

/* 负责人卡片 */
.staff-card {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
}

.staff-info {
  display: flex;
  align-items: center;
  margin-bottom: 48rpx;
  justify-content: space-between;
}

.staff-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #101217;
  margin-right: 20rpx;
}

.staff-title {
  font-size: 24rpx;
  color: #26D1CB;
  border-radius: 6rpx;
  font-weight: 500;
  padding: 4rpx 16rpx;
  background-color: #fff;
}

.staff-desc {
  font-size: 28rpx;
  color: #4E5969;
  font-weight: 400;
  line-height: 1.6;
}

/* 样品要求列表 */
.requirements-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.requirement-item {
  display: flex;
}

.item-number {
  font-size: 28rpx;
  color: #040000;
  margin-right: 10rpx;
  font-weight: bold;
}

.item-text {
  font-size: 28rpx;
  color: #040000;
  flex: 1;
  line-height: 1.5;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-btn {
  width: 45%;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.consult-btn {
  border: 2rpx solid #26D1CB;
  background-color: #FFF;
  color: #26D1CB;
  font-size: 32rpx;
  font-weight: bold;
}

.reserve-btn {
  background-color: #26D1CB;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}


.content-wrapper{
  padding-bottom: 200rpx;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f8f8f8;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f8f8f8;
  padding: 40rpx;
}

.error-text {
  font-size: 32rpx;
  color: #ff5151;
  text-align: center;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #26d1cb;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 状态标签样式 */
.status-tag.status-1 {
  background-color: #52c41a;
  color: #fff;
}

.status-tag.status-2 {
  background-color: #ff4d4f;
  color: #fff;
}

.status-tag.status-3 {
  background-color: #faad14;
  color: #fff;
}
</style> 