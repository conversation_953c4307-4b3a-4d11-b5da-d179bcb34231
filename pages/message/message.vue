<template>
  <view class="message-page">
    <!-- Tab切换 -->
    <view class="tab-bar">
      <view class="tab-item" :class="{active: tab === 0}" @tap="tab = 0">
        <text>未读</text>
        <view v-if="tab === 0" class="tab-underline"></view>
        <view v-if="unreadCount > 0" class="tab-badge">{{ unreadCount }}</view>
      </view>
      <view class="tab-item" :class="{active: tab === 1}" @tap="tab = 1">
        <text>已读</text>
        <view v-if="tab === 1" class="tab-underline"></view>
      </view>
    </view>

    <!-- 通知列表 -->
    <scroll-view 
      scroll-y 
      class="notice-list" 
      :style="{height: scrollHeight + 'px'}"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-for="(item, idx) in filteredList" :key="idx" class="notice-card" @tap="viewNoticeDetail(item, idx)">
        <view class="notice-header">
          <view class="notice-icon-bg">
            <lk-svg src="/static/svg/notice.svg" width="24rpx" height="24rpx"></lk-svg>
          </view>
          <view class="notice-title">{{ item.title }}</view>
        </view>
        <view class="notice-content">{{ item.content }}</view>
        <view class="notice-footer" v-if="tab === 0">
          <view class="notice-detail">查看详情</view>
          <lk-svg src="/static/user/chevron-right.png" width="48rpx" height="48rpx"></lk-svg>
        </view>
      </view>
      
      <!-- 空状态显示 -->
      <view class="empty-state" v-if="filteredList.length === 0">
        <lk-empty text="暂无通知"></lk-empty>
      </view>
      
      <!-- 底部填充区域，防止被tabbar遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>

    <!-- 底部选项卡 -->
    <custom-tab-bar :selected="userRole === 'admin' ? 1 : 3"></custom-tab-bar>
  </view>
</template>

<script>
import lkSvg from "@/components/lk-svg/lk-svg.vue";
import lkEmpty from "@/components/lk-empty/lk-empty.vue";
import { mapState } from "vuex";

export default {
  components: {
    lkSvg,
    lkEmpty
  },
  data() {
    return {
      tab: 0, // 0:未读 1:已读
      unreadCount: 5,
      scrollHeight: 0,
      noticeList: [
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: false },
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: false },
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: false },
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: false },
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: false },
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: true },
        { title: '通知标题', content: '这是一段通知详情这是一段通知详情这是一段通知详情...', read: true },
      ],
      isRefreshing: false
    };
  },
  computed: {
    ...mapState(["userRole"]),
    filteredList() {
      return this.noticeList.filter(item => this.tab === 0 ? !item.read : item.read);
    }
  },
  onLoad() {
    this.calcScrollHeight();
  },
  onReady() {
    this.calcScrollHeight();
  },
  methods: {
    calcScrollHeight() {
      const systemInfo = uni.getSystemInfoSync();
      
      // 顶部选项卡高度（rpx）
      const tabBarHeight = 90;
      
      // 底部高度不需要在滚动高度计算中减去，因为我们已经添加了底部填充区域
      // 只需要减去顶部选项卡高度即可
      this.scrollHeight = systemInfo.windowHeight - uni.upx2px(tabBarHeight);
    },
    viewNoticeDetail(item, idx) {
      // 如果是未读通知，标记为已读
      if (!item.read && this.tab === 0) {
        this.markAsRead(idx);
      }
      
      // 模拟跳转到详情页
      uni.showToast({
        title: `跳转到通知详情页 (标题: ${item.title})`,
        icon: 'none'
      });
      // 实际应用中，这里需要跳转到通知详情页面
      // uni.navigateTo({
      //   url: `/pages/message/message-detail?id=${item.id}`
      // });
    },
    markAsRead(idx) {
      // 根据索引在原始数组中找到对应的通知
      const originalIdx = this.noticeList.findIndex(item => 
        item.title === this.filteredList[idx].title && 
        item.content === this.filteredList[idx].content
      );
      
      if (originalIdx !== -1) {
        // 标记为已读
        this.$set(this.noticeList, originalIdx, {
          ...this.noticeList[originalIdx],
          read: true
        });
        
        // 更新未读计数
        this.updateUnreadCount();
      }
    },
    updateUnreadCount() {
      // 计算未读通知数量
      this.unreadCount = this.noticeList.filter(item => !item.read).length;
    },
    onRefresh() {
      this.isRefreshing = true;
      // 模拟数据刷新
      setTimeout(() => {
        this.isRefreshing = false;
      }, 1000);
    }
  },
  mounted() {
    // 初始化未读计数
    this.updateUnreadCount();
  }
};
</script>

<style lang="scss" scoped>
.message-page {
  min-height: 100vh;
  background: #f6f7fb;
  display: flex;
  flex-direction: column;
}

.tab-bar {
  display: flex;
  background: #fff;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}
.tab-item {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  color: #999;
  padding: 24rpx 0 16rpx 0;
  position: relative;
}
.tab-item.active {
  color: #26D1CB;
  font-weight: bold;
}
.tab-underline {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 48rpx;
  height: 6rpx;
  background: #26D1CB;
  border-radius: 3rpx;
}
.tab-badge {
  position: absolute;
  top: 8rpx;
  left: 60%;
  background: #ff5151;
  color: #fff;
  font-size: 20rpx;
  border-radius: 50%;
  padding: 8rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255,81,81,0.15);
  z-index: 2;
}

.notice-list {
  flex: 1;
  padding: 28rpx 32rpx 70rpx 32rpx;
  background: #f5f8fb;
  box-sizing: border-box;
}

.notice-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 28rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  position: relative;
  overflow: hidden;
}

.notice-card:active {
  background-color: #f9f9f9;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 4rpx;
}

.notice-icon-bg {
  width: 48rpx;
  height: 48rpx;
  background: #26D1CB;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.notice-title {
  font-size: 32rpx;
  color: #101217;
  font-weight: 500;
}
.notice-content {
  font-size: 28rpx;
  color: #4E5969;
  font-weight: 400;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 4rpx;
}
.notice-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;
  padding-top: 14rpx;
  margin-right: 20rpx;
  border-top: 1px solid #f0f0f0;
}
.notice-detail {
  font-size: 28rpx;
  color: #101217;
  font-weight: 400;
}
.notice-arrow {
  font-size: 36rpx;
  color: #666;
  line-height: 1;
}

.empty-state {
  padding: 40rpx 0;
  text-align: center;
}

.bottom-space {
  height: 160rpx;
  width: 100%;
}
</style>