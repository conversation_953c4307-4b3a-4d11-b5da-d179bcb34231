<template>
  <view class="instrument-select-page">
    <!-- 自定义导航栏 -->
    <u-navbar
      title="选择仪器"
      :border-bottom="true"
      left-icon="arrow-left"
      @left-click="goBack"
    ></u-navbar>

    <!-- 搜索框 -->
    <view class="search-container"  :style="{ paddingTop: uNavbarHeight + 'px' }">
      <view class="search-box">
        <u-icon name="search" size="18" color="#999"></u-icon>
        <input
          class="search-input"
          placeholder="搜索仪器名称"
          v-model="searchKeyword"
          @input="onSearchInput"
          @confirm="onSearchConfirm"
        />
        <view class="search-clear" v-if="searchKeyword" @tap="clearSearch">
          <u-icon name="close-circle-fill" size="16" color="#ccc"></u-icon>
        </view>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs" v-if="categories.length > 0">
      <scroll-view scroll-x class="category-scroll">
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: activeCategory === index }"
            v-for="(category, index) in categories"
            :key="category.id"
            @tap="selectCategory(index)"
          >
            {{ category.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索结果提示 -->
    <view class="search-result-tip" v-if="searchKeyword.trim()">
      <text class="tip-text">搜索"{{ searchKeyword }}"的结果</text>
      <text class="result-count">共{{ instrumentList.length }}个结果</text>
    </view>

    <!-- 仪器列表 -->
    <view class="instrument-content">
      <scroll-view
        scroll-y
        class="instrument-scroll"
        @scrolltolower="loadMore"
        :refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <view class="instrument-list">
          <view
            class="instrument-item"
            v-for="instrument in instrumentList"
            :key="instrument.id"
            @tap="selectInstrument(instrument)"
          >
            <!-- 仪器图片 -->
            <view class="instrument-image">
              <image
                :src="instrument.image || '/static/images/default-instrument.png'"
                class="image"
                mode="aspectFill"
                @error="handleImageError"
              />
            </view>

            <!-- 仪器信息 -->
            <view class="instrument-info">
              <view class="instrument-name">{{ instrument.name }}</view>
              <view class="instrument-desc" v-if="instrument.description">
                {{ instrument.description }}
              </view>
              <view class="instrument-meta">
                <text class="meta-item" v-if="instrument.location">
                  📍 {{ instrument.location }}
                </text>
                <text class="meta-item" v-if="instrument.status">
                  {{ getStatusText(instrument.status) }}
                </text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="instrument-action">
              <view class="reserve-btn" @tap.stop="goToReserve(instrument)">
                立即预约
              </view>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="loading">
          <u-loading-icon mode="flower"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" v-if="!hasMore && instrumentList.length > 0">
          <text>没有更多数据了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && instrumentList.length === 0">
          <text class="empty-text">暂无仪器数据</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import instrumentApi from '@/api/instrument';

export default {
  name: 'InstrumentSelect',
  data() {
    return {
      categories: [],
      activeCategory: 0,
      instrumentList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 20,
      currentCategoryId: null,
      searchKeyword: '', // 搜索关键词
      searchTimer: null // 搜索防抖定时器
    };
  },
  onLoad() {
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.fetchCategories();
      if (this.categories.length > 0) {
        this.selectCategory(0);
      }
    },

    // 获取分类列表
    async fetchCategories() {
      try {
        const data = await instrumentApi.getInstrumentCategoryList();
        this.categories = data || [];
        console.log('获取到仪器分类:', this.categories);
      } catch (error) {
        console.error('获取仪器分类失败:', error);
        uni.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      }
    },

    // 选择分类
    selectCategory(index) {
      this.activeCategory = index;
      this.currentCategoryId = this.categories[index]?.id;

      // 选择分类时清除搜索关键词
      this.searchKeyword = '';

      this.resetList();
      this.fetchInstruments();
    },

    // 重置列表
    resetList() {
      this.instrumentList = [];
      this.currentPage = 1;
      this.hasMore = true;
    },

    // 获取仪器列表
    async fetchInstruments(isLoadMore = false) {
      if (this.loading) return;

      this.loading = true;

      try {
        const params = {
          current: this.currentPage,
          size: this.pageSize
        };

        if (this.currentCategoryId) {
          params.categoryId = this.currentCategoryId;
        }

        // 添加搜索关键词
        if (this.searchKeyword.trim()) {
          params.name = this.searchKeyword.trim();
        }

        const result = await instrumentApi.getInstrumentPage(params);
        
        if (isLoadMore) {
          this.instrumentList = [...this.instrumentList, ...result.records];
        } else {
          this.instrumentList = result.records || [];
        }
        
        this.hasMore = this.instrumentList.length < result.total;
        
        console.log('获取到仪器列表:', result);
      } catch (error) {
        console.error('获取仪器列表失败:', error);
        uni.showToast({
          title: '获取仪器失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return;
      
      this.currentPage++;
      this.fetchInstruments(true);
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.resetList();
      this.fetchInstruments();
    },

    // 选择仪器（保留原有功能，用于其他场景）
    selectInstrument(instrument) {
      console.log('选择仪器:', instrument);

      // 通过事件总线传递选中的仪器
      uni.$emit('selectInstrument', {
        id: instrument.id,
        name: instrument.name,
        description: instrument.description,
        location: instrument.location,
        status: instrument.status
      });

      // 返回上一页
      this.goBack();
    },

    // 跳转到预约页面
    goToReserve(instrument) {
      console.log('跳转到预约页面:', instrument);

      // 跳转到预约页面，传递仪器ID和需求订单标识
      uni.navigateTo({
        url: `/pages/instrument/reserve?instrumentId=${instrument.id}&fromDemand=1`
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '正常',
        2: '维护中',
        3: '故障',
        4: '停用'
      };
      return statusMap[status] || '未知';
    },

    // 搜索输入事件（防抖）
    onSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.performSearch();
      }, 500);
    },

    // 搜索确认事件
    onSearchConfirm() {
      // 清除定时器，立即执行搜索
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.performSearch();
    },

    // 执行搜索
    performSearch() {
      console.log('执行搜索:', this.searchKeyword);
      this.resetList();
      this.fetchInstruments();
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.performSearch();
    },

    // 图片加载错误处理
    handleImageError(e) {
      console.log('图片加载失败:', e);
      // 可以在这里设置默认图片或其他处理逻辑
    }
  }
};
</script>

<style lang="scss" scoped>
.instrument-select-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.search-container {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 25rpx;
  padding: 16rpx 24rpx;
  position: relative;
}

.search-input {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;

  &::placeholder {
    color: #999;
  }
}

.search-clear {
  margin-left: 16rpx;
  padding: 8rpx;
}

.search-result-tip {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip-text {
  font-size: 28rpx;
  color: #333;
}

.result-count {
  font-size: 26rpx;
  color: #666;
}

.search-result-tip {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip-text {
  font-size: 28rpx;
  color: #333;
}

.result-count {
  font-size: 26rpx;
  color: #666;
}

.category-tabs {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0; /* 防止被压缩 */
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 20rpx 30rpx;
}

.category-item {
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  white-space: nowrap;
  transition: all 0.3s ease;

  &.active {
    background-color: #26d1cb;
    color: #fff;
  }

  &:last-child {
    margin-right: 30rpx; /* 确保最后一个分类有足够的右边距 */
  }
}

.instrument-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  margin-top: 20rpx;
  min-height: 0; /* 确保flex子项能正确收缩 */
}

.instrument-scroll {
  flex: 1;
  height: 0; /* 配合flex: 1使用 */
}

.instrument-list {
  padding: 20rpx 30rpx;
  padding-bottom: 40rpx; /* 底部留出更多空间 */
}

.instrument-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background-color: #f9f9f9;
    transform: scale(0.98);
  }
}

.instrument-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;

  .image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
  }
}

.instrument-info {
  flex: 1;
}

.instrument-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.instrument-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.instrument-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
  margin-bottom: 4rpx;
}

.instrument-action {
  padding: 10rpx;
}

.reserve-btn {
  background-color: #26d1cb;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  text-align: center;

  &:active {
    background-color: #1fb5b0;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}

.loading-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
