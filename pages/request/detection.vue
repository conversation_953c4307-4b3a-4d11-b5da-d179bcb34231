<template>
  <view class="detection-container">
    <!-- 页面内容区域 -->
    <view class="content-area">
      <!-- 需求名称 -->
      <view class="form-item required">
        <view class="form-label ">
          需求名称 
        </view>
        <input 
          class="form-input" 
          placeholder="请输入" 
          v-model="formData.name"
        />
      </view>
      
      <!-- 需求描述 -->
      <view class="form-item required">
        <view class="form-label ">
          需求描述 
        </view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入" 
          v-model="formData.description"
        />
      </view>
      
      <!-- 添加仪器按钮 -->
      <view class="add-instrument-btn" @tap="addInstrument">
        添加仪器
      </view>
      
      <!-- 仪器列表 -->
      <view class="instrument-list">
        <!-- 已添加的仪器 -->
        <view class="instrument-item form-item required" v-for="(item, index) in formData.instruments" :key="index">
          <view class="instrument-header ">
            <view class="form-label ">
              仪器
            </view>
            <view class="delete-btn" @tap="deleteInstrument(index)">
             <lk-svg src="/static/svg/detection_delete.svg" width="48rpx" height="48rpx"></lk-svg>
            </view>
          </view>
          <view class="instrument-info">
            <view class="instrument-name">{{ item.instrumentName || item.name }}</view>
            <view class="instrument-desc" v-if="item.experimentName">
              实验名称：{{ item.experimentName }}
            </view>
            <view class="instrument-desc" v-if="item.description">{{ item.description }}</view>
            <view class="instrument-meta">
              <text class="meta-item" v-if="item.location">📍 {{ item.location }}</text>
              <text class="meta-item" v-if="item.appointmentTimes && item.appointmentTimes.length > 0">
                ⏰ {{ item.appointmentTimes.length }}个时间段
              </text>
              <text class="meta-item" v-if="item.totalAmount || item.amount">
                💰 ¥{{ (item.totalAmount || item.amount || 0).toFixed(2) }}
              </text>
            </view>
          </view>
        </view>

        <!-- 空状态提示 -->
        <view class="empty-instruments" v-if="formData.instruments.length === 0">
          <text class="empty-text">暂无仪器，请点击上方"添加仪器"按钮添加</text>
        </view>
      </view>
      
      <!-- 支付方式 -->
      <view class="form-item required">
        <view class="form-label ">
          支付方式 
        </view>
        <view class="payment-options">
          <!-- 个人账户余额 -->
          <view class="payment-option" :class="{'active': formData.paymentMethod === 'personal'}" @tap="selectPayment('personal')">
            <view class="check-icon" v-if="formData.paymentMethod === 'personal'">
              <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
            </view>
            <view class="payment-left">
              <view class="payment-tag">
                <text>个人账户余额</text>
              </view>
              <view class="balance-text">当前可用余额: {{personalBalance}} 元</view>
            </view>
            <view class="payment-right">
              <text class="discount-text">无折扣</text>
            </view>
          </view>
          
          <!-- 松山湖团队额度 -->
          <view class="payment-option" :class="{'active': formData.paymentMethod === 'teamCredit'}" @tap="selectPayment('teamCredit')">
            <view class="check-icon" v-if="formData.paymentMethod === 'teamCredit'">
              <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
            </view>
            <view class="payment-left">
              <view class="payment-tag">
                <text>松山湖团队额度</text>
              </view>
              <view class="balance-text">当前可用余额: {{teamCreditBalance}} 元</view>
            </view>
            <view class="payment-right">
              <text class="discount-text discount">可打5折</text>
            </view>
          </view>
          
          <!-- 松山湖团队余额 -->
          <view class="payment-option" :class="{'active': formData.paymentMethod === 'teamBalance'}" @tap="selectPayment('teamBalance')">
            <view class="check-icon" v-if="formData.paymentMethod === 'teamBalance'">
              <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
            </view>
            <view class="payment-left">
              <view class="payment-tag">
                <text>松山湖团队余额</text>
              </view>
              <view class="balance-text">当前可用余额: {{teamBalance}} 元</view>
            </view>
            <view class="payment-right">
              <text class="discount-text discount">可打5折</text>
            </view>
          </view>
          
          <!-- XXX团队额度 -->
          <view class="payment-option" :class="{'active': formData.paymentMethod === 'otherTeamCredit'}" @tap="selectPayment('otherTeamCredit')">
            <view class="check-icon" v-if="formData.paymentMethod === 'otherTeamCredit'">
              <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
            </view>
            <view class="payment-left">
              <view class="payment-tag">
                <text>XXX团队额度</text>
              </view>
              <view class="balance-text">当前可用余额: {{otherTeamCreditBalance}} 元</view>
            </view>
            <view class="payment-right">
              <text class="discount-text discount">可打7折</text>
            </view>
          </view>
          
          <!-- XXX团队余额 -->
          <view class="payment-option" :class="{'active': formData.paymentMethod === 'otherTeamBalance'}" @tap="selectPayment('otherTeamBalance')">
            <view class="check-icon" v-if="formData.paymentMethod === 'otherTeamBalance'">
              <lk-svg src="/static/svg/pay_check.svg" width="28rpx" height="28rpx"></lk-svg>
            </view>
            <view class="payment-left">
              <view class="payment-tag">
                <text>XXX团队余额</text>
              </view>
              <view class="balance-text">当前可用余额: {{otherTeamBalance}} 元</view>
            </view>
            <view class="payment-right">
              <text class="discount-text discount">可打7折</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮区域 -->
    <view class="bottom-buttons">
      <view class="cancel-btn" @tap="handleCancel">取消</view>
      <view class="save-btn" @tap="handleSave">暂时保存</view>
      <view class="submit-btn" @tap="handleSubmit">提交</view>
    </view>
  </view>
</template>

<script>
import lkSvg from "@/components/lk-svg/lk-svg.vue";
import orderApi from "@/api/order";

export default {
  components: {
    lkSvg
  },
  data() {
    return {
      formData: {
        name: '',
        description: '',
        instruments: [], // 默认为空数组，用户主动添加仪器
        paymentMethod: 'personal'
      },
      personalBalance: '00.00',
      teamCreditBalance: '00.00',
      teamBalance: '00.00',
      otherTeamCreditBalance: '00.00',
      otherTeamBalance: '00.00',
      instrumentList: [
        {
          label: '试测',
          value: '试测'
        },
        {
          label: '正式',
          value: '正式'
        }
      ],
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '提交检测需求'
    });
  },
  onShow() {
    // 监听仪器选择事件
    uni.$off('selectInstrument'); // 避免重复监听
    uni.$on('selectInstrument', (instrument) => {
      console.log('接收到选择的仪器:', instrument);
      this.handleInstrumentSelected(instrument);
    });

    // 监听需求订单仪器添加事件
    uni.$off('addDemandInstrument'); // 避免重复监听
    uni.$on('addDemandInstrument', (reservationData) => {
      console.log('接收到预约数据:', reservationData);
      this.handleReservationAdded(reservationData);
    });
  },
  onHide() {
    // 移除事件监听
    uni.$off('selectInstrument');
    uni.$off('addDemandInstrument');
  },
  methods: {
    // 添加仪器
    addInstrument() {
      // 跳转到仪器选择页面
      uni.navigateTo({
        url: '/pages/request/instrumentSelect'
      });
    },

    // 处理仪器选择
    handleInstrumentSelected(instrument) {
      // 检查是否已经添加过该仪器
      const existingIndex = this.formData.instruments.findIndex(item => item.id === instrument.id);

      if (existingIndex !== -1) {
        uni.showToast({
          title: '该仪器已添加',
          icon: 'none'
        });
        return;
      }

      // 添加新仪器到列表
      this.formData.instruments.push({
        id: instrument.id,
        name: instrument.name,
        description: instrument.description,
        location: instrument.location,
        status: instrument.status
      });

      uni.showToast({
        title: '仪器添加成功',
        icon: 'success'
      });
    },

    // 处理预约数据添加
    handleReservationAdded(reservationData) {
      console.log('处理预约数据:', reservationData);

      // 检查是否已经添加过该仪器
      const existingIndex = this.formData.instruments.findIndex(
        item => item.instrumentId === reservationData.instrumentId
      );

      if (existingIndex !== -1) {
        // 更新现有仪器的预约信息
        this.formData.instruments[existingIndex] = {
          ...this.formData.instruments[existingIndex],
          ...reservationData
        };
        console.log('仪器预约信息已更新');
      } else {
        // 添加新的仪器预约
        this.formData.instruments.push(reservationData);
        console.log('仪器预约添加成功');
      }
    },
    
    // 删除仪器
    deleteInstrument(index) {
      this.formData.instruments.splice(index, 1);
    },
    // 选择支付方式
    selectPayment(method) {
      this.formData.paymentMethod = method;
    },
    
    // 取消
    handleCancel() {
      uni.navigateBack();
    },
    
    // 暂时保存
    handleSave() {
      uni.showToast({
        title: '已保存',
        icon: 'success'
      });
    },
    
    // 提交
    async handleSubmit() {
      // 表单验证
      if (!this.formData.name) {
        uni.showToast({
          title: '请输入需求名称',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.description) {
        uni.showToast({
          title: '请输入需求描述',
          icon: 'none'
        });
        return;
      }

      // 检查是否有预约仪器
      if (this.formData.instruments.length === 0) {
        uni.showToast({
          title: '请至少添加一个仪器预约',
          icon: 'none'
        });
        return;
      }

      // 检查支付方式
      if (!this.formData.paymentMethod) {
        uni.showToast({
          title: '请选择支付方式',
          icon: 'none'
        });
        return;
      }

      await this.submitDemandOrder();
    },

    // 提交需求订单
    async submitDemandOrder() {
      try {
        uni.showLoading({
          title: '提交中...'
        });

        // 构建需求订单数据
        const demandOrderData = this.buildDemandOrderData();

        console.log('提交需求订单数据:', demandOrderData);

        // 调用需求订单API
        const result = await orderApi.createDemandOrder(demandOrderData);

        uni.hideLoading();

        if (result) {
          uni.showToast({
            title: '提交成功',
            icon: 'success'
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      } catch (error) {
        uni.hideLoading();
        console.error('提交需求订单失败:', error);
        uni.showToast({
          title: error.msg || '提交失败，请重试',
          icon: 'none'
        });
      }
    },

    // 构建需求订单数据
    buildDemandOrderData() {
      return {
        experimentName: this.formData.name,
        remark: this.formData.description,
        status: 1, // 提交状态
        payType: this.getPayTypeValue(),
        teamId: this.getTeamId(),
        userId: this.getCurrentUserId(),
        instruments: this.formData.instruments.map(instrument => ({
          ...instrument,
          status: 1, // 订单状态
          userId: this.getCurrentUserId(),
          payType: this.getPayTypeValue(),
          teamId: this.getTeamId()
        }))
      };
    },

    // 获取支付方式值
    getPayTypeValue() {
      const payTypeMap = {
        'personalBalance': 1, // 个人账户余额
        'teamBalance': 2,     // 团队余额
        'teamCredit': 3,      // 团队额度
        'otherTeamCredit': 3, // 其他团队额度
        'otherTeamBalance': 2 // 其他团队余额
      };
      return payTypeMap[this.formData.paymentMethod] || 1;
    },

    // 获取团队ID
    getTeamId() {
      // 根据支付方式返回对应的团队ID
      if (this.formData.paymentMethod === 'teamBalance' || this.formData.paymentMethod === 'teamCredit') {
        return 1; // 松山湖团队ID，实际应该从用户信息中获取
      }
      return 0; // 个人账户
    },

    // 获取当前用户ID
    getCurrentUserId() {
      // 实际应该从用户状态管理中获取
      return 1; // 临时返回固定值
    }
  }
};
</script>

<style lang="scss" scoped>
.detection-container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

.content-area {
  flex: 1;
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}


.form-label {
  font-size: 32rpx;
  color: #4e5969;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 400;
}
.form-item.required .form-label::after {
  content: "*";
  color: #ff5151;
  margin-left: 4rpx;
}



.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.add-instrument-btn {
  width: 100%;
  height: 80rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #00C0D4;
  font-weight: 600;
  margin-bottom: 30rpx;
  border: 1px solid #00C0D4;
}

.instrument-list {
  margin-bottom: 30rpx;
}

.instrument-item {
  margin-bottom: 20rpx;
}

.instrument-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.instrument-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.delete-btn {
  width: 40rpx;
  height: 40rpx;
}

.delete-icon {
  width: 100%;
  height: 100%;
}

.instrument-info {
  background-color: #F9FAFB;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 16rpx;
}

.instrument-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.instrument-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.instrument-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.empty-instruments {
  padding: 60rpx 30rpx;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 16rpx;
  margin-top: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  line-height: 1.5;
}

.instrument-selector {
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-text {
  font-size: 28rpx;
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.payment-option {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  border: 1px solid #DCDCDC;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.payment-option.active {
  border-color: #40E0D0;
  background-color: rgba(64, 224, 208, 0.05);
}

.payment-option.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border-top: 40rpx solid #40E0D0;
  border-right: 40rpx solid transparent;
  z-index: 1;
}

.check-icon {
  position: absolute;
  left: 0rpx;
  top: -6rpx;
  width: 28rpx;
  height: 28rpx;
  z-index: 2;
}

.payment-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: flex-start;
}

.payment-tag {
  font-size: 32rpx;
  color: #101217;
  font-weight: 400;
  margin-bottom: 10rpx;
}

.balance-text {
  font-size: 28rpx;
  color: #4E5969;
  font-weight: 400;
}

.payment-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.discount-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.60);
  font-weight: 400;
  text-decoration: line-through;
}

.discount-text.discount {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 136, 0, 0.90);
  text-decoration: none;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 20rpx 60rpx 20rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  justify-content: space-between;
  z-index: 3;
}

.cancel-btn, .save-btn, .submit-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin: 0 10rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #ffffff;
  color: #4E5969;
  border: 1px solid #E5E6EB;
}

.save-btn {
  background-color: #fff;
  color: #26D1CB;
  border: 1px solid #26D1CB;
}

.submit-btn {
  background-color: #26D1CB;
  color: #fff;
  border: none;
}
</style> 