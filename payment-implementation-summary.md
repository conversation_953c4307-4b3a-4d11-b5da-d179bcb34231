# 需求页面支付功能实现总结

## 学习预约页面的支付写法

### 预约页面 (pages/instrument/reserve.vue) 的支付实现特点：

1. **动态获取支付方式**：
   - 使用 `orderApi.getUserPayment()` 获取用户可用的支付方式
   - 支付方式数据结构：`{ payType, balance, discount, teamId, teamName }`

2. **支付方式选择**：
   - 使用索引 `paymentMethodIndex` 来标识选中的支付方式
   - 通过 `selectPayment(index)` 方法处理选择

3. **支付方式显示**：
   - 动态循环渲染支付方式选项
   - 根据 `payType` 显示不同的支付方式名称
   - 显示余额和折扣信息

4. **数据提交**：
   - 使用 `getPayTypeValue()` 获取支付类型值
   - 使用 `getTeamId()` 获取团队ID（如果是团队支付）

## 需求页面的支付功能实现

### 1. 模板修改
- 将静态的支付方式选项改为动态循环渲染
- 使用 `v-for` 循环 `paymentMethods` 数组
- 添加无支付方式时的提示

### 2. 数据结构修改
```javascript
data() {
  return {
    formData: {
      paymentMethodIndex: -1 // 改为使用索引
    },
    paymentMethods: [], // 支付方式列表
    paymentLoading: false // 加载状态
  }
}
```

### 3. 新增方法

#### 获取支付方式
```javascript
async fetchPaymentMethods() {
  const data = await orderApi.getUserPayment();
  this.paymentMethods = data;
  this.updateBalanceDisplay(data);
}
```

#### 支付方式处理
```javascript
selectPayment(index) {
  this.formData.paymentMethodIndex = index;
}

getPaymentMethodName(method) {
  // 根据 payType 返回支付方式名称
}

getDiscountText(discount) {
  // 返回折扣文本
}
```

#### 数据提交
```javascript
getPayTypeValue() {
  const selectedMethod = this.paymentMethods[this.formData.paymentMethodIndex];
  return selectedMethod.payType;
}

getTeamId() {
  const selectedMethod = this.paymentMethods[this.formData.paymentMethodIndex];
  return selectedMethod.teamId || 0;
}
```

### 4. 页面生命周期
- 在 `onLoad()` 中调用 `fetchPaymentMethods()` 获取支付方式

### 5. 表单验证
- 验证 `paymentMethodIndex` 是否有效
- 检查 `paymentMethods` 数组是否为空

## API 接口字段说明

### getUserPayment 接口返回数据结构：
```javascript
[{
  id: number,           // 用户ID
  userId: number,       // 用户ID
  name: string,         // 用户名称
  phoneNumber: string,  // 手机号
  payType: number,      // 付款方式 1:个人账户 2:团队余额 3:团队额度
  balance: number,      // 余额
  discount: number,     // 折扣
  teamId: number,       // 团队ID
  teamName: string      // 团队名称
}]
```

### createDemandOrder 接口参数：
```javascript
{
  experimentName: string,  // 需求名称
  remark: string,         // 需求描述
  status: number,         // 状态 0:草稿 1:提交
  payType: number,        // 支付方式
  teamId: number,         // 团队ID
  userId: number,         // 用户ID
  instruments: [{
    // 仪器预约数据
    payType: number,      // 支付方式
    teamId: number,       // 团队ID
    // ... 其他字段
  }]
}
```

## 关键改进点

1. **统一支付方式管理**：使用动态获取的支付方式，而不是硬编码
2. **数据一致性**：确保需求订单和子订单使用相同的支付方式和团队ID
3. **用户体验**：显示实时的余额和折扣信息
4. **错误处理**：添加支付方式获取失败的处理逻辑

## 测试要点

1. 页面加载时是否正确获取支付方式
2. 支付方式选择是否正常工作
3. 余额和折扣信息是否正确显示
4. 表单验证是否包含支付方式检查
5. 提交时是否正确传递支付相关参数
