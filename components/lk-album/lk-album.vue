<!-- 网格拍摄相册上传 -->
<template>
  <view class="lk-album" :style="[customStyle]">
    <!-- 相册网格 -->
    <view class="lk-album-item-box" v-for="(item, index) in list" :key="index" :style="[innerItemBoxStyle]"
      :class="[{'lk-album-item-top' : index < columnCount, 'lk-album-item-left': index % columnCount == 0}]">
      <view class="lk-album-item" :style="[itemStyle]">
        <!-- 图片 -->
        <image v-if="type == 'image'" class="lk-album-item-content" :src="item.fileUrl" @tap.stop="previewMedia(item)">
        </image>

        <!-- 视频 -->
        <view v-else class="lk-album-item-content" @tap.stop="previewMedia(item)">
          <view class="lk-album-item-content-video">
            <u-icon v-if="item.status == 'uploaded'" name="play-circle" size="30" color="#FFF"></u-icon>
          </view>
        </view>

        <!-- 重传 -->
        <view class="lk-album-item-reupload" v-if="item.status == 'failed'" @tap.stop="reuploadMedia(item)">重新上传</view>

        <!-- 转圈 -->
        <view class="lk-album-item-uploading" v-if="item.status == 'uploading'">
          <u-loading-icon color="#FFFFFF"></u-loading-icon>
        </view>

        <!-- 删除 -->
        <view class="lk-album-item-del" v-if="modifiable && removable" @tap.stop="deleteMedia(item)">
          <u-icon name="close" color="#FFFFFF" size="24rpx"></u-icon>
        </view>
      </view>
    </view>

    <!-- 拍照 -->
    <template v-if="modifiable && list.length < limitCount && !hideButton">
      <!-- 单独一行的按钮 -->
      <template v-if="aloneButton">
        <view class="lk-album-alone-button-gap"></view>
        <view class="lk-album-alone-button" :style="[innerButtonStyle, buttonStyle]" @tap.stop="open">
          <slot name="alone-button">
            <u-icon :name="buttonIcon" :color="buttonIconColor" :size="buttonIconSize"></u-icon>
          </slot>
        </view>
      </template>
      <!-- 和其他图片一样大小的按钮 -->
      <view v-else class="lk-album-item-box" :style="[innerItemBoxStyle]"
        :class="[{'lk-album-item-top' : list.length < columnCount, 'lk-album-item-left': list.length % columnCount == 0}]">
        <view class="lk-album-item lk-album-button" :style="[innerButtonStyle, itemStyle, buttonStyle]"
          @tap.stop="open">
          <slot name="button">
            <u-icon :name="buttonIcon" :color="buttonIconColor" :size="buttonIconSize"></u-icon>
          </slot>
        </view>
      </view>
    </template>

    <view v-if="!modifiable && list.length == 0" class="lk-album-empty">
      <slot name="empty">
        {{ type == 'image' ? '暂无图片~' : '暂无视频~' }}
      </slot>
    </view>

    <!-- 弹框 -->
    <view class="tools" @touchmove.native.prevent="() => {}">

      <!-- #ifdef MP-WEIXIN -->
      <lk-mp-privacy v-if="mpPrivacy"></lk-mp-privacy>
      <!-- #endif -->

      <u-popup v-if="previewVideo != null" :show="true" class="tools-video-preview-popup" mode="center"
        @close="previewVideo = null" :customStyle="{
          'backgroundColor': 'transparent'
        }">
        <video class="tools-video-preview-popup-video" :src="previewVideo && previewVideo.fileUrl" :autoplay="true"
          :controls="true"></video>
        <view class="tools-video-preview-popup-close" @tap.stop="previewVideo = null">
          <u-icon name="close" size="30" color="#FFF"></u-icon>
        </view>
      </u-popup>

      <u-popup mode="bottom" bgColor="transparent" :show="showChoosePopup" @close="showChoosePopup = false"
        :safeAreaInsetBottom="false">
        <view class="tools-choose-popup">
          <text class="tools-choose-popup-item" @tap.stop="openCamera">拍摄</text>
          <view class="tools-choose-popup-divider" />
          <text class="tools-choose-popup-item" @tap.stop="openAlbum">从手机相册选择</text>
          <view class="tools-choose-popup-divider" />
          <text class="tools-choose-popup-item" @tap.stop="showChoosePopup = false">取消</text>
          <u-safe-bottom></u-safe-bottom>
        </view>
      </u-popup>
      <!-- #ifdef APP -->
      <u-modal :show="showPermission" @confirm="permissionConfirm" @cancel="permissionCancel"
        :content="permissionContent" :asyncClose="true" :showCancelButton="true" confirmText="好" cancelText="不允许">
      </u-modal>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
  import commonApi from '@/api/common.js'

  // #ifdef APP
  import permission from '@/common/permission.js'
  // #endif

  export default {
    name: 'lk-album',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      // 初始文件列表
      value: {
        type: [Array, String, Object],
        default: null,
      },
      // auto, url, url-array, key, key-array, object, object-notnull, object-array
      valueType: {
        type: String, 
        default: 'auto'
      },
      valueDelimiter: {
        type: String,
        default: ','
      },
      // { 
      //  name: 'fileName',
      //  noName: false,
      //  url: 'fileUrl', 
      //  noUrl: false,
      //  key: 'fileKey' 
      //  noKey: false,
      // }
      valueProp: {
        type: Object,
        default: null
      },
      // 文件类型 image video
      type: {
        type: String,
        default: 'image',
      },
      // 来源 camera album
      source: {
        type: String,
        default: '',
      },
      // 列数
      columnCount: {
        type: Number,
        default: 3,
      },
      gapSize: {
        type: String,
        default: '20rpx'
      },
      // 限制数量
      limitCount: {
        type: Number,
        default: 999,
      },
      // 文件大小限制
      limitSize: {
        type: Number,
        default: 10 * 1024 * 1024,
      },
      removable: {
        type: Boolean,
        default: true
      },
      singleReplace: {
        type: Boolean,
        default: false,
      },
      // 可修改
      modifiable: {
        type: Boolean,
        default: true,
      },
      customStyle: {
        type: Object,
        default: null,
      },
      itemStyle: {
        type: Object,
        default: null,
      },
      hideButton: {
        type: Boolean,
        default: false
      },
      aloneButton: {
        type: Boolean,
        default: false
      },
      buttonIcon: {
        type: String,
        default: 'plus'
      },
      buttonIconSize: {
        type: String,
        default: '40rpx'
      },
      buttonIconColor: {
        type: String,
        default: '#AAA'
      },
      buttonStyle: {
        type: Object,
        default: null
      },
      // 内嵌小程序授权弹框
      mpPrivacy: {
        type: Boolean,
        default: true,
      }
    },
    watch: {
      value: {
        handler(val) {
          this.setFiles(val)
        },
        immediate: true
      },
      showChoosePopup(val) {
        this.$emit('showOperatePopupChange', val)
      }
    },
    data() {
      return {
        showChoosePopup: false, // 弹框选择菜单
        showPermission: false, // 权限弹框
        permissionContent: null, // 权限提示内容
        list: [], // 相册列表
        previewVideo: null,
        emitedFileUrls: null,
        autoValueType: '',
      }
    },
    computed: {
      innerButtonStyle() {
        const style = {}
        if (this.aloneButton) {
          style.marginTop = this.list.length > 0 ? this.gapSize : 0
        }
        return style
      },
      innerItemBoxStyle() {
        const columnCount = Math.min(this.columnCount, this.limitCount)
        const gapSize = this.$u.getPx(this.gapSize)
        const width = `calc((100% - ${(columnCount - 1) * gapSize}px) / ${columnCount})`
        return {
          'width': width,
          'padding-top': width,
          'margin-left': gapSize + 'px',
          'margin-top': gapSize + 'px',
        }
      },
      fileNameName() {
        return this.valueProp?.name || 'fileName'
      },
      noFileName() {
        return this.valueProp?.noName == true
      },
      fileUrlName() {
        return this.valueProp?.url || 'fileUrl'
      },
      noFileUrl() {
        return this.valueProp?.noUrl == true
      },
      fileKeyName() {
        return this.valueProp?.key || 'fileKey'
      },
      noFileKey() {
        return this.valueProp?.noKey == true
      },
    },
    methods: {
      getAutoValueType() {
        if (this.autoValueType) {
          return this.autoValueType
        }
        if (this.value == null) {
          this.autoValueType = this.limitCount == 1 ? 'object' : 'object-array'
        } else if (typeof this.value == 'string') {
          this.autoValueType = 'url'
        } else if (Array.isArray(this.value)) {
          this.autoValueType = typeof this.value[0] == 'string' ? 'url-array' : 'object-array'
        } else {
          this.autoValueType = 'object'
        }
        return this.autoValueType
      },
      setFiles(files) {
        if (!files) {
          this.list = []
          this.emitedFileUrls = null
          return
        }
        const list = []
        if (typeof files == 'string') {
          if (this.limitCount == 1) {
            list.push({
              fileUrl: files,
              fileKey: '',
              status: 'uploaded'
            })
          } else {
            files.split(this.valueDelimiter).forEach(it => {
              list.push({
                fileUrl: it,
                fileKey: '',
                status: 'uploaded'
              })
            })
          }
        } else if (Array.isArray(files)) {
          for (let file of files) {
            if (typeof file == 'string') {
              list.push({
                fileUrl: file,
                fileKey: null,
                status: 'uploaded',
              })
            } else {
              list.push({
                fileName: file[this.fileNameName],
                fileUrl: file[this.fileUrlName],
                fileKey: file[this.fileKeyName],
                status: 'uploaded',
              })
            }
          }
        } else {
          const fileUrl = files[this.fileUrlName]
          if (fileUrl) {
            if (this.limitCount == 1) {
              list.push({
                fileName: files[this.fileNameName],
                fileUrl: fileUrl,
                fileKey: files[this.fileKeyName],
                status: 'uploaded'
              })
            } else {
              const fileNameList = files[this.fileNameName]?.split(this.valueDelimiter) || []
              const fileKeyList = files[this.fileKeyName]?.split(this.valueDelimiter) || []
              fileUrl.split(this.valueDelimiter).forEach((url, index) => {
                list.push({
                  fileName: fileNameList[index],
                  fileUrl: url,
                  fileKey: fileKeyList[index],
                  status: 'uploaded',
                })
              })
            }
          }
        }
        if (list.map(it => it.fileUrl).join(this.valueDelimiter) != this.emitedFileUrls) {
          this.list = list
        }
      },
      videoTimeUpdate(e, id) {
        if (e.detail.currentTime > 0) {
          let ctx = uni.createVideoContext(id, this)
          if (ctx) {
            ctx.stop()
          }
        }
      },
      newMedia() {
        return {
          fileUrl: '',
          fileName: '',
          fileKey: '',
          status: '', // uploading uploaded failed
        }
      },
      previewMedia(media) {
        if (this.modifiable && this.limitCount == 1 && this.singleReplace) {
          this.open()
          return
        }
        if (media.status != 'uploaded') {
          return
        }
        if (this.type == 'image') {
          uni.previewImage({
            urls: [media.fileUrl]
          })
        } else {
          this.previewVideo = media
        }
      },
      emitChange() {
        const list = this.list.filter(it => it.status == 'uploaded')
        const fileUrls = list.map(it => it.fileUrl).join(this.valueDelimiter)
        if (fileUrls == this.emitedFileUrls) {
          return
        }
        this.emitedFileUrls = fileUrls
        let value = null
        const valueType = this.valueType && this.valueType != 'auto' ? this.valueType : this.getAutoValueType()
        if (valueType == 'url') {
          value = fileUrls
        } else if (valueType == 'url-array') {
          value = list.map(it => it.fileUrl)
        } else if (valueType == 'key') {
          value = list.map(it => it.fileKey).join(this.valueDelimiter)
        } else if (valueType == 'key-array') {
          value = list.map(it => it.fileKey)
        } else if (valueType == 'object-array') {
          value = list.map(it => {
            const res = {}
            if (!this.noFileName) {
              res[this.fileNameName] = it.fileName
            }
            if (!this.noFileName) {
              res[this.fileUrlName] = it.fileUrl
            }
            if (!this.noFileKey) {
              res[this.fileKeyName] = it.fileKey
            }
            return res
          })
        } else if (list.length == 0) {
          if (valueType == 'object-notnull') {
            value = {}
            if (!this.noFileName) {
              value[this.fileNameName] = ''
            }
            if (!this.noFileUrl) {
              value[this.fileUrlName] = ''
            }
            if (!this.noFileKey) {
              value[this.fileKeyName] = ''
            }
          }
        } else if (this.limitCount <= 1) {
          value = {}
          if (!this.noFileName) {
            value[this.fileNameName] = list[0].fileName
          }
          if (!this.noFileUrl) {
            value[this.fileUrlName] = list[0].fileUrl
          }
          if (!this.noFileKey) {
            value[this.fileKeyName] = list[0].fileKey
          }
        } else {
          value = {}
          if (!this.noFileName) {
            value[this.fileNameName] = list.map(it => it.fileName).join(this.valueDelimiter)
          }
          if (!this.noFileUrl) {
            value[this.fileUrlName] = fileUrls
          }
          if (!this.noFileKey) {
            value[this.fileKeyName] = list.map(it => it.fileKey).join(this.valueDelimiter)
          }
        }
        this.$emit('input', value)
        this.$emit('change', value)
        this.$nextTick(() => {
          uni.$u.formValidate(this, 'change')
        })
      },
      deleteMedia(media, notEmitChange = false) {
        const index = this.list.indexOf(media)
        if (index >= 0) {
          this.list.splice(index, 1)
          if (!notEmitChange) {
            this.emitChange()
          }
        }
      },
      getInfo() {
        const info = {
          uploading: 0,
          uploaded: 0,
          failed: 0,
          total: this.list.length
        }
        this.list.forEach(it => {
          info[it.status]++
        })
        return info
      },
      reuploadMedia(media) {
        this.uploadMedia(media)
      },
      // #ifdef APP
      permissionConfirm() {
        this.showPermission = false
        permission.gotoAppSetting()
      },
      permissionCancel() {
        this.showPermission = false
      },
      checkAndChooseMedia(sourceType) {
        let result = permission.isIOS ? permission.requestIOS(sourceType) :
          permission.requestAndroid(sourceType == 'camera' ?
            'android.permission.CAMERA' : 'android.permission.READ_EXTERNAL_STORAGE')

        result.then(res => {
          if (res == 1 || res == null) {
            // 已授权(或IOS未请求过)
            if (res == 1 && this.type == 'video' && permission.isIOS) {
              // IOS麦克风
              permission.requestIOS('record').then(res => {
                if (res != 1 && res != null) {
                  this.showPermission = true
                  this.permissionContent =
                    `"${this.appName}"想访问您的麦克风\n如果不允许，您将无法在${this.appName}中拍摄视频`
                } else {
                  this.chooseMedia(sourceType)
                }
              })
            } else {
              this.chooseMedia(sourceType)
            }
          } else {
            this.showPermission = true
            if (sourceType == 'camera') {
              this.permissionContent = this.type == 'image' ?
                `"${this.appName}"想访问您的相机\n如果不允许，您将无法在${this.appName}中拍摄照片` :
                `"${this.appName}"想访问您的相机\n如果不允许，您将无法在${this.appName}中拍摄视频`
            } else if (permission.isIOS) {
              this.permissionContent = `"${this.appName}"想访问您的相册\n如果不允许，您将无法在${this.appName}中打开相册`
            } else {
              this.permissionContent =
                `"${this.appName}"需要使用存储权限\n如果不允许，您将无法在${this.appName}中打开相册`
            }
          }
        })
      },
      // #endif
      chooseMedia(sourceType) {
        const choose = this.type == 'image' ? uni.chooseImage : uni.chooseVideo
        choose({
          count: Math.max(1, this.limitCount - this.list.length),
          sizeType: ['original', 'compressed'],
          sourceType: [sourceType],
          success: res => {
            if (res.tempFiles && res.tempFiles.length > 0) {
              for (let file of res.tempFiles) {
                if (file.size > this.limitSize) {
                  uni.showToast({
                    title: '文件大小不能超过' + Math.floor(this.limitSize / 1024 / 1024) + 'M',
                    icon: 'error',
                  })
                } else {
                  this.addMediaFile(file.path, file.name)
                }
              }
            } else if (res.tempFilePaths) {
              for (let path of res.tempFilePaths) {
                this.addMediaFile(path)
              }
            } else if (res.tempFilePath) {
              if (res.size && res.size > this.limitSize) {
                uni.showToast({
                  title: '文件大小不能超过' + Math.floor(this.limitSize / 1024 / 1024) + 'M',
                  icon: 'error',
                })
              } else {
                this.addMediaFile(res.tempFilePath, res.tempFile?.name)
              }
            }

          },
          fail: res => {
            console.log(JSON.stringify(res))
          }
        })
      },
      open() {
        if (this.source == 'camera') {
          this.openCamera()
        } else if (this.source == 'album') {
          this.openAlbum()
        } else {
          this.openPopup()
        }
      },
      openPopup() {
        this.showChoosePopup = true
      },
      openCamera() {
        this.showChoosePopup = false
        // #ifdef APP
        this.checkAndChooseMedia('camera')
        // #endif
        // #ifndef APP
        this.chooseMedia('camera')
        // #endif
      },
      openAlbum() {
        this.showChoosePopup = false
        // #ifdef APP
        this.checkAndChooseMedia('album')
        // #endif
        // #ifndef APP
        this.chooseMedia('album')
        // #endif
      },
      addMediaFile(path, name) {
        if (this.list.length >= this.limitCount) {
          if (this.limitCount == 1 && this.singleReplace) {
            this.list.splice(0)
          } else {
            uni.showToast({
              title: '最多可上传' + this.limitCount + '张照片',
              icon: 'error',
            })
            return
          }
        }
        const media = this.newMedia()
        media.fileUrl = path
        media.fileName = name
        this.list.push(media)
        this.uploadMedia(media)
      },
      uploadMedia(media) {
        if (!media.fileUrl ||
          media.status == 'uploading' ||
          media.status == 'uploaded') {
          return
        }
        media.status = 'uploading'
        commonApi.uploadFile({
            filePath: media.fileUrl,
            fileName: media.fileName,
            name: 'file',
            timeout: 30000,
          })
          .then(res => {
            if (res) {
              if (this.list.indexOf(media) >= 0) {
                media.fileUrl = res.uploadVirtualPath
                media.fileKey = res.uploadPath
                media.status = 'uploaded'
                this.emitChange()
              }
            } else {
              media.status = 'failed'
            }
          })
          .catch(err => {
            media.status = 'failed'
          })
      },


    },
  }
</script>

<style lang="scss" scoped>
  .lk-album {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    &-item-box {
      position: relative;
      height: 0;
      overflow: hidden;
    }

    &-item {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;

      &-top {
        margin-top: 0 !important;
      }

      &-left {
        margin-left: 0 !important;
      }

      &-content {
        width: 100%;
        height: 100%;
        overflow: hidden;
        box-sizing: border-box;

        &-video {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #000000;
        }

      }

      &-uploading {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #0000007F;
      }

      &-reupload {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        background-color: #0000007F;
        font-size: 26rpx;
      }

      &-del {
        position: absolute;
        right: 0;
        top: 0;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, .3);
        display: flex;
        justify-content: center;
        align-items: center;
      }

    }

    &-button {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #EEEEEE;
    }

    &-alone-button-gap {
      width: 100%;
      height: 0;
    }

    &-alone-button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10rpx 30rpx;
      border: 1px solid #DADBDE;
    }

    &-empty {
      font-size: 30rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
    }

  }

  .tools {
    position: fixed;
    top: 0;
    z-index: 9999;

    &-video-preview-popup {

      &-video {
        width: 100vw;
        height: 50vh;
      }

      &-close {
        display: flex;
        align-items: stretch;
        justify-content: center;
        width: 100%;
        height: 100rpx;
      }

    }

    &-choose-popup {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      background-color: #FFFFFF;
      border-radius: 20rpx 20rpx 0 0;

      &-item {
        text-align: center;
        padding: 30rpx;
      }

      &-divider {
        width: 100%;
        background-color: #F1F1F1;
      }

      &-divider:nth-child(2) {
        height: 2rpx;
      }

      &-divider:nth-child(4) {
        height: 10rpx;
      }

    }

  }
</style>