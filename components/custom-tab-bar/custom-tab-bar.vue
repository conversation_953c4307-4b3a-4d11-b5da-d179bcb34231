<template>
  <view class="tab-bar-container">
   
    <!-- 用户角色的tabbar -->
    <view class="custom-tab-bar" v-if="userRole === 'user'">
      <!-- 首页 -->
      <view
        :class="['tab-bar-item', { active: selected === 0 }]"
        @click="switchTab(0, '/pages/home/<USER>')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 0 ? '/static/tabbar/home1.png' : '/static/tabbar/home2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">首页</text>
      </view>

      <!-- 仪器 -->
      <view
        :class="['tab-bar-item', { active: selected === 1 }]"
        @click="switchTab(1, '/pages/instrument/instrument')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 1 ? '/static/tabbar/instrument1.png' : '/static/tabbar/instrument2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">仪器</text>
      </view>

      <!-- 中间提交需求按钮 -->
      <view class="center-button-wrapper">
        <view class="center-button" @click="toggleAddMenu">
          <text class="center-plus">+</text>
        </view>
        <text class="center-text">提交需求</text>
      </view>

      <!-- 通知 -->
      <view
        :class="['tab-bar-item', { active: selected === 3 }]"
        @click="switchTab(3, '/pages/message/message')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 3 ? '/static/tabbar/message1.png' : '/static/tabbar/message2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">通知</text>
      </view>

      <!-- 我的 -->
      <view
        :class="['tab-bar-item', { active: selected === 4 }]"
        @click="switchTab(4, '/pages/user/user')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 4 ? '/static/tabbar/user1.png' : '/static/tabbar/user2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">我的</text>
      </view>
    </view>

    <!-- 管理员角色的tabbar -->
    <view class="custom-tab-bar" v-else>
      <!-- 订单管理 -->
      <view
        :class="['tab-bar-item', { active: selected === 0 }]"
        @click="switchTab(0, '/pages/admin/orderManage')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 0 ? '/static/tabbar/order1.png' : '/static/tabbar/order2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">订单管理</text>
      </view>

      <!-- 通知 -->
      <view
        :class="['tab-bar-item', { active: selected === 1 }]"
        @click="switchTab(1, '/pages/message/message')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 1 ? '/static/tabbar/message1.png' : '/static/tabbar/message2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">通知</text>
      </view>

      <!-- 我的 -->
      <view
        :class="['tab-bar-item', { active: selected === 2 }]"
        @click="switchTab(2, '/pages/user/user')"
      >
        <view class="tab-icon-wrapper">
          <image :src="selected === 2 ? '/static/tabbar/user1.png' : '/static/tabbar/user2.png'" class="tab-bar-icon" />
        </view>
        <text class="tab-text">我的</text>
      </view>
    </view>

    <view class="safe-area-bottom"></view>
    <!-- 实名认证弹窗 -->
    <verification-popup
      :show="showVerificationPopup"
      @cancel="hideVerificationPopup"
      @confirm="goToVerification"
    ></verification-popup>
  </view>
  
</template>

<script>
import { mapState } from "vuex";
import verificationPopup from '../../pages/user/component/verification-popup.vue';
export default {
  components: {
    verificationPopup
  },
  props: {
    selected: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showVerificationPopup: false,
      userTabBarList: [
        {
          pagePath: "/pages/home/<USER>",
          text: "首页",
          iconPath: "/static/tabbar/home2.png",
          selectedIconPath: "/static/tabbar/home1.png"
        },
        {
          pagePath: "/pages/instrument/instrument",
          text: "仪器",
          iconPath: "/static/tabbar/instrument1.png",
          selectedIconPath: "/static/tabbar/instrument2.png"
        },
        {
          pagePath: "/pages/message/message",
          text: "通知",
          iconPath: "/static/tabbar/message1.png",
          selectedIconPath: "/static/tabbar/message2.png"
        },
        {
          pagePath: "/pages/user/user",
          text: "我的",
          iconPath: "/static/tabbar/user1.png",
          selectedIconPath: "/static/tabbar/user2.png"
        }
      ],
      adminTabBarList: [
        {
          pagePath: "/pages/admin/orderManage",
          text: "订单管理",
          iconPath: "/static/tabbar/order1.png",
          selectedIconPath: "/static/tabbar/order2.png"
        },
        {
          pagePath: "/pages/message/message",
          text: "通知",
          iconPath: "/static/tabbar/message1.png",
          selectedIconPath: "/static/tabbar/message2.png"
        },
        {
          pagePath: "/pages/user/user",
          text: "我的",
          iconPath: "/static/tabbar/user1.png",
          selectedIconPath: "/static/tabbar/user2.png"
        }
      ]
    };
  },
  computed: {
    ...mapState(["userInfo", "userRole"]),
    // 根据用户角色获取对应的tabbar列表
    tabBarList() {
      return this.userRole === 'user' ? this.userTabBarList : this.adminTabBarList;
    }
  },
  methods: {
    hideVerificationPopup() {
      this.showVerificationPopup = false;
    },
    goToVerification() {
      this.hideVerificationPopup();
      uni.navigateTo({
        url: '/pages/user/verification'
      });
    },
    switchTab(index, pagePath) {
      uni.switchTab({
        url: pagePath
      });
    },
    toggleAddMenu() {
      // 检查用户是否已实名认证
      // if(!this.$store.state.userInfo || this.$store.state.userInfo.identityAuthenticationId == 0){
      //   this.showVerificationPopup = true;
      //   return;
      // }

      uni.navigateTo({
        url: '/pages/request/detection'
      });
    }
  }
};
</script>

<style scoped>
.tab-bar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.custom-tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 110rpx;
  background-color: #ffffff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #ffffff;
}

.tab-bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 8rpx 0;
  transition: all 0.2s ease;
}

.tab-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
}

.tab-bar-icon {
  width: 48rpx;
  height: 48rpx;
  transition: transform 0.2s ease;
}

.tab-text {
  font-size: 20rpx;
  color: #999999;
  line-height: 28rpx;
  transition: color 0.2s ease;
}

.active .tab-text {
  color: #40E0D0;
  font-weight: 500;
}

.active .tab-bar-icon {
  transform: scale(1.1);
}

.center-button-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  top: -20rpx;
  flex: 1;
}

.center-button {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #40E0D0 0%, #20B2AA 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(64, 224, 208, 0.3);
  border: 6rpx solid #ffffff;
  transition: all 0.2s ease;
}

.center-button:active {
  transform: scale(0.95);
}

.center-plus {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 300;
  line-height: 1;
}

.center-text {
  font-size: 20rpx;
  margin-top: 8rpx;
  color: #999999;
  line-height: 28rpx;
}
</style>
