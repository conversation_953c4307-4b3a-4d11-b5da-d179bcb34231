<template>
  <view class="lk-loading" :class="[loadingClass]" :style="[loadingStyle, customStyle]">
    <u-loading-icon size="28" mode="circle"></u-loading-icon>
    <view v-if="text" class="text">正在加载</view>
  </view>
</template>

<script>
  export default {
    name: "lk-loading",
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      center: {
        type: Boolean,
        default: true
      },
      text: {
        type: String,
        default: '正在加载'
      },
      bgColor: {
        type: String,
        default: '#FFFFFF'
      },
      customStyle: {
        type: Object,
        default: null
      }
    },
    data() {
      return {}
    },
    computed: {
      loadingClass() {
        if (this.center) {
          return 'center'
        }
        return ''
      },
      loadingStyle() {
        return this.bgColor ? { 'background' : this.bgColor } : {}
      },
    },
    methods: {
      click() {
        this.$emit('click')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .lk-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30rpx;

    &.center {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }

    .text {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      color: #AAA;
      margin-top: 20rpx;
    }

  }
</style>
