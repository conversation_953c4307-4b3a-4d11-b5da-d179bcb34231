<template>
	<view class="lk-calendar">
		<!-- <view class="header">
			<view class="header-title">
				<view class="header-title-time">2021年12月</view>
				<view class="header-title-week">周日</view>
			</view>
		</view> -->
		<view class="week-title"><WeekTitle /></view>
		<view class="months"><Months @isShowWeek="getIsShowWeek" :list="list"  :sel-time="selTime" @getDate="getDate" :msg-time="msgTime"/></view>
	</view>
</template>

<script>
import WeekTitle from './WeekTitle.vue';
import Months from './Months.vue';
export default {
	name: 'calendar',
	props:{
		list:{
			type:Array,
			default:()=>[]
		},
		selTime:{
			type:[String,Number],
			default:''
		},
		msgTime:{
			type:[String,Number],
			default:''
		},
		
	},
	components: {
		WeekTitle,
		Months
	},
	data() {
		return {};
	},
	methods:{
		getDate(date){
			this.$emit('getDate',date)
		},
		getIsShowWeek(bool){
			this.$emit('isShowWeek',bool)
		}
	}
};
</script>

<style lang="scss" scoped>
$padding: 20rpx;
.header {
	display: flex;
	align-items: center;
	padding: $padding;
	letter-spacing: 2rpx;
	&-title {
		display: flex;
		align-items: flex-end;
		&-time {
			font-size: 46rpx;
			font-weight: bold;
		}
		&-week {
			color: #999;
		}
	}
}
</style>
