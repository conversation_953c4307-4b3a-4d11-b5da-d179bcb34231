<template>
	<view class="header-weekdays">
		<text v-for="(weekday, index) in weekdays" class="header-weekdays-weekday" :key="index">{{ weekday }}</text>
	</view>
</template>

<script>
export default {
	name: 'calendar-weektitle',
	data() {
		return {
			weekdays: ['日', '一', '二', '三', '四', '五', '六']
		};
	}
};
</script>

<style lang="scss" scoped>
.header-weekdays {
	display: flex;
	&-weekday {
		text-align: center;
		height: 42px;
		line-height: 42px;
		flex: 1;
		font-size: 22rpx;
		color: #fff;
	}
}
</style>
