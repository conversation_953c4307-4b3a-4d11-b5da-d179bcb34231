<template>
	<view class="month_box">
		<view class="months" id="months" :style="{height:height+'px'}" :animation="animationData" ref="animation">
			<!-- 展示选中的周 -->
			<view class="day" v-if="show" v-for="(day,index) in currentWeek" :key="index">
				<view class="day-content" :class="{
				  'day-content_disable': !day.currentMonth,
				  'day-content_checked': day.date === currentDay
				}" @click="switchMonth(day,day.currentMonth)" v-if="show">
					{{ day.day }}
				</view>
				<view :class="['day-dto',`day-dto_${day.status}`]"></view>
			</view>
			<!-- 展示当前月 -->
			<view class="day" v-for="day in dates" :key="day.date">
				<view class="day-content" :class="{
  	      'day-content_disable': !day.currentMonth,
  	      'day-content_checked': day.date === currentDay
  	    }" @click="switchMonth(day,day.currentMonth)">
					{{ day.day }}
				</view>
				<view :class="['day-dto',`day-dto_${day.status}`]"></view>
			</view>
		</view>
		<view class="up_down"><text @touchstart.stop="touchStart" @touchend.stop="touchEnd"
				class="up_down__xiala_box"><text class="up_down__xiala" /></text>
		</view>
	</view>
</template>

<script>
	import dayjs from '@/common/utils/day.js';
	let animation
	//#ifdef APP-PLUS
	animation = uni.requireNativePlugin('animation')
	//#endif
	//#ifdef MP-WEIXIN
	animation = wx.createAnimation()
	//#endif

	export default {
		name: 'calendar-months',
		props: {
			list: {
				type: Array,
				default: () => []
			},
			selTime: {
				type: [String, Number],
				default: ''
			},
			msgTime: {
				type: [String, Number],
				default: ''
			},
		},
		data() {
			return {
				dayjs,
				dates: [],
				currentTime: dayjs(new Date()),
				currentDay: dayjs(new Date()).format('YYYY-MM-DD'),
				touchStartX: 0, // 触屏起始点x  
				touchStartY: 0, // 触屏起始点y,
				animationData: {},
				height: null,
				currentWeek: [],
				show: false
			};
		},
		created() {

			this.animation = uni.createAnimation()
			this.initDays();
		},
		watch: {
			list: {
				immediate: true,
				handler(val) {
					this.$nextTick(() => {
						val && this.echoData(val)
					})
				}
			},
			selTime: {
				immediate: true,
				handler(val) {
					this.currentTime = val ? dayjs(val) : this.currentTime
					this.initDays();
				}
			},
			msgTime: {
				immediate: true,
				handler(val) {
					console.log(this.show + '-----show');
					this.currentDay = val ? val : dayjs(new Date()).format('YYYY-MM-DD')
					this.currentTime = val ? dayjs(val) : this.currentTime
					this.initDays();
					this.list && this.echoData(this.list)
				}
			}
		},
		created() {
			this.$nextTick(() => {
				const query = uni.createSelectorQuery().in(this);
				query.select('#months').boundingClientRect(data => {
					this.height = data.height
          this.animationObj(this.height / 6)
					this.show = true
					this.$emit('isShowWeek', this.show)
				}).exec();
			})
		},
		methods: {
			getWeek(target) { //获取选中日期所在周的每一天
				let now = new Date(target);
				let now_day = now.getDay();
				let now_time = now.getTime();
				let result = [0, 1, 2, 3, 4, 5, 6]
				return result.map(i => (new Date(now_time + 24 * 60 * 60 * 1000 * (i - now_day))).getDate())
			},
			getCurrentWeekDays() {
				let month = dayjs(this.currentDay).format('MM')
				let days = dayjs(this.currentDay).format('DD')
				let index = this.dates.findIndex(it => it.month == month && it.day == days)
				let startIndex = index >= 0 ? index - this.dates[index].week : 0
				this.currentWeek = this.dates.slice(startIndex, startIndex + 7)
			},
			/**  
			 * 触摸开始  
			 **/
			touchStart(e) {
				this.getCurrentWeekDays()
				console.log("触摸开始")
				this.touchStartX = e.touches[0].clientX;
				this.touchStartY = e.touches[0].clientY;
			},
			/**  
			 * 触摸结束  
			 **/
			touchEnd(e) {
				console.log("触摸结束")
				let deltaX = e.changedTouches[0].clientX - this.touchStartX;
				let deltaY = e.changedTouches[0].clientY - this.touchStartY;
				if (Math.abs(deltaY) > 50 && Math.abs(deltaX) < Math.abs(deltaY)) {
					if (deltaY < 0) {
						console.log("上滑")
						this.animationObj(this.height / 6)
						this.show = true
					} else {
						console.log("下滑")
						this.animationObj(this.height)
						this.show = false
					}
          
					this.$emit('isShowWeek', this.show)
				} else {
					console.log("可能是误触！")
				}
			},
			animationObj(height) {
				const animation = uni.createAnimation({
					timingFunction: 'ease-in-out',
					duration: 200
				});
				animation.height(height).step({
					duration: 200
				})
				// 导出动画数据给面板的animationData值
				this.animationData = animation.export()
			},
			echoData(val) {
				let _this = this
				// val.forEach(item => {
				this.dates = this.dates.map(it => {
					let item = val.find(e => e == it.date)
					if (item) {
						it.status = item == it.date ? 'success' : 'default'
					} else {
						it.status = 'default'
					}
					return it
				})
				// })
			},
			switchMonth(day, bool) {
				if (bool) {
					this.currentDay = day.date;
					this.$emit('getDate', this.currentDay)
					if (!day.currentMonth) {
						this.currentTime = dayjs(day.date);
						this.initDays();
					}
				}
			},
			initDays() {
				let currWeeks = this.getCurrWeekList();
				let proWeek = this.getProWeekList(currWeeks[0].date);
				let nextWeek = this.getNextWeekList(currWeeks[currWeeks.length - 1].date);
				// 前面需要补充多少填
				let beforeDay = -currWeeks[0].week;
				let beforeWeek = beforeDay < 0 ? proWeek.splice(beforeDay) : [];
				// 后面需要补充多少天，显示6个星期的日期
				let afterDay = (beforeWeek.length + currWeeks.length <= 35 ? 13 : 6) - currWeeks[currWeeks.length - 1]
				.week;
				let afterWeek = afterDay > 0 ? nextWeek.splice(0, afterDay) : [];
				this.dates = [...beforeWeek, ...currWeeks, ...afterWeek];
				this.dates.forEach(item => {
					let arr = item.date.split('-')
					item.month = arr[1]
				})
				this.getCurrentWeekDays()
			},
			/**
			 * 获取指定日期上月数据
			 * @param {Object} date
			 * @param { type } pro :上个月末数据，next ：下个月初数据
			 */
			getProWeekList(date) {
				let currDate = dayjs(date)
					.subtract(1, 'month')
					.format('YYYY-MM-DD');
				return this.getMonthDay(currDate, false);
			},
			/**
			 * 获取当月的数据
			 * @param {Object} data
			 */
			getCurrWeekList() {
				let data = this.currentTime.format('YYYY-MM-DD');
				return this.getMonthDay(data);
			},
			/**
			 * 获取指定日期下个月的数据
			 * @param {Object} date
			 */
			getNextWeekList(date) {
				let nextTime = dayjs(date)
					.add(1, 'month')
					.format('YYYY-MM-DD');
				return this.getMonthDay(nextTime, false);
			},
			/**
			 * 获取指定日期的月份天数数据
			 * @param {Object} date
			 */
			getMonthDay(date, currentMonth = true) {
				let time = dayjs(date);
				return new Array(time.daysInMonth()).fill(1).map((item, index) => {
					let day = index + 1;
					// 星期，0-6，0为周日
					const week = time.date(day).day();
					const date = time.date(day).format('YYYY-MM-DD');
					let config = {
						day,
						date,
						week,
						currentMonth: currentMonth
					};
					return config;
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.month_box {
		position: relative;

		.up_down {
			position: absolute;
			width: 100%;
			font-size: 48rpx;
			height: 60rpx;
			bottom: -66rpx;
			background: #f7f7f7;
			text-align: center;
			line-height: 28rpx;
			// border-radius: 40rpx 40rpx 0 0;

			&__xiala_box {
				display: inline-block;
				width: 200rpx;
				height: 60rpx;
			}

			&__xiala {
				display: inline-block;
				width: 60rpx;
				height: 14rpx;
				background: #DCDCDC;
				border-radius: 6rpx;
			}
		}
	}

	.months {
		display: flex;
		flex-wrap: wrap;
		padding-bottom: 10rpx;
		overflow: hidden;

		.day {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 10rpx 0 20rpx 0;
			position: relative;
			width: calc(100% / 7);

			&-content {
				flex: 1;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				max-width: 60rpx;
				max-height: 60rpx;
				min-height: 60rpx;
				border-radius: 16rpx;
				font-size: 34rpx;
				color: #fff;

				&_disable {
					color: #9dbfff;
				}

				&_checked {
					color: #3d7fff;
					background-color: #fff;
				}
			}

			&-dto {
				$size: 15rpx;
				width: $size;
				height: $size;
				border-radius: 50%;
				position: absolute;
				display: block;
				margin-top: 10rpx;
				bottom: 0;

				&_success {
					background-color: #fff;
				}

				&_error {
					background-color: #f09736;
				}

				&_parmise {
					background-color: #3d7fff;
				}
			}
		}
	}
</style>
