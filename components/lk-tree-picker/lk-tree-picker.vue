<template>
  <u-popup :show="show" mode="bottom" @close="cancel">
    <u-toolbar
      confirmColor="#3d7fff"
      @cancel="cancel"
      @confirm="confirm"
    ></u-toolbar>
    <view class="lk-list-picker-search">
      <u-search
        placeholder="请输入关键字"
        v-model="keyword"
        bgColor="#f7f7f7"
        :showAction="false"
        shape="square"
      />
    </view>
    <scroll-view v-if="show" scroll-y="true" class="tree-picker">
      <!-- @node-expand="handleNodeExpand"
        @node-click="handleNodeClick" -->
      <ly-tree
        ref="tree"
        :treeData="treeData"
        :nodeKey="treeProps.id||'id'"
        :show-radio="!multiple"
        :showCheckbox="multiple"
        :checkOnClickNode="true"
        :expandOnClickNode="false"
        :childVisibleForFilterNode="true"
        :defaultExpandAll="true"
        @check="handleCheck"
        :checkStrictly="true"
        :props="treeProps"
        :checkOnlyLeaf="false"
        :default-checked-keys="defaultCheckedKeys"
        @radio-change="handleRadioChange"
        @node-click="handleNodeClick"
        :filterNodeMethod="filterNodeMethod"
      ></ly-tree>
    </scroll-view>
  </u-popup>
</template>

<script>
// import dayjs from "@/common/utils/day.js";
// import mpMixin from "@/common/mixin/mp.js";

export default {
  name: "lk-tree-picker",
  // mixins: [mpMixin],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
    // 显示农历
    lunar: {
      type: Boolean,
      default: false,
    },
    // 日期选择范围-开始日期
    minDate: {
      type: String,
      default: "",
    },
    treeProps: {
      type: Object,
      default: () => {
        return { label: "name" ,children:'children'};
      },
    },
    // 日期选择范围-结束日期
    maxDate: {
      type: String,
      default: "",
    },
    // 范围选择
    range: {
      type: Boolean,
      default: false,
    },
    defaultRange: {
      type: Array,
      default: null,
    },
    selected: {
      type: Array,
      default: () => [],
    },
    // 是否显示月份为背景
    showMonth: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },

  },
  data() {
    return {
      currentData: [],
      keyword: "",
    };
  },
  watch:{
    keyword(){
      this.$refs.tree.filter(this.keyword);
    },

    defaultCheckedKeys(){
      this.$nextTick(()=>{
        this.currentData = this.$refs.tree&&this.$refs.tree.getCheckedNodes();
      })
    },
    treeData(){
      this.keyword = ''
      this.$nextTick(()=>{
        this.currentData = this.$refs.tree&&this.$refs.tree.getCheckedNodes();
      })
    },
    show(){
      if(this.show){
        this.keyword = ''
      }
    }
  },
  computed: {
    innerDate() {
      if (this.range && !this.date && this.defaultRange?.length) {
        return this.defaultRange[0];
      }
      return this.date;
    },
  },
  methods: {
    filterNodeMethod(...arg) {
      let node = arg[1]
      let keyword = arg[0]
      let label = arg[2]&& arg[2].label
      if (keyword) {
        return label && label.includes(keyword);
      } else {
        return true;
      }
    },
    change(e) {
      if (e.range?.data?.length) {
        this.dateList = e.range.data;
      } else {
        this.dateList = [e.fulldate];
      }
    },
    cancel() {
      this.$emit("cancel");
      this.dateList = [];
    },
    handleCheck(obj) {
      this.currentData = obj.checkedNodes;
    },
    confirm() {
      this.$emit("confirm", this.currentData);
    },
    // 只要节点的选中状态改变就触发（包括设置默认选中，点击选中/取消选中），会被触发多次
    handleRadioChange(obj) {
      console.log("handleRadioChange", obj);
    },

    handleNodeClick(obj) {
      console.log("handleNodeClick", JSON.stringify(obj));
      console.log("getNodePath", this.$refs.tree.getNodePath(obj.data));
    },
  },
};
</script>

<style lang="scss" scoped>
.lk-list-picker-search{
   padding: 20rpx 30rpx;
}
.tree-picker {
  height: 50vh;
  &-search {
   
  }

  // overflow: auto;
  // padding: 0 30rpx 30rpx 30rpx;
}
</style>

