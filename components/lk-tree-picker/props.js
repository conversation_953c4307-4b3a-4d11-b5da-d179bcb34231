import textBoxProps from '@/components/lk-text-box/props.js'

export default {
  mixins: [textBoxProps],
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 选择列表
    list: {
      type: Array,
      default: null,
    },
    // 列表中值字段名称
    valueName: {
      type: String,
      default: 'value'
    },
    // 列表中在输入框显示字段名称
    labelName: {
      type: String,
      default: 'label'
    },
    // 列表中在选择列表中显示字段名称
    pickerLabelName: {
      type: String,
      default: ''
    },
    suffixIcon: {
      type: String,
      default: 'arrow-down'
    },
    clearable: {
      type: Boolean,
      default: false
    },
  }
}
