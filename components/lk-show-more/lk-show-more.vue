<template>
  <view class="lk-show-more" :class="[ showMoreClass ]">

    <view class="lk-show-more-content" :style="[ contentStyle ]">
      <view id="lk-show-more-content-inner" class="lk-show-more-content-inner" :style="[ contentInnerStyle ]">
        <slot></slot>
      </view>
    </view>

    <template v-if="status == 'collapsed'">
      <view class="lk-show-more-toggle" @tap.stop="expand">
        <slot name="expand">
          <span>{{ expandText }}</span>
          <u-icon name="arrow-down" color="#3370FF" size="30rpx" />
        </slot>
      </view>
    </template>

    <template v-else-if="collapsable && status == 'expanded'">
      <view class="lk-show-more-toggle" @tap.stop="collapse">
        <slot name="collapse">
          <span>{{ collapseText }}</span>
          <u-icon name="arrow-up" color="#3370FF" size="30rpx" />
        </slot>
      </view>
    </template>
  </view>
</template>

<script>
  export default {
    name: 'lk-show-more',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      // 折叠显示行数
      showLine: {
        type: [Number, String],
        default: 0
      },
      // 拆卸叠高度，不设置时根据showLine计算
      showHeight: {
        type: [String, Number],
        default: 0
      },
      expandText: {
        type: String,
        default: '查看全部'
      },
      collapseText: {
        type: String,
        default: '收起'
      },
      collapsable: {
        type: Boolean,
        default: true
      },
      // 显示省略
      ellipsis: {
        type: Boolean,
        default: true
      }
    },
    computed: {
      showHeightPx() {
        return typeof this.showHeight == 'string' && this.showHeight.endsWith('rpx') ?
          uni.upx2px(parseInt(this.showHeight)) : parseInt(this.showHeight)
      },
      showMoreClass() {
        return 'status-' + this.status
      },
      contentStyle() {
        const style = {}
        if (this.status == 'none') {
          if (this.showHeightPx > 0) {
            style.height = this.showHeightPx + 'px'
          } else if (this.collapsedHeight > 0) {
            style.height = this.collapsedHeight + 'px'
          }
        } else if (this.status == 'collapsed') {
          style.height = this.collapsedHeight + 'px'
        }
        return style
      },
      computingCollapsedHeight() {
        return this.status == 'none' && this.showHeightPx == 0 && this.collapsedHeight < 0
      },
      contentInnerStyle() {
        const style = {}
        if (parseInt(this.showLine) > 0) {
          if ((this.status == 'collapsed' && this.ellipsis) || this.computingCollapsedHeight) {
            style['-webkit-line-clamp'] = this.showLine
          }
        }
        return style
      }
    },
    data() {
      return {
        status: 'none', // none normal expanded collapsed,
        collapsedHeight: -1
      }
    },
    mounted() {
      this.compute()
    },
    methods: {
      compute() {
        this.status = 'none'
        this.collapsedHeight = -1
        this.queryBounding(false)
      },
      queryBounding() {
        uni.createSelectorQuery()
          .in(this)
          .select('#lk-show-more-content-inner')
          .boundingClientRect(data => {
            if (this.computingCollapsedHeight) {
              // 先计算折叠高度
              this.collapsedHeight = data.height
              // 计算展开后高度是否超限
              this.$nextTick(this.queryBounding)
            } else {
              if (this.showHeightPx > 0) {
                // 指定高度
                this.collapsedHeight = this.showHeightPx
              }
              this.status = data.height > this.collapsedHeight ? 'collapsed' : 'normal'
            }
          }).exec()
      },
      expand() {
        if (this.status == 'collapsed') {
          this.status = 'expanded'
          this.$emit('expand')
        }
      },
      collapse() {
        if (this.status == 'expanded') {
          this.status = 'collapsed'
          this.$emit('collapse')
        }
      }
    }
  }
</script>

<style lang="scss">
  .lk-show-more {
    display: flex;
    flex-direction: column;

    &-content {
      overflow: hidden;

      &-inner {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

    }

    &-toggle {
      display: flex;
      flex-direction: row;
      align-items: center;
      align-self: center;
      color: #3370FF;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 42rpx;
    }
  }
</style>
