<template>
  <view class="lk-empty" :class="[emptyClass]" :style="[customStyle]" @tap.stop="click">
    <image class="image" src="@/static/none.png" mode="heightFix"></image>
    <text class="text">{{ text }}</text>
  </view>
</template>

<script>
  export default {
    name: "lk-empty",
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      center: {
        type: Boolean,
        default: true
      },
      text: {
        type: String,
        default: '暂无数据'
      },
      customStyle: {
        type: Object,
        default: null
      }
    },
    data() {
      return {}
    },
    computed: {
      emptyClass() {
        if (this.center) {
          return 'center'
        }
        return ''
      },
    },
    methods: {
      click() {
        this.$emit('click')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .lk-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30rpx;

    &.center {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }

    .image {
      max-height: 320rpx;
    }

    .text {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      color: #AAA;
    }

  }
</style>
