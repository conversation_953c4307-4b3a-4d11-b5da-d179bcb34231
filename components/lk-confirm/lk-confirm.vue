<template>
  <view>
    <u-popup :show="innerShow" :mode="mode" :round="round" :duration="duration"
      :closeOnClickOverlay="innerCloseOnClickOverlay" :safeAreaInsetBottom="safeAreaInsetBottom"
      :customStyle="innerCustomStyle" @close="cancel">

      <view class="lk-confirm" :style="[innerBoxStyle]">
        <!-- 标题 -->
        <slot name="title">
          <view v-if="innerTitle" class="lk-confirm-title">
            {{ innerTitle }}
          </view>
        </slot>

        <!-- 显示内容 -->
        <view class="lk-confirm-content">
          <slot>
            <!-- 顶层 -->
            <view v-for="(item, index) in innerContent" :key="index" :style="[item.style]">
              <template v-if="item.text">
                {{ item.text }}
              </template>
              <template v-else-if="item.children">
                <!-- 第二层 -->
                <view v-for="(child, cindex) in item.children" :key="cindex" :style="[child.style]">
                  <template v-if="child.text">
                    {{ child.text}}
                  </template>
                  <template v-else-if="child.children">
                    <!-- 第三层 -->
                    <view v-for="(leaf, lindex) in child.children" :key="lindex" :style="[leaf.style]">
                      {{ leaf.text || ''}}
                    </view>
                  </template>
                </view>
              </template>
            </view>
          </slot>
        </view>

        <!-- 底部按钮 -->
        <view class="lk-confirm-footer" :class="['theme-button-' + themeButtonType]" :style="{ justifyContent: centerButton ? 'center' : undefined}">
          <template v-if="themeButtonType">
            <!-- 主题按钮 -->
            <!-- 取消 -->
            <u-button v-if="innerShowCancelButton" :customStyle="cancelButtonStyle"
              @click='cancel'>{{innerCancelButtonText}}</u-button>
            <!-- 确定 -->
            <u-button v-if="innerShowConfirmButton" :customStyle="confirmButtonStyle"
              @click="confirm">{{innerConfirmButtonText}}</u-button>
          </template>
          <template v-else>
            <!-- 扁平按钮 -->
            <!-- 取消 -->
            <u-button v-if="innerShowCancelButton" type="text" :customStyle="cancelButtonStyle"
              @click='cancel'>{{innerCancelButtonText}}</u-button>
            <!-- 分隔线 -->
            <view v-if="innerShowCancelButton && innerShowConfirmButton" class="lk-confirm-footer-gap"></view>
            <!-- 确定 -->
            <u-button v-if="innerShowConfirmButton" type="text" :customStyle="confirmButtonStyle"
              @click="confirm">{{innerConfirmButtonText}}</u-button>
          </template>
        </view>

      </view>

    </u-popup>
  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  const defaultWidth = '550rpx'

  export default {
    name: 'lk-confirm',
    mixins: [mpMixin, props],
    data() {
      return {
        duration: 300,
        opened: false,
        openId: 0,
        options: null
      }
    },
    computed: {
      safeAreaInsetBottom() {
        return this.mode != 'top' && this.mode != 'center'
      },
      innerCustomStyle() {
        const style = {}
        style.overflow = 'hidden'
        if (this.width) {
          style.width = this.width
          style.margin = 'auto'
        } else if (this.mode != 'top' && this.mode != 'bottom') {
          style.width = defaultWidth
        }
        if (this.height) {
          style.height = this.height
        }
        if (this.mode == 'top' || this.mode == 'center' || this.mode == 'bottom') {
          style.maxHeight = '100vh'
        }
        return style
      },
      innerBoxStyle() {
        const style = {}
        if (this.width) {
          style.width = this.width
        }
        return style
      },
      cancelButtonStyle() {
        return this.themeButtonType ? {
          color: 'rgba(0, 0, 0, 0.88)',
          background: '#ffffff',
          boxShadow: '0 2px 0 rgba(0, 0, 0, 0.02)',
          border: '1px solid #d9d9d9',
        } : {
          color: this.cancelButtonColor
        }
      },
      confirmButtonStyle() {
        return this.themeButtonType ? {
          color: '#ffffff',
          background: '#26D1CB',
        } : {
          color: this.confirmButtonColor
        }
      },
      themeButtonType() {
        if (this.themeButton == 'flex') {
          return 'flex'
        }
        return this.themeButton ? 'auto' : ''
      },
      innerShow() {
        return this.options ? this.opened : this.show
      },
      innerCloseOnClickOverlay() {
        return this.options?.closeOnClickOverlay ?? this.closeOnClickOverlay
      },
      innerTitle() {
        return this.options?.title ?? this.title
      },
      innerShowCancelButton() {
        return this.options?.showCancelButton ?? this.showCancelButton
      },
      innerCancelButtonText() {
        return this.options?.cancelButtonText ?? this.cancelButtonText
      },
      innerShowConfirmButton() {
        return this.options?.showConfirmButton ?? this.showConfirmButton
      },
      innerConfirmButtonText() {
        return this.options?.confirmButtonText ?? this.confirmButtonText
      },
      innerContent() {
        const content = this.options?.content ?? this.content
        return content ? this.fit(Array.isArray(content) ? content : [content]) : []
      },
    },
    methods: {
      fit(list) {
        if (!list) {
          return []
        }
        const res = []
        list.forEach((it, idx) => {
          let e = null
          if (Array.isArray(it)) {
            e = {
              children: it
            }
          } else if (typeof it == 'string') {
            e = {
              text: it
            }
          } else {
            e = it
          }
          if (e.children) {
            e.children = this.fit(e.children)
          }
          res.push(e)
        })
        return res
      },
      cancel() {
        if (this.options) {
          if (this.opened) {
            this._close()
            this.options.reject()
          }
        } else if (this.show) {
          this.$emit('cancel')
          this.$emit('close')
        }
      },
      confirm() {
        const options = this.options
        if (this.options) {
          if (this.opened) {
            this._close()
            this.options.resolve()
          }
        } else if (this.show) {
          this.$emit('confirm')
          if (!this.confirmNoClose) {
            this.$emit('close')
          }
        }
      },
      /**
       * {
       *  title,
       *  content,
       *  showCancelButton,
       *  cancelButtonText,
       *  showConfirmButton,
       *  confirmButtonText, 
       * }
       */
      open(options) {
        return new Promise((resolve, reject) => {
          this.opened = true
          this.openId++
          this.options = {
            ...options,
            resolve,
            reject,
          }
        })
      },
      _close() {
        this.opened = false
        const openId = this.openId
        setTimeout(() => {
          if (this.openId == openId) {
            this.options = null
          }
        }, this.duration)
      },
    }
  }
</script>

<style lang="scss" scoped>
  .lk-confirm {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;

    $padding: 48rpx;

    padding-top: $padding;

    &-title {
      text-align: center;
      font-size: 32rpx;
      line-height: 45rpx;
      padding: 0 $padding 32rpx $padding;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
    }

    &-content {
      flex: 1;
      padding: 0 $padding;
      font-size: 32rpx;
      line-height: 48rpx;
      color: #333333;
      overflow: auto;
    }

    &-footer {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 48rpx;
      border-top: #EEEEEE solid 1px;

      &-gap {
        height: 100rpx;
        width: 1px;
        background-color: #EEEEEE;
      }

      ::v-deep .u-button {
        flex: 1;
        height: 100rpx;
      }

      &.theme-button-flex {
        padding: 0 20rpx 30rpx 20rpx;
        border: none;

        ::v-deep .u-button {
          height: 80rpx;
          font-size: 32rpx;
          line-height: 48rpx;
          border: none;
          border-radius: 12rpx;
          margin: 0 20rpx;
        }
      }

      &.theme-button-auto {
        padding: 0 $padding $padding $padding;
        border: none;
        justify-content: space-between;

        ::v-deep .u-button {
          flex: none;
          width: calc(50% - 12rpx);
          max-width: 280rpx;
          height: 80rpx;
          font-size: 32rpx;
          line-height: 48rpx;
          border: none;
          border-radius: 12rpx;
          margin: 0;
        }
      }

    }

  }
</style>