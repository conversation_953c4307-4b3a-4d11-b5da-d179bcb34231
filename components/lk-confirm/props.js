export default {
  props: {
    // 显示弹框
    show: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 显示内容
    // String 'text'
    // Object {text: 'text', style: {}, children: []}
    // Array ['text', {text: 'text', style: {}, children: []}]
    content: {
      type: [String, Object, Array],
      default: null,
    },
    // 弹框位置，left top right bottom center
    mode: {
      type: String,
      default: 'center'
    },
    // 圆角半径
    round: {
      type: String,
      default: '24rpx'
    },
    // 宽度
    width: {
      type: String,
      default: ''
    },
    // 高度
    height: {
      type: String,
      default: ''
    },
    closeOnClickOverlay: {
      type: Boolean,
      default: false
    },
    // 显示取消按钮
    showCancelButton: {
      type: Boolean,
      default: true
    },
    // 取消按钮文本
    cancelButtonText: {
      type: String,
      default: '取消'
    },
    // 取消按钮颜色
    cancelButtonColor: {
      type: String,
      default: '#777777'
    },
    // 显示确认按钮
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    // 确认按钮文本
    confirmButtonText: {
      type: String,
      default: '确定'
    },
    // 确认按钮颜色
    confirmButtonColor: {
      type: String,
      default: '#1CBE83'
    },
    // 确认时关闭
    confirmNoClose: {
      type: Boolean,
      default: false
    },
    // 主题按钮
    // flex 主题按钮
    // auto 主题按钮
    themeButton: {
      type: [String, Boolean],
      default: true
    },
    centerButton: {
      type: Boolean,
      default: false
    }
  },
}
