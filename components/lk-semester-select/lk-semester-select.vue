<template>
  <lk-list-select v-model="semesterId" :list="semesterList" labelName="markName" valueName="id" :color="color"
    :backgroundColor="backgroundColor" :placeholder="placeholder" :placeholderColor="placeholderColor"
    :fontSize="fontSize" :lineHeight="lineHeight" :multipleLine="multipleLine" :noBorder="noBorder"
    :borderColor="borderColor" :borderRadius="borderRadius" :activeBorderColor="activeBorderColor"
    :prefixIcon="prefixIcon" :activePrefixIcon="activePrefixIcon" :suffixIcon="suffixIcon"
    :activeSuffixIcon="clearable ? 'close-circle' : activeSuffixIcon" :iconColor="iconColor"
    :activeIconColor="activeIconColor" :iconSize="iconSize" :display="display" :stretch="stretch" :align="align"
    :customStyle="customStyle" :textStyle="textStyle" :placeholderStyle="placeholderStyle" :contentStyle="contentStyle"
    :iconStyle="iconStyle" :prefixIconStyle="prefixIconStyle" :suffixIconStyle="suffixIconStyle" :clearable="clearable"
    @clear="$emit('clear')">
  </lk-list-select>
</template>

<script>
  import commonApi from '@/api/common.js'

  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  export default {
    name: 'lk-semester-select',
    mixins: [mpMixin, props],
    data() {
      return {
        semesterList: [],
        semesterId: null,
        showPicker: false,
      }
    },
    computed: {
      semester() {
        return this.semesterList.find(it => it.id == this.semesterId)
      }
    },
    watch: {
      semesterId() {
        this.$nextTick(() => {
          if (this.semester) {
            this.$emit('input', this.semester)
            this.$emit('change', this.semester)
          }
        })
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.getCurrentSemester()
        this.getSemesterList()
      },
      getCurrentSemester() {
        commonApi.getCurrentSemester({
            studentId: this.$store.state?.userInfo?.studentId
          })
          .then(res => {
            if (!res) {
              return
            }
            const semester = {
              id: res.id,
              year: res.year,
              type: res.type,
              semesterStartTime: res.semesterStartTime,
              semesterEndTime: res.semesterEndTime,
              name: `${res.year}学年${res.type == 2 ? '秋' : '春'}季学期`,
              markName: `${res.year}学年${res.type == 2 ? '秋' : '春'}季学期${this.currentSuffix}`,
              isCurrent: true,
            }
            // 列表中本学期设置
            const index = this.semesterList.findIndex(it => it.id == semester.id)
            if (index >= 0) {
              this.$set(this.semesterList, index, semester)
            } else if (!this.semesterList.length) {
              this.semesterList = [semester]
            }
            this.current = semester
            this.semesterId = semester.id
          })
      },
      getSemesterList() {
        commonApi.getSemesterList({
            studentId: this.$store.state?.userInfo?.studentId
          })
          .then(res => {
            this.semesterList = res?.map(it => {
              const isCurrent = it.id == this.current?.id
              const name = `${it.year}学年${it.type == 2 ? '秋' : '春'}季学期`
              return {
                ...it,
                name: name,
                markName: isCurrent ? this.current?.markName : name,
                isCurrent: isCurrent,
              }
            })
          })
      },
    }
  }
</script>

<style>
</style>