<template>
	<view>
		<u-icon v-if="iconName" :name="iconName" :size="iconSize + 'rpx'" />
		<image v-if="imgUrl" :src="imgUrl" :style="{ width: imgSize + 'rpx', height: imgSize + 'rpx' }" />
	</view>
</template>

<script>
	import loadIcons from '../svgIcon/data.js'
	import {
		getFile2SvgIcon,
		isImageFile
	} from '@/common/utils/icon.js'

	export default {
		props: {
			fileType: Number,
			fileName: String,
			fileUrl: String,
			file: Object,
			files: Object,
			localFile: Object,
			iconSize: {
				type: Number,
				default: 96
			},

			imgSize:{
				type: Number,
				default: 96
			}
		},
		data() {
			return {
				icons: {},
				iconName: '',
				imgUrl: '',
			};
		},
		async created() {
			this.icons = await loadIcons;
			this.setIconName();
		},
		methods: {
			setIconName() {
				if (this.fileType === 2) {
					this.iconName = this.icons.file2Folder;
					return;
				}
				const name = this.fileName;
				if (this.fileUrl && isImageFile(name)) {
					this.imgUrl = this.fileUrl;
					this.iconName = '';
				} else {
					this.iconName = this.icons[getFile2SvgIcon(name)];
					this.imgUrl = '';
				}
			}
		}
	};
</script>