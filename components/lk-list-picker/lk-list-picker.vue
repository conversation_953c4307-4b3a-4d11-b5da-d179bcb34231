<template>
  <u-popup :show="show" :mode="mode" :round="round" :safeAreaInsetBottom="false"
    :customStyle="{overflow:'hidden', minHeight: 0, minWidth: 0}" :closeOnClickOverlay="false">
    <view class="lk-list-picker" :style="[ panelStyle ]">

      <view v-if="title" class="lk-list-picker-title">{{ title }}</view>

      <view class="lk-list-picker-search">
        <u-search placeholder="请输入关键字" v-model="keyword" bgColor="#f7f7f7" :showAction="false" shape="square" />
      </view>

      <scroll-view v-if="multiple" class="lk-list-picker-list" scroll-y="true">
        <view v-for="item in filterList" :key="item[valueName]" class="lk-list-picker-list-item"
          @click="clickCheckBox(item)">
          <view class="lk-list-picker-list-item-checkbox" :class="{ checked: checkMap[item[valueName]]}">
            <u-icon name="checkbox-mark" size="26rpx" :color="checkMap[item[valueName]] ? '#FFFFFF' : 'transparent'" />
          </view>
          <view class="lk-list-picker-list-item-text" :class="{ checked: checkMap[item[valueName]] }">
            {{ item[labelName] }}
          </view>
          <view v-if="multipleLimit > 0" class="lk-list-picker-list-item-mask"></view>
        </view>
      </scroll-view>
      <scroll-view v-else class="lk-list-picker-list" scroll-y="true">
        <view v-for="item in filterList" :key="item[valueName]" class="lk-list-picker-list-item"
          @click="clickRadio(item)">
          <view class="lk-list-picker-list-item-radio" :class="{ checked: item[valueName] == checkValue}">
            <u-icon name="checkbox-mark" size="26rpx"
              :color="item[valueName] == checkValue ? '#FFFFFF' : 'transparent'" />
          </view>
          <view class="lk-list-picker-list-item-text" :class="{ checked: item[valueName] == checkValue}">
            {{ item[labelName] }}
          </view>
        </view>
      </scroll-view>

      <view class="lk-list-picker-footer">
        <u-button v-if="showResetButton" plain color="#1CBE83" @click="reset">重置</u-button>
        <u-button plain color="#1CBE83" @click="cancel">取消</u-button>
        <u-button type="primary" color="#1CBE83" @click="confirm">确定</u-button>
      </view>
      <u-safe-bottom v-if="safeBottom"></u-safe-bottom>
    </view>
  </u-popup>
</template>

<script>
  export default {
    name: 'lk-list-picker',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
      list: {
        type: Array,
        default: () => {},
      },
      value: {
        type: String,
        default: ''
      },
      labelName: {
        type: String,
        default: 'label',
      },
      valueName: {
        type: String,
        default: 'value',
      },
      multiple: {
        type: Boolean,
        default: false,
      },
      multipleLimit: {
        type: Number,
        default: 0,
      },
      mode: {
        type: String,
        default: 'bottom',
      },
      round: {
        type: String,
        default: '',
      },
      width: {
        type: String,
        default: '80vw',
      },
      height: {
        type: String,
        default: '80vh',
      },
      showResetButton: {
        type: Boolean,
        default: false,
      }
    },
    watch: {
      show(val) {
        if (val) {
          this.init()
        } else {
          this.keyword = ''
          this.checkValue = ''
          this.checkList = []
        }
      },
    },
    mounted() {
      this.init()
    },
    computed: {
      panelStyle() {
        const style = {}
        if (this.mode == 'left' || this.mode == 'right') {
          style.width = this.width || '80vw'
          style.height = '100vh'
        } else if (this.mode == 'top' || this.mode == 'bottom') {
          style.width = '100vw'
          style.height = this.height || '80vw'
        } else {
          style.width = this.width || '80vw'
          style.height = this.height || '80vw'
        }
        return style
      },
      safeBottom() {
        return ['left', 'right', 'bottom'].includes(this.mode)
      },
      filterList() {
        let list
        if (!this.keyword) {
          list = this.list
        } else {
          const keywordList = this.keyword.split(' ').filter(it => it != '')
          if (keywordList.length == 0) {
            list = this.list
          } else {
            list = this.list.filter(it => {
              return !!keywordList.find(kw => {
                if (it[this.labelName].indexOf(kw) >= 0) {
                  return true
                }
                return false
              })
            })
          }
        }
        return list
      },
      checkMap() {
        const map = {}
        this.checkList.forEach(it => {
          map[it] = true
        })
        return map
      }
    },
    data() {
      return {
        keyword: '', // 过滤关键字
        checkValue: '', // 单选
        checkList: [], // 多选
      }
    },
    methods: {
      noop() {},
      clickRadio(item) {
        this.checkValue = item[this.valueName]
      },
      clickCheckBox(item) {
        const index = this.checkList.indexOf(item[this.valueName])
        if (index >= 0) {
          this.checkList.splice(index, 1)
        } else if (this.multipleLimit > 0 && this.checkList.length >= this.multipleLimit) {
          uni.showToast({
            title: `超过最大选择数量：${this.multipleLimit}`,
            icon: 'none',
          })
        } else {
          this.checkList.push(item[this.valueName])
        }
      },
      init() {
        this.checkValue = this.value
        this.checkList = this.value ? this.value.split(',') : []
      },
      reset() {
        this.checkValue = ''
        this.checkList = []
      },
      confirm() {
        const value = this.multiple ? this.checkList.join(',') : this.checkValue
        this.$emit('confirm', value)
      },
      cancel() {
        this.$emit('cancel')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .lk-list-picker {
    background-color: #FFF;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 10rpx;

    &-title {
      padding: 20rpx 30rpx;
      font-size: 30rpx;
      font-weight: bold;
    }

    &-search {
      padding: 20rpx 30rpx;
    }

    &-list {
      flex: 1;
      overflow: hidden;

      &-item {
        display: flex;
        flex-direction: row;
        padding: 10rpx 30rpx;
        position: relative;

        &-checkbox {
          display: flex;
          align-items: center;
          justify-content: center;
          border: solid 1px #c8c9cc;
          border-radius: 30x;
          width: 26rpx;
          height: 26rpx;

          &.checked {
            border-color: #1CBE83;
            background-color: #1CBE83;
          }
        }

        &-radio {
          display: flex;
          align-items: center;
          justify-content: center;
          border: solid 1px #c8c9cc;
          border-radius: 100%;
          width: 26rpx;
          height: 26rpx;

          &.checked {
            border-color: #1CBE83;
            background-color: #1CBE83;
          }

        }

        &-text {
          flex: 1;
          font-size: 26rpx;
          color: #333333;
          text-align: center;

          &.checked {
            color: #1CBE83;
          }
        }

        &-mask {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
      }
    }

    &-footer {
      display: flex;
      align-items: center;
      padding: 30rpx;

      ::v-deep .u-button {
        height: 60rpx;
        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 34rpx;
        border-radius: 12rpx;
        padding: 0;

        &:not(:first-child) {
          margin-left: 12rpx;
        }
      }

    }

  }
</style>
