<template>
  <image :class="['lk-svg', customClass]" :style="[style]" :src="data" :mode="mode" @tap="e => $emit('click', e)">
  </image>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import {
    encode
  } from 'js-base64'

  export default {
    name: 'lk-svg',
    mixins: [mpMixin],
    props: {
      customClass: {
        type: String,
        default: ''
      },
      customStyle: {
        type: Object,
        default: null
      },
      width: {
        type: String,
        default: ''
      },
      height: {
        type: String,
        default: '',
      },
      src: {
        type: String,
        default: ''
      },
      mode: {
        type: String,
        default: 'scaleToFill'
      },
      color: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        rawData: '',
        rawType: '', // url raw base64
      }
    },
    computed: {
      style() {
        const style = {
          ...this.customStyle
        }
        if (this.width) {
          style.width = this.width
        }
        if (this.height) {
          style.height = this.height
        }
        return style
      },
      data() {
        if (this.rawType == 'url' || this.rawType == 'base64') {
          return this.rawData
        }
        
        let svgContent = this.rawData;
        
        if (this.color) {
          svgContent = svgContent
            .replaceAll('"currentColor"', `"${this.color}"`)
            .replaceAll("'currentColor'", `'${this.color}'`)
            .replaceAll('fill="currentColor"', `fill="${this.color}"`)
            .replaceAll("fill='currentColor'", `fill='${this.color}'`);
        }
        
        const base64 = encode(svgContent)
        return `data:image/svg+xml;base64,${base64}`
      }
    },
    watch: {
      src: {
        handler() {
          this.load()
        },
        immediate: true,
      }
    },
    methods: {

      getHttpData() {
        return new Promise((resolve, reject) => {
          uni.request({
            url: this.src,
            success: res => resolve(res.data),
            fail: err => reject(err)
          })
        })
      },

      async getData() {
        if (!this.src) {
          return Promise.reject('src empty')
        }
        if (this.src.startsWith('http')) {
          return await this.getHttpData()
        }
        
        // #ifdef MP
        return new Promise((resolve, reject) => {
          uni.getFileSystemManager().readFile({
            filePath: this.src,
            success: res => resolve(res.data),
            fail: err => reject(err),
            encoding: 'utf-8'
          })
        })
        // #endif
        
        // #ifndef MP
        try {
          const response = await fetch(this.src);
          if (!response.ok) {
            return null;
          }
          const text = await response.text();
          return text;
        } catch (error) {
          return null;
        }
        // #endif
      },

      load() {
        const useGetData = !!this.color

        if (!useGetData) {
          this.rawData = this.src
          this.rawType = 'url'
          return
        }

        this.getData().then(data => {
          if (!data) {
            return
          }
          
          if (data.includes('"currentColor"') || data.includes("'currentColor'") || data.includes('fill="currentColor"')) {
            this.rawData = data
            this.rawType = 'raw'
          } else {
            this.rawData = `data:image/svg+xml;base64,${encode(data)}`
            this.rawType = 'base64'
          }
        }).catch(err => {
        })
      }
    }
  }
</script>

<style>
</style>