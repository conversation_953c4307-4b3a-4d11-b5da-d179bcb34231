<template>
	<!-- 发布动态 -->
	<view class="upload">
		<!-- 图片和文件 -->
		<!-- <view class="upload-preview-list">
			<view class="upload-preview-item" v-for="(item, index) in sortedList" :key="index"
				@tap.stop="openPrewvieIimg(sortedList.map(item => item.fileUrl), index)">
				<view class="upload-image-list-item" v-if="item.type === 'image_url'">
					<image height="100rpx" radius="8rpx" width="100rpx" mode="aspectFill" class="cover"
						:src="item.fileUrl">
					</image>
				</view>
				<view v-if="item.type === 'file_url'" class="upload-file-list-item">
					<view class="file-key-container"
						:style="{ backgroundColor: getExtensionColor(item.fileKey), '--after-border-color': getAfterColor(item.fileKey) }">
						{{ extension(item.fileKey) }}
					</view>
					<view class="file-details-container">
						<view class="file-name">
							{{ item.fileName }}
						</view>
						<view class="file-info">
							{{ item.fileType }}, {{ (item.fileSize / 1000).toFixed(0) }}kb
						</view>
					</view>
				</view>
				<image v-if="!isShow" src="@/static/common/close_img.svg" mode="" class="delete-btn"
					@tap.stop="deleteImage(index)"></image>
			</view>
		</view> -->

		<!-- 上传图片文件 -->
		<view class="popup">
			<view class="content upload-content">
				<view v-if="canSelectImg" class="content-item" @click="chooseImageFromCamera">
					<view class="upload-icon">
						<image src="@/static/common/camera.svg" mode=""></image>
					</view>
					<view class="content-item-lable">
						相机
					</view>
				</view>
				<view v-if="canSelectImg" class="content-item" @click="chooseImageFromAlbum">
					<view class="upload-icon">
						<image src="@/static/common/phone_img.svg" mode=""></image>
					</view>
					<view class="content-item-lable">
						手机图片
					</view>
				</view>
				<view v-if="canSelectImg" class="content-item" @click="chooseImageFromWeChat">
					<view class="upload-icon">
						<image src="@/static/common/wx_img_icon.svg" mode=""></image>
					</view>
					<view class="content-item-lable">
						微信图片
					</view>
				</view>
				<view v-if="canSelectFile" class="content-item">
					<view class="upload-icon">
						<image src="@/static/common/wx_file_icon.svg" mode="" @click="chooseFileFromWeChat"></image>
					</view>
					<view class="content-item-lable">
						微信文件
					</view>
				</view>
			</view>
		</view>
		<!-- #ifdef MP-WEIXIN || ENV_DEV -->
		<!-- 隐私 -->
		<lk-mp-privacy></lk-mp-privacy>
		<!-- #endif -->
	</view>
</template>

<script>
	import envcfg from '@/common/config/index.js'
	import api from "@/api/circleApi/releaseDynamic.js";
	import login from '../../api/login';
	import {
		mapState
	} from 'vuex'
	export default {
		props: {
			value: {
				type: Array,
				default: () => []
			},
			maxCount: {
				type: Number,
				default: null
			},
			isShow: {
				type: Boolean,
				default: false
			},
			isFileShow: {
				type: Boolean,
				default: true
			},
			isImgShow: {
				type: Boolean,
				default: true
			},
			// 是否在组件内触发确认提交
			confirmTrigger: {
				type: Boolean,
				default: false
			},
			canSelectImg: {
				type: Boolean,
				default: true
			},
			canSelectFile: {
				type: Boolean,
				default: true
			}
		},
		watch: {
			value: {
				handler(val) {
					this.initData()
				},
				deep: true
			},
		},
		mounted() {
			this.initData()
		},
		data() {
			return {
				form: {
					content: ''
				},
				imageList: [],
				fileList: [],
				fileShow: true,
				selectList: [],
				selectShow: false,
				id: "", //圈子id
				selectName: '',
				name: null,
				taskName: null,
				topicId: null,
				taskId: null,
				isSubmitting: false, // 防止连续点击
				bottomDistance: 0, // 底部栏距离底部的距离，默认为0
			}
		},
		computed: {
			...mapState(['userInfo']),
			photoNum() {
				return this.imageList.length
			},
			combinedList() {
				return [...this.imageList, ...this.fileList];
			},
			sortedList() {
				return this.combinedList.sort((a, b) => a.uploadTime - b.uploadTime);
			}
		},
		onShow() {},
		methods: {
			initData() {
				this.imageList = this.value.filter(item => item.type === 'image_url');
				this.fileList = this.value.filter(item => item.type === 'file_url');
			},
			openPrewvieIimg(urls, index) {
				uni.previewImage({
					urls: urls || [],
					current: urls[index],
					fail: () => this.$u.toast('打开失败'),
				})
			},
			openFile(url) {
				if (/\.(jpg|jpeg|bmp|png)(\?|^)/i.test(url)) {
					// 预览图片
					uni.previewImage({
						urls: [url],
						fail: () => this.$u.toast('打开文件失败'),
					})
					return
				}
				// 先下载
				uni.downloadFile({
					url: url,
					success: res => {
						// 打开文档
						uni.openDocument({
							filePath: res.tempFilePath,
							showMenu: true,
							fail: err => {
								if (err?.errMsg?.includes('filetype not supported')) {
									this.$u.toast('不支持的文件类型')
								} else if (err?.errMsg?.includes('file doesn\'t exist')) {
									this.$u.toast('文件不存在')
								} else {
									this.$u.toast('打开文件失败')
								}
							}
						})
					},
					fail: () => this.$u.toast('打开文件失败'),
				})
			},
			// 获取扩展名的第一个字符
			extension(val) {
				if (!val) {
					return
				}
				const filename = val
				const extension = filename.split('.').pop();
				const firstChar = extension.charAt(0);
				return firstChar.toUpperCase()
			},
			getExtensionColor(filename) {
				const match = this.extension(filename)
				switch (match) {
					case 'P':
						return '#ef6000';
					case 'D':
						return '#15a4ff';
					case 'X':
						return '#40d48d';
					default:
						return '#767a82'; // 默认颜色
				}
			},
			getAfterColor(filename) {
				const match = this.extension(filename)
				switch (match) {
					case 'P':
						return '#ffc9a4';
					case 'D':
						return '#89d1ff';
					case 'X':
						return '#9affcf';
					default:
						return '#ccc'; // 默认颜色
				}
			},
			deleteImage(index) {
				this.combinedList.splice(index, 1);
				this.$emit('input', this.combinedList);
			},
			fileClose() {
				this.fileShow = false
			},
			fileSubmit() {
				this.fileShow = false
				this.$emit('submit', this.combinedList)
			},
			selectClose() {
				this.selectShow = false
			},
			// 选择手机相册中的图片
			chooseImageFromAlbum() {
				const count = this.maxCount ? this.maxCount - this.combinedList.length : this.maxCount;
				if (this.maxCount && count === 0) {
					uni.showToast({
						title: `最多选择${this.maxCount}个文件`,
						icon: 'none'
					});
					return;
				}
				uni.chooseImage({
					count: count,
					//设置选择图片的数量
					sizeType: ['original', 'compressed'],
					sourceType: ['album'], // 从相册选择
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						this.uploadToServer(tempFilePaths, 'image_url'); // 上传图片
					}
				});
			},
			// 选择微信聊天记录中的图片
			chooseImageFromWeChat() {
				const count = this.maxCount ? this.maxCount - this.combinedList.length : this.maxCount;
				if (this.maxCount && count === 0) {
					uni.showToast({
						title: `最多选择${this.maxCount}个文件`,
						icon: 'none'
					});
					return;
				}
				wx.chooseMessageFile({
					count: count, // 你可以根据需要选择多个文件
					type: 'image', // 选择图片类型的文件
					success: (res) => {
						const tempFiles = res.tempFiles.map(file => file.path);
						this.uploadToServer(tempFiles, 'image_url'); // 上传图片
					}
				});
			},
			// 选择微信聊天记录中的文件
			chooseFileFromWeChat() {
				const count = this.maxCount ? this.maxCount - this.combinedList.length : this.maxCount;
				if (this.maxCount && count === 0) {
					uni.showToast({
						title: `最多选择${this.maxCount}个文件`,
						icon: 'none'
					});
					return;
				}
				wx.chooseMessageFile({
					count: count, // 允许选择的文件数量
					type: 'file', // 选择文件类型的文件
					success: (res) => {
						const tempFiles = res.tempFiles;
						this.uploadToServer(tempFiles, 'file_url'); // 上传文件
					},
					fail: (err) => {
						uni.showToast({
							title: '选择文件失败',
							icon: 'none'
						});
					}
				});
			},
			// 选择相机拍摄的图片
			chooseImageFromCamera() {
				const count = this.maxCount ? this.maxCount - this.combinedList.length : this.maxCount;
				if (this.maxCount && count === 0) {
					uni.showToast({
						title: `最多选择${this.maxCount}个文件`,
						icon: 'none'
					});
					return;
				}
				uni.chooseImage({
					count: count, //设置选择图片的数量
					sizeType: ['original', 'compressed'],
					sourceType: ['camera'], // 从相机拍摄
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						this.uploadToServer(tempFilePaths, 'image_url'); // 上传图片
					}
				});
			},
			selectConfirm() {
				this.selectClose(); // 关闭弹窗
			},
			uploadToServer(event, type) {
				let files = event;
				console.log(files,'files');
				// 显示加载提示
				uni.showLoading({
					title: '上传中...'
				});
				// 遍历文件数组
				files.forEach((file, index) => {
					uni.uploadFile({
						url: `${envcfg.baseUrl}/instrument/system/file/public/upload${file.type === 'file' ? `?filename=${file.name}` : '' }`, // 后端用于处理图片并返回图片地址的接口
						header: {
							Authorization: uni.getStorageSync("token"),
						},
						filePath: type === 'image_url' ? file : file.path, // 文件路径
						name: "file", // 默认
						success: (res) => {
							let data = JSON.parse(res.data); // 返回的是字符串，需要转成对象格式
							if (data.code == 200) {
								const fileData = {
									fileUrl: data.data.fileUrl,
									fileName: data.data.fileName,
									fileKey: data.data.fileKey,
									id: data.data.id,
									fileType: data.data.fileType,
									fileSize: data.data.fileSize,
									type: type,
									uploadTime: Date.now() // 记录上传时间
								}
								this.combinedList.push(fileData);
								this.$emit('input', [...this.combinedList]);
								if (!this.confirmTrigger) {
									this.fileClose()
								} else {
									this.fileSubmit()
								}
							}
							// 关闭加载提示
							if (index === files.length - 1) {
								uni.hideLoading();
							}
						},
						fail: (err) => {
							// 关闭加载提示
							uni.hideLoading();
						},
					});
				});
			},
			close() {
				uni.$emit('showReleaseDynamic')
				this.navBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #ffffff !important;
	}

	.upload {
		flex: 1;
		display: flex;
		flex-direction: column;
		margin-top: 36rpx;

		::v-deep .u-textarea {
			min-height: 200rpx;
			overflow-y: hidden;
			margin-bottom: 32rpx;
			padding-bottom: 48rpx;
		}

		.upload-preview-list {
			display: flex;
			flex-direction: row;
			overflow-x: auto;
			/* 允许横向滚动 */
			white-space: nowrap;
			/* 防止子元素换行 */
			padding-left: 10rpx;
			padding-right: 10rpx;
		}

		.upload-preview-item {
			position: relative;
			margin-right: 20rpx;
			/* 增加间距 */
		}

		.upload-image-list-item {
			display: flex;
			flex-direction: row;
			/* 水平排列 */
			align-items: center;
			position: relative;
			font-size: 32rpx;
			padding: 10rpx;
			border-radius: 8rpx;
			width: 120rpx;
			height: 120rpx;

			.cover {
				object-fit: cover;
				height: 120rpx;
				width: 120rpx;
				border-radius: 8rpx;
			}
		}

		.delete-btn {
			width: 40rpx;
			height: 40rpx;
			position: absolute;
			top: -15rpx;
			right: -15rpx;
			/* 调整位置到右上角 */
		}

		.upload-file-list-item {
			display: flex;
			flex-direction: row;
			/* 水平排列 */
			align-items: center;
			position: relative;
			background-color: #f7f8fa;
			color: #6A6A6D;
			font-size: 20rpx;
			padding: 10rpx;
			border-radius: 8rpx;
			width: 240rpx;
			height: 100rpx;

			.file-key-container {
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 24rpx;
				color: white;
				font-family: Arial, sans-serif;
				font-size: 48rpx;
				text-align: center;
				line-height: 100rpx;
				padding: 0rpx 28rpx;
				margin-right: 12rpx;
			}

			.file-details-container {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				width: 180rpx;
			}

			.file-name {
				font-size: 28rpx;
				color: #05101F;
				margin-bottom: 5rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				width: 160rpx;
			}

			.file-info {
				font-size: 24rpx;
				color: #6A6A6D;
				width: 180rpx;
			}
		}

		.popup {
			// padding: 32rpx;

			.upload-content {
				height: 160rpx !important;
			}

			.content {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 100%;

				.content-item {
					width: 33%;
					text-align: center;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: space-between;

					.upload-icon {
						width: 88rpx;
						height: 88rpx;
						border-radius: 28rpx;
						box-shadow: 0rpx 0rpx 13rpx 0rpx rgba(0,0,0,0.04);
						background-color: #ffffff;
						display: flex;
						align-items: center;
						justify-content: center;

						image {
							width:45rpx;
							height: 45rpx;
						}
					}

					.content-item-lable {
						font-size: 28rpx;
						margin-top: 8rpx;
						color: #75777b;
					}
				}

				.select-list {
					width: 100%;
					padding-top: 30rpx;
					height: 100%;

					.select-list-item {
						display: flex;
						background-color: #f7f7f7;
						justify-content: space-between;
						align-items: center;
						padding: 20rpx;
						margin-bottom: 20rpx;

						.select-list-item-content {
							display: flex;
							align-items: center;
						}

						.icon {
							width: 32rpx;
							height: 36rpx;
							margin-right: 32rpx;
						}

						.select-list-item-title {
							font-size: 32rpx;
							color: #05101F;
							line-height: 46rpx;
						}

						.select-list-item-book-name {
							font-size: 28rpx;
							color: #6A6A6D;
							line-height: 46rpx;
						}
					}

					.select-list-item-upload-num {
						font-size: 24rpx;
						color: #6A6A6D;
					}
				}
			}
		}

		.upload-bottom {
			position: fixed;
			left: 0;
			right: 0;
			height: 120rpx;
			line-height: 120rpx;
			background-color: #f5f5f5;
			z-index: 100;

			.upload-bottom-btn {
				display: flex;
				justify-content: space-between;
				align-items: center;
				color: #05101F;
				font-size: 28rpx;

				.btn {
					padding: 0 58rpx;
				}
			}
		}
	}
</style>