<template>
	<!-- 发布动态 -->
	<view class="upload">

		<!-- 图片 -->
		<view class="upload-img-list" v-if="isImgShow">
			<view class="upload-img-list-item upload-cover" v-if="!isShow &&type=='image'" @tap.stop="handleShowUpload">
				<slot name="img-upload">
					<u-icon name="plus" color="#8f97a0" size="34"></u-icon>
				</slot>
			</view>

			<view class="upload-img-list-item" v-for="(item, index) in imageList" :key="index"
				@tap.stop="openPrewvieIimg(imageList.map(item=>item.fileUrl),index )">

				<image height="100%" radius="8rpx" width="100%" mode="aspectFill" class="cover" :src="item.fileUrl">
					</u--image>
					<image v-if="!isShow" src="@/static/common/close_img.png" mode="" class="delete-btn"
						@tap.stop="deleteImage('imageList',index)"></image>
			</view>

		</view>

		<!-- 文件 -->
		<view class="upload-file-list" v-if="isFileShow">
			<view class="upload-file-list-item" v-for="(item, index) in fileList" :key="index"
				@tap.stop="openFile(item.fileUrl)">
				<view class="icon-container"
					:style="{ backgroundColor: getExtensionColor(item.fileKey), '--after-border-color': getAfterColor(item.fileKey) }">
					{{ extension(item.fileKey) }}
				</view>
				<view class="upload-file-list-item-name">
					{{ item.fileName }}
				</view>
				<image v-if="!isShow" src="@/static/common/close_img.png" mode="" class="delete-btn"
					@tap.stop="deleteImage('fileList',index)"></image>
			</view>
		</view>
		<view class="upload-handle" v-if="!isShow &&type=='file'">
			<view class="upload-handle-item" @tap.stop="handleShowUpload">
				<slot name="button">
					<view class="default-button">
						<image src="@/static/common/file.png" class="upload-handle-icon">
						</image>{{
							type=='image'?'图片':'文件'
						}}
					</view>
				</slot>
			</view>
		</view>
		<!-- 上传图片文件 -->
		<u-popup :round="10" :show="fileShow" mode="bottom" @close="fileClose">
			<view class="popup">
				<view class="head">
					<view class="" @click="fileClose">取消</view>
					<view class="title">
						上传文件
					</view>
					<view class="" @click="fileSubmit">
						确认
					</view>

				</view>
				<view class="content upload-content">
					<view class="content-item" v-if="type=='image'" @click="chooseImageFromAlbum">
						<image src="@/static/common/phone_img_icon.png" mode=""></image>
						<view class="content-item-lable">
							手机图片
						</view>
					</view>
					<view class="content-item" v-if="type=='image'" @click="chooseImageFromWeChat">
						<image src="@/static/common/wx_img_icon.svg" mode=""></image>
						<view class="content-item-lable">
							微信聊天图片
						</view>
					</view>
					<view class="content-item" v-if="type=='file'">
						<image src="@/static/common/wx_file_icon.png" mode="" @click="chooseFileFromWeChat"></image>
						<view class="content-item-lable">
							微信聊天文件
						</view>
					</view>
				</view>
			</view>

		</u-popup>
		<!-- #ifdef MP-WEIXIN || ENV_DEV -->
		<!-- 隐私 -->
		<lk-mp-privacy></lk-mp-privacy>

		<!-- #endif -->
	</view>
</template>

<script>
	import envcfg from '@/common/config/index.js'
	import api from "@/api/circleApi/releaseDynamic.js";
	import login from '../../api/login';
	import {
		mapState
	} from 'vuex'
	export default {
		props: {
			type: {
				type: String,
				default: 'image'
			},
			value: {
				type: Array,
				default: () => []
			},
			maxCount: {
				type: Number,
				default: 9
			},
			isShow: {
				type: Boolean,
				default: false
			},
			isFileShow: {
				type: Boolean,
				default: true
			},
			isImgShow: {
				type: Boolean,
				default: true
			},
			// 是否在组件内触发确认提交
			confirmTrigger: {
				type: Boolean,
				default: false
			}
		},
		watch: {
			value: {
				handler(val) {
					this.initData()
				},
				deep: true
			},

		},
		mounted() {
			this.initData()
		},
		data() {
			return {
				form: {
					content: ''
				},
				imageList: [],
				fileList: [],
				fileShow: false,
				selectList: [],
				selectShow: false,
				id: "", //圈子id
				selectName: '',
				name: null,
				taskName: null,
				topicId: null,
				taskId: null,
				isSubmitting: false, // 防止连续点击
				bottomDistance: 0, // 底部栏距离底部的距离，默认为0
			}
		},
		computed: {
			...mapState(['userInfo']),
			photoNum() {
				return this.imageList.length
			},
		},
		onShow() {

		},


		methods: {
			handleShowUpload() {
				this.fileShow = true
			},
			initData() {
				if (this.type == 'image') {
					this.imageList = [...this.value]
				} else {
					this.fileList = [...this.value]
				}
			},
			openPrewvieIimg(urls, index) {
				uni.previewImage({
					urls: urls || [],
					current: urls[index],
					fail: () => this.$u.toast('打开失败'),
				})
			},
			openFile(url) {
				if (/\.(jpg|jpeg|bmp|png)(\?|^)/i.test(url)) {
					// 预览图片
					uni.previewImage({
						urls: [url],
						fail: () => this.$u.toast('打开文件失败'),
					})
					return
				}
				// 先下载
				uni.downloadFile({
					url: url,
					success: res => {
						// 打开文档
						uni.openDocument({
							filePath: res.tempFilePath,
							showMenu: true,
							fail: err => {
								if (err?.errMsg?.includes('filetype not supported')) {
									this.$u.toast('不支持的文件类型')
								} else if (err?.errMsg?.includes('file doesn\'t exist')) {
									this.$u.toast('文件不存在')
								} else {
									this.$u.toast('打开文件失败')
								}
							}
						})
					},
					fail: () => this.$u.toast('打开文件失败'),
				})
			},
			// 获取扩展名的第一个字符
			extension(val) {
				if (!val) {
					return
				}
				const filename = val
				const extension = filename.split('.').pop();
				const firstChar = extension.charAt(0);
				return firstChar.toUpperCase()
			},
			getExtensionColor(filename) {
				const match = this.extension(filename)
				switch (match) {
					case 'P':
						return '#ef6000';
					case 'D':
						return '#15a4ff';
					case 'X':
						return '#40d48d';
					default:
						return '#767a82'; // 默认颜色
				}
			},
			getAfterColor(filename) {
				const match = this.extension(filename)
				switch (match) {
					case 'P':
						return '#ffc9a4';
					case 'D':
						return '#89d1ff';
					case 'X':
						return '#9affcf';
					default:
						return '#ccc'; // 默认颜色
				}
			},

			deleteImage(list, index) {
				this[list].splice(index, 1);
				this.$emit('input', this[list]);
			},
			fileClose() {
				this.fileShow = false
			},
			fileSubmit() {
				this.fileShow = false
				this.$emit('submit', this.fileList)
			},
			selectClose() {
				this.selectShow = false
			},
			// 检查是否可以选择图片或文件
			canSelect(type) {
				if (type === 'image' && this.fileList.length > 0) {
					uni.showToast({
						title: '已选择文件，不能再选择图片',
						icon: 'none'
					});
					return false;
				}
				if (type === 'file' && this.photoNum > 0) {
					uni.showToast({
						title: '已选择图片，不能再选择文件',
						icon: 'none'
					});
					return false;
				}
				return true;
			},
			// 选择手机相册中的图片
			chooseImageFromAlbum() {
				if (!this.canSelect('image')) return;
				const count = this.maxCount - this.photoNum;
				if (count === 0) {
					uni.showToast({
						title: '最多选择' + this.maxCount + '张图片',
						icon: 'none'
					});
					return;
				}
				uni.chooseImage({
					count: count, //设置选择图片的数量
					sizeType: ['original', 'compressed'],
					sourceType: ['album'], // 从相册选择
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						this.uploadToServer(tempFilePaths, 'album'); // 上传图片
					}
				});
			},
			// 选择微信聊天记录中的图片
			chooseImageFromWeChat() {
				if (!this.canSelect('image')) return;
				const count = this.maxCount - this.photoNum;
				if (count === 0) {
					uni.showToast({
						title: `最多选择${this.maxCount}张图片`,
						icon: 'none'
					});
					return;
				}
				wx.chooseMessageFile({
					count: count, // 你可以根据需要选择多个文件
					type: 'image', // 选择图片类型的文件
					success: (res) => {
						const tempFiles = res.tempFiles;
						this.uploadToServer(tempFiles, 'image'); // 上传图片
					}
				});
			},
			// 选择微信聊天记录中的文件
			chooseFileFromWeChat() {
				if (!this.canSelect('file')) return;
				const count = this.maxCount - this.fileList.length;
				if (count === 0) {
					uni.showToast({
						title: `最多选择${this.maxCount}个文件`,
						icon: 'none'
					});
					return;
				}
				wx.chooseMessageFile({
					count: count, // 允许选择的文件数量
					type: 'file', // 选择文件类型的文件
					success: (res) => {
						const tempFiles = res.tempFiles;
						this.uploadToServer(tempFiles, 'file'); // 上传文件
					},
					fail: (err) => {
						uni.showToast({
							title: '选择文件失败',
							icon: 'none'
						});
					}
				});
			},

			selectConfirm() {
				this.selectClose(); // 关闭弹窗
			},

			uploadToServer(event, type) {
				let files = event;
				// 显示加载提示
				uni.showLoading({
					title: '上传中...'
				});
				// 遍历文件数组
				files.forEach((file, index) => {
					uni.uploadFile({
						url: `${envcfg.baseUrl}/instrument/system/file/public/upload`, // 后端用于处理图片并返回图片地址的接口
						header: {
							Authorization: uni.getStorageSync("token"),
						},
						filePath: type == 'album' ? file : file.path, // 文件路径
						name: "file", // 默认
						success: (res) => {
							let data = JSON.parse(res.data); // 返回的是字符串，需要转成对象格式
							if (data.code == 200) {
								const fileData = {
									fileUrl: data.data.uploadVirtualPath,
									fileName: file.name,
									fileKey: data.data.uploadPath
								}
								if (type === 'file') {
									this.fileList.push(fileData);
									this.$emit('input', [...this.fileList]);
								} else {
									this.imageList.push(fileData);
									this.$emit('input', [...this.imageList]);
								}
								if (!this.confirmTrigger) {
									this.fileClose()
								} else {
									// uni.showToast({
									//   title: '选择文件成功',
									//   icon: 'none'
									// });
									this.fileSubmit()
								}
							}
							// 关闭加载提示
							if (index === files.length - 1) {
								uni.hideLoading();
							}
						},
						fail: (err) => {
							// 关闭加载提示
							uni.hideLoading();
						},
					});
				});
			},
			close() {
				uni.$emit('showReleaseDynamic')
				this.navBack()
			}
		}
	}
</script>

<style>
	page {
		background-color: #fff;
	}
</style>
<style lang="scss" scoped>
	page {
		background-color: #ffffff !important;
	}

	.upload {
		flex: 1;
		display: flex;
		flex-direction: column;

		::v-deep .u-textarea {
			min-height: 200rpx;
			overflow-y: hidden;
			margin-bottom: 32rpx;
			padding-bottom: 48rpx;
		}

		.upload-handle {
			width: 100%;
			display: flex;


			.upload-handle-item {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 20rpx;
				background-color: #F5F5F5;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				// border-radius: 44rpx;
				color: #323233;
				font-size: 24rpx;

				.default-button {
					width: 30%;
					height: 64rpx;
				}

				.upload-handle-icon {
					width: 32rpx;
					height: 32rpx;
					margin-right: 10rpx;
				}
			}
		}

		.upload-img-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;

			.upload-cover {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 18rpx;
				height: 154rpx;
				background-color: #F5F5F5;
			}

			.upload-img-list-item {
				position: relative;
				width: calc(33.333% - 18rpx);
				margin-right: 18rpx;
				height: 154rpx;
				margin-bottom: 18rpx;
				border-radius: 8rpx 8rpx 8rpx 8rpx;

				.cover {
					object-fit: cover;
					height: 100%;
					width: 100%;
					border-radius: 8rpx;
				}

				.delete-btn {
					width: 36rpx;
					height: 36rpx;
					position: absolute;
					top: 8rpx;
					right: 0;
				}

				.delete-btn::after {
					content: '';
					position: absolute;
					top: -10rpx;
					/* 扩大点击区域 */
					right: -10rpx;
					/* 扩大点击区域 */
					width: 56rpx;
					/* 新的点击区域大小 */
					height: 56rpx;
					/* 新的点击区域大小 */
					border-radius: 50%;
					background: transparent;
					/* 保持透明不影响视觉效果 */
				}
			}

			/* 清除每行最后一个图片的右边距 */
			.upload-img-list-item:nth-child(3n) {
				margin-right: 0;
			}
		}

		.upload-file-list {

			.upload-file-list-item {
				display: flex;
				justify-content: start;
				align-items: center;
				position: relative;
				background-color: #F7F7F7;
				color: #6A6A6D;
				font-size: 28rpx;
				padding: 20rpx;
				border-radius: 8rpx;
				margin-bottom: 20rpx;

				.upload-file-list-item-name {
					width: 440rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.delete-btn {
					width: 30rpx;
					height: 30rpx;
					position: absolute;
					top: 24rpx;
					right: 20rpx;
					padding: 4rpx;
				}

				.icon-container {
					position: relative;
					margin-right: 32rpx;
					/* 设置为相对定位，以便伪元素可以相对于它定位 */
					display: inline-block;
					/* 使用 inline-block 以便可以设置宽高 */
					width: 36rpx;
					height: 48rpx;
					--after-border-color: #ccc;
					color: white;
					font-family: Arial, sans-serif;
					font-size: 24rpx;
					text-align: center;
					line-height: 48rpx;
					border-radius: 4rpx;
				}

				.icon-container::after {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					border-color: transparent transparent var(--after-border-color) var(--after-border-color);
					border-width: 0 12rpx 12rpx 0;
					border-style: solid;
					width: 0;
					height: 0;
					border-radius: 0 0 0 4rpx;
				}

			}

			/* 清除每行最后一个图片的右边距 */
			.upload-img-list-item:nth-child(3n) {
				margin-right: 0;
			}
		}


		.task {
			height: 700rpx;
		}

		.popup {
			padding: 32rpx;

			.head {
				width: 100%;
				display: flex;
				text-align: center;
				justify-content: space-between;
				// justify-content: space-between;

				.title {
					font-size: 32rpx;
					color: #1C1C1E;
				}
			}

			.upload-content {
				height: 260rpx !important;
			}

			.content {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 100%;

				.content-item {
					width: 33%;
					text-align: center;
					justify-content: space-between;

					image {
						width: 80rpx;
						height: 80rpx
					}

					.content-item-lable {
						font-size: 28rpx;
						margin-top: 20rpx;
					}
				}

				.select-list {
					width: 100%;
					padding-top: 30rpx;
					height: 100%;

					.select-list-item {
						display: flex;
						background-color: #f7f7f7;
						justify-content: space-between;
						align-items: center;
						padding: 20rpx;
						margin-bottom: 20rpx;

						.select-list-item-content {
							display: flex;
							align-items: center;
						}

						.icon {
							width: 32rpx;
							height: 36rpx;
							margin-right: 32rpx;
						}

						.select-list-item-title {
							font-size: 32rpx;
							color: #05101F;
							line-height: 46rpx;
						}

						.select-list-item-book-name {
							font-size: 28rpx;
							color: #6A6A6D;
							line-height: 46rpx;
						}
					}

					.select-list-item-upload-num {
						font-size: 24rpx;
						color: #6A6A6D;
					}

				}

			}
		}
	}

	.upload-bottom {
		position: fixed;
		left: 0;
		right: 0;
		height: 120rpx;
		line-height: 120rpx;
		background-color: #f5f5f5;
		z-index: 100;

		.upload-bottom-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			color: #05101F;
			font-size: 28rpx;

			.btn {
				padding: 0 58rpx;
			}
		}
	}
</style>