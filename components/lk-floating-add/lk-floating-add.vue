<template>
  <view class="lk-floating-add" :style="[customStyle]" @tap="$emit('click')">
    <u-icon name="plus" color="#ffffff" size="30rpx"></u-icon>
  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  export default {
    mixins: [mpMixin],
    name: 'lk-floating-add',
    props: {
      customStyle: {
        type: [Object, null],
        default: null
      },
    }
  }
</script>

<style lang="scss" scoped>
  .lk-floating-add {
    width: 96rpx;
    height: 96rpx;
    background-color: #26D1CB;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    right: 34rpx;
    bottom: calc(88rpx + 50px + env(safe-area-inset-bottom));
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, .3);

    /* #ifdef H5 */
    margin-bottom: var(--window-bottom);
    /* #endif */

  }
</style>