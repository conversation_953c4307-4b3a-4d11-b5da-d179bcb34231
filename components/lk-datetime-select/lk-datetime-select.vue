<template>
  <lk-text-box :text="innerText" :color="color" :backgroundColor="backgroundColor" :placeholder="placeholder"
    :placeholderColor="placeholderColor" :fontSize="fontSize" :lineHeight="lineHeight" :multipleLine="multipleLine"
    :noBorder="noBorder" :borderColor="borderColor" :borderRadius="borderRadius" :activeBorderColor="activeBorderColor"
    :prefixIcon="prefixIcon" :activePrefixIcon="activePrefixIcon" :suffixIcon="suffixIcon"
    :activeSuffixIcon="clearable ? 'close-circle' : activeSuffixIcon" :iconColor="iconColor"
    :activeIconColor="activeIconColor" :iconSize="iconSize" :display="display" :stretch="stretch" :align="align"
    :customStyle="customStyle" :textStyle="textStyle" :placeholderStyle="placeholderStyle" :contentStyle="contentStyle"
    :iconStyle="iconStyle" :prefixIconStyle="prefixIconStyle" :suffixIconStyle="suffixIconStyle"
    @click="showPicker = true" @clickSuffix="clickSuffix">

    <view slot="extend">
      <u-datetime-picker v-if="showPicker" :show="true" :value="pickerValue" @confirm="pickerConfirm" :mode="mode"
        @cancel="showPicker = false"
				:formatter="formatter"
				>
      </u-datetime-picker>
    </view>

  </lk-text-box>
</template>

<script>
  import dayjs from '@/common/utils/day.js'

  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  export default {
    name: 'lk-datetime-select',
    mixins: [mpMixin, props],
    data() {
      return {
        innerValue: '',
        showPicker: false,
      }
    },
    computed: {
      innerText() {
        if (this.dateSeparator && this.innerValue) {
          if (Array.isArray(this.dateSeparator)) {
            const arr = this.innerValue.split('-')
            const list = [arr[0]]
            for (let i = 0; i < arr.length - 1; i++) {
              list.push(i < this.dateSeparator.length ? this.dateSeparator[i] : '-')
              list.push(arr[i + 1])
            }
            return list.join('')
          }
          return this.innerValue.replaceAll('-', this.dateSeparator)
        }
        return this.innerValue
      },
      pickerValue() {
        if (!this.innerValue) {
          return this.mode == 'time' ? dayjs().format('HH:mm') : Date.now()
        }
        return this.innerValue
      }
    },
    watch: {
      value(val) {
        this.innerValue = this.toInnerValue(val)
      }
    },
    created() {
      this.innerValue = this.toInnerValue(this.value)
    },
    methods: {
			formatter(type, value) {
			    if (type === 'year') {
			        return `${value}年`
			    }
			    if (type === 'month') {
			        return `${value}月`
			    }
			    if (type === 'day') {
			        return `${value}日`
			    }
					if (type === 'hour') {
					    return `${value}时`
					}
					if (type === 'minute') {
					    return `${value}分`
					}

			    return value
			},
      toInnerValue(value) {
        if (value) {
          if (this.mode == 'date') {
            return dayjs(value).format('YYYY-MM-DD')
          } else if (this.mode == 'year-month') {
            return dayjs(value).format('YYYY-MM')
          } else if (this.mode == 'time') {
            return typeof value == 'number' ? dayjs(value).format('HH:mm') : value
          } else if(this.mode == 'datetime'){
						return  dayjs(value).format('YYYY-MM-DD HH:mm:ss')
					}
        }
        return ''
      },
      emitValue(value) {
        this.innerValue = value
        this.$emit('input', this.innerValue)
        this.$emit('change', this.innerValue)
        this.$nextTick(() => {
          uni.$u.formValidate(this, 'change')
        })
      },
      pickerConfirm(e) {
				console.log(e)
        this.showPicker = false
        this.emitValue(this.toInnerValue(e.value))
      },
      clickSuffix(e) {
        if (this.clearable && this.innerValue) {
          e.stop = true
          this.emitValue('')
          this.$emit('clear')
        }
      }
    }
  }
</script>

<style>
</style>