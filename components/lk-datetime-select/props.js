import textBoxProps from '@/components/lk-text-box/props.js'

export default {
  mixins: [textBoxProps],
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // date year-month
    mode: {
      type: String,
      default: 'date'
    },
    dateSeparator: {
      type: [String, Array],
      default: ''
    },
    suffixIcon: {
      type: String,
      default: 'arrow-down'
    },
    fontSize: {
      type: String,
      default: '26rpx'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    textStyle:{
      type:Object,
      default:()=>{}
    },
    customStyle:{
      type:Object,
      default:()=>{}
    }
  }
}
