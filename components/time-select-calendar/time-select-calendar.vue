<template>
  <view class="time-select-calendar">
    <!-- 月份标题 -->
    <view class="month-header">
      <text class="month-title">{{ currentYear }}年{{ currentMonth }}月</text>
    </view>

    <!-- 周标题 -->
    <view class="week-header">
      <text class="week-day">日</text>
      <text class="week-day">一</text>
      <text class="week-day">二</text>
      <text class="week-day">三</text>
      <text class="week-day">四</text>
      <text class="week-day">五</text>
      <text class="week-day">六</text>
    </view>
    
    <!-- 日历内容区域，动态高度 -->
    <view class="month-grid" :style="{ height: calendarHeight + 'px' }">
      <view class="calendar-row" v-for="(row, rowIdx) in calendarRows" :key="rowIdx">
        <view 
          v-for="(day, colIdx) in row" 
          :key="colIdx"
          :class="['calendar-day', { 'empty': !day, 'active': day === selectedDay } ]"
          @tap.stop="day && selectDay(day)"
        >
          <text v-if="day" class="day-number">{{ day }}</text>
        </view>
      </view>
    </view>
    
    <!-- 拖动横线指示器 -->
    <view 
      class="calendar-toggle"
      @touchstart="handleToggleTouchStart"
      @touchmove="handleToggleTouchMove"
      @touchend="handleToggleTouchEnd"
    >
      <view class="toggle-line"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TimeSelectCalendar',
  props: {
    selectedDay: {
      type: Number,
      default: null
    },
    minHeight: {
      type: Number,
      default: 150
    },
    maxHeight: {
      type: Number,
      default: 350
    }
  },
  data() {
    return {
      calendarHeight: 150,
      dragStartY: 0,
      dragStartHeight: 150,
      calendarRows: [],
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      currentDate: new Date()
    };
  },
  mounted() {
    this.initCalendar();
    this.calendarHeight = this.minHeight;
  },
  methods: {
    // 初始化日历数据
    initCalendar() {
      // 获取当前月份的第一天
      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1);
      // 获取当前月份的最后一天
      const lastDay = new Date(this.currentYear, this.currentMonth, 0);
      // 获取当前月份有多少天
      const daysInMonth = lastDay.getDate();
      // 获取当前月份第一天是星期几（0=周日, 1=周一, ..., 6=周六）
      const firstDayOfWeek = firstDay.getDay();

      // 构造日历数据
      const days = [];

      // 填充前面的空白天数
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push(null);
      }

      // 填充当前月份的天数
      for (let d = 1; d <= daysInMonth; d++) {
        days.push(d);
      }

      // 填充后面的空白天数，确保总共42个位置（6行×7列）
      while (days.length < 42) {
        days.push(null);
      }

      // 按每7天分组
      this.calendarRows = [];
      for (let i = 0; i < 42; i += 7) {
        this.calendarRows.push(days.slice(i, i + 7));
      }
    },
    
    // 选择日期
    selectDay(day) {
      this.$emit('select-day', day);
    },
    
    // 拖拽开始
    handleToggleTouchStart(e) {
      this.dragStartY = e.touches[0].clientY;
      this.dragStartHeight = this.calendarHeight;
    },
    
    // 拖拽移动
    handleToggleTouchMove(e) {
      const moveY = e.touches[0].clientY;
      let delta = moveY - this.dragStartY;
      let newHeight = this.dragStartHeight + delta;
      if (newHeight < this.minHeight) newHeight = this.minHeight;
      if (newHeight > this.maxHeight) newHeight = this.maxHeight;
      this.calendarHeight = newHeight;
    },
    
    // 拖拽结束
    handleToggleTouchEnd() {
      // 拖动结束，自动吸附到展开或收起
      if (this.calendarHeight > (this.maxHeight + this.minHeight) / 2) {
        this.calendarHeight = this.maxHeight;
      } else {
        this.calendarHeight = this.minHeight;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.time-select-calendar {
  background-color: #fff;
  padding-bottom: 10rpx;
}

/* 月份标题 */
.month-header {
  padding: 20rpx 0;
  text-align: center;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.month-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 周标题 */
.week-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.week-day {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #999;
}

/* 月份网格 */
.month-grid {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  overflow: hidden;
  transition: height 0.3s cubic-bezier(0.4,0,0.2,1);
}

.calendar-row {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
}

.calendar-day {
  flex: 1;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.calendar-day.empty {
  background-color: transparent;
}

.calendar-day .day-number {
  font-size: 30rpx;
  color: #333;
  position: relative;
  z-index: 1;
}

.calendar-day.active::before {
  content: '';
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #26D1CB;
  z-index: 0;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.calendar-day.active .day-number {
  color: #fff;
  font-weight: bold;
  z-index: 1;
}

.calendar-day.disabled .day-number {
  color: #ccc;
}

/* 日历展开/收起切换按钮 */
.calendar-toggle {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 15rpx 0;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  height: 50rpx;
  position: relative;
  z-index: 9;
}

.toggle-line {
  width: 80rpx;
  height: 8rpx;
  background-color: #ddd;
  border-radius: 4rpx;
}
</style>
