<template>
  <u-button type="primary" plain :customStyle="{
      position: 'absolute',
      width: 'auto',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      borderRadius: '12rpx',
  }" @click="checkLogin()">
    <view class="lk-login-button-text">
      <slot>
        登录
      </slot>
    </view>
  </u-button>
</template>

<script>
  export default {
    props: {
      width: {
        type: String,
        default: '10em'
      }
    }
  }
</script>

<style lang="scss" scoped>
  .lk-login-button {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>