<template>
  <view class="lk-lottie" :style="[customStyle]" @tap="e => $emit('click', e)">
    <hx-lottie v-if="options" :options="options"></hx-lottie>
  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'

  export default {
    name: 'lk-lottie',
    mixins: [mpMixin],
    props: {
      customStyle: {
        type: [Object, null],
        default: null
      },
      name: {
        type: String,
        default: ''
      },
      autoplay: {
        type: Boolean,
        default: true
      },
      loop: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        data: null
      }
    },
    computed: {
      options() {
        return this.data ? {
          data: this.data,
          autoplay: this.autoplay,
          loop: this.loop,
        } : null
      }
    },
    watch: {
      name: {
        handler(val) {
          val && this.loadData()
        },
        immediate: true
      }
    },
    methods: {
      loadData() {
        this.data = require(`./data/${this.name}.json`)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .lk-lottie {}
</style>