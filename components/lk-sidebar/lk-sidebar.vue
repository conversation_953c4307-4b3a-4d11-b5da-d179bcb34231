<template>
	<view>
		<view v-if="showParent" class="floating-box">
			<!-- <view v-if="showWorkflowIcon" class="icon" @click="handleWorkFlowClick()">
				<image src="/static/chat/workflow.svg" mode="widthFix" class="icon-image" title="应用工作流程"></image>
			</view> -->
			<view class="icon" @click="handleInstructClick()">
				<image src="/static/chat/new_instruction.png" mode="widthFix" class="icon-image" title='快捷指令'></image>
			</view>
		</view>
		<lk-workflow v-if="showWorkflow" :timestamp="timestamp" @resultValue="updateWorkflowValue"
			@close="closeWorkflow"></lk-workflow>
		<lk-instruct v-if="showInstruct" :timestamp="timestamp" @resultValue="updateInstructValue"
			@toggleHeight="toggleHeight" @close="closeInstruct"></lk-instruct>
	</view>
</template>

<script>
	import api from '@/api/chat.js'

	export default {
		props: {
			sideBarValue: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				showWorkflow: false,
				showInstruct: false,
				showParent: true,
				showWorkflowIcon: false,
				timestamp: null,
			};
		},
		watch: {
			sideBarValue: {
				handler(newVal) {
					if (newVal) {
						this.timestamp = new Date().getTime()

						if (newVal && newVal.id && uni.getStorageSync("userInfo").account) {
							this.checkTenantList(newVal.id);
						}
					}
				},
				deep: true,
				immediate: true
			}
		},
		created() {},
		methods: {
			async checkTenantList(tenantAppId) {
				this.showWorkflowIcon = false;
				try {
					const response = await api.tenantListAll({
						tenantAppId: tenantAppId
					});
					if (response && response.length > 0) {
						this.showWorkflowIcon = true;
					} else {
						this.showWorkflowIcon = false;
					}
				} catch (error) {
					console.error('Error fetching tenant list:', error);
				}
			},
			handleWorkFlowClick(icon) {
				this.showWorkflow = true;
				this.showParent = false;
				this.timestamp = new Date().getTime()
				this.$emit('iconClick', this.timestamp);
			},
			updateWorkflowValue(newValue) {
				this.showInstruct = false;
				this.showParent = true;
				this.$emit('updateSideBarValue', newValue);
			},
			closeWorkflow(newValue) {
				this.showWorkflow = false;
				this.showParent = true;
				if (newValue) {
					this.updateWorkflowValue(newValue);
				}
			},

			handleInstructClick(icon) {
				this.showInstruct = true;
				this.showParent = false;
				this.timestamp = new Date().getTime()
				this.$emit('iconClick', this.timestamp);
			},
			updateInstructValue(newValue) {
				this.showInstruct = false;
				this.showParent = true;
				this.$emit('updateSideBarValue', newValue);
			},
			toggleHeight(height) {
				this.$emit('toggleHeight', height);
			},
			closeInstruct(newValue) {
				this.showInstruct = false;
				this.showParent = true;
				if (newValue) {
					this.updateInstructValue(newValue);
				}
			}
		}
	}
</script>

<style scoped>
	.floating-box {
		position: fixed;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		display: flex;
		flex-direction: column;
		align-items: center;
		padding:2rpx 12rpx 2rpx 10rpx;
		background-color: #ffffff;
		border-radius: 40rpx 0 0 40rpx;
		box-shadow: 0rpx 2rpx 16rpx 0rpx rgba(125,128,143,0.06);
	}

	.icon {
		margin: 12rpx 0;
		width: 80rpx;
		height: 80rpx;
		position: relative;
		background-color: #f2f6ff;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon-image {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
	}
</style>