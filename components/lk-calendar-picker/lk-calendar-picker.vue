<template>
  <u-popup :show="show" mode="bottom" @close="cancel">
    <u-toolbar
      confirmColor="#3d7fff"
      title="选择日期范围"
      @cancel="cancel"
      @confirm="confirm"
    ></u-toolbar>
    <view v-if="show" class="calendar-picker">
      <uni-calendar
        :date="innerDate"
        :lunar="lunar"
        :startDate="minDate"
        :endDate="maxDate"
        :range="range"
        :defaultRange="defaultRange"
        :selected="selected"
        :showMonth="showMonth"
        @change="change"
      ></uni-calendar>
    </view>
  </u-popup>
</template>

<script>
import dayjs from "@/common/utils/day.js";
import mpMixin from "@/common/mixin/mp.js";

export default {
  name: "lk-calendar-picker",
  mixins: [mpMixin],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    // 自定义当前时间，默认为今天
    date: {
      type: String,
      default: null,
    },
    // 显示农历
    lunar: {
      type: Boolean,
      default: false,
    },
    // 日期选择范围-开始日期
    minDate: {
      type: String,
      default: "",
    },
    // 日期选择范围-结束日期
    maxDate: {
      type: String,
      default: "",
    },
    // 范围选择
    range: {
      type: Boolean,
      default: false,
    },
    defaultRange: {
      type: Array,
      default: null,
    },
    selected: {
      type: Array,
      default: () => [],
    },
    // 是否显示月份为背景
    showMonth: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dateList: [],
    };
  },
  computed: {
    innerDate() {
      if (this.range && !this.date && this.defaultRange?.length) {
        return this.defaultRange[0];
      }
      return this.date;
    },
  },
  methods: {
    change(e) {
      if (e.range?.data?.length) {
        this.dateList = e.range.data;
      } else {
        this.dateList = [e.fulldate];
      }
    },
    cancel() {
      this.$emit("cancel");
      this.dateList = [];
    },
    confirm() {
      if (this.dateList.length) {
        this.$emit("confirm", this.dateList);
        this.dateList = [];
      } else {
        const now = dayjs();
        if (
          (this.minDate &&
            now.valueOf() < dayjs(this.minDate).startOf("day").valueOf()) ||
          (this.maxDate &&
            now.valueOf() > dayjs(this.maxDate).endOf("day").valueOf())
        ) {
          uni.showToast({
            title: "请选择日期",
            icon: "none",
          });
        } else {
          this.$emit(
            "confirm",
            this.defaultRange?.length
              ? [...this.defaultRange]
              : [now.format("YYYY-MM-DD")]
          );
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.calendar-picker {
  padding: 0 30rpx 30rpx 30rpx;
  ::v-deep .uni-calendar {
    .uni-calendar-item__weeks-box-text {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #05101f;
      line-height: 48rpx;
      text-align: center;
      font-style: normal;
    }

    .uni-calendar-item--disable {
      color: #c0c0c0;
    }

    .uni-calendar-item__weeks-lunar-text {
      color: #3d7fff;
    }
    .uni-calendar-item__weeks-box{
      min-width: 101rpx;
    }
    .uni-calendar-item--isDay,
    .uni-calendar-item--before-checked,
    .uni-calendar-item--checked,
    .uni-calendar-item--after-checked {
      background: transparent !important;
      opacity: 1;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 48rpx;

      text-align: center;
      .uni-calendar-item__weeks-box-item {
        width: 60rpx;
        height: 60rpx;
        box-sizing: border-box;
        background: linear-gradient( 314deg, #1CBE83 0%, #8DEDC8 100%);
        box-shadow: 0rpx 6rpx 7rpx 0rpx rgba(124,160,255,0.25);
        border-radius: 50%;
          .uni-calendar-item--isDay-text:nth-child(1){
            opacity:0;
          }
      }
    }

    .uni-calendar__weeks {
      padding-bottom: 20rpx;
    }

    .uni-calendar__header-text {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 39rpx;
      text-align: left;
      font-style: normal;
    }

    .uni-calendar__weeks-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .uni-calendar-item--multiple {
     background: #F2F3FF;
      opacity: 1;
    }
  }
}
</style>
