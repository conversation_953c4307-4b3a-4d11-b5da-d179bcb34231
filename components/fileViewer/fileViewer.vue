<template>
  <view>
    <view v-if="showVideo" class="modal">
      <view class="modal-content">
        <video id="videoId" :controls="controls" @fullscreenchange="fullscreenchange" :src="videoUrl" @play="playVideo"></video>
        <view class="close-btn" @click="closeVideo">
          <u-icon name="close-circle" size="24"></u-icon>
        </view>
      </view>
      <view class="modal-overlay" @click="closeVideo"></view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    file: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      videoUrl: '',
      videoContext: null,
      seek: 1000, // 默认获取视频的第一帧作为封面
      showVideo: false, // 控制视频组件的显示
      isFullScreen: false, // 控制全屏状态
      controls: true, // 默认显示控件
      isLoading: false,
    };
  },
  watch: {
    file: {
      immediate: true,
      handler(newFile) {
        if (newFile && newFile.fileUrl) {
          this.openFile(newFile.fileUrl, newFile.fileType);
        }
      }
    }
  },
  methods: {
    openFile(url, fileType) {
      if (this.isLoading) return;
      this.isLoading = true;
      if (fileType !== 1) {
        return;
      }

      const whiteFileTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'];
      const whiteImages = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'ico', 'wbmp', 'fax', 'net', 'rp', 'jfif', 'jpe', 'tiff', 'tif'];
      const whiteMedia = ['mp3', 'wav', 'flac', 'aac', 'mp4', 'avi', 'wmv', 'mov', 'flv'];
			const fileTypeFromUrl = url.split('?')[0].split('.').pop()?.toLowerCase() ?? '';

			console.log(fileTypeFromUrl,'fileTypeFromUrl');
      uni.downloadFile({
        url: url,
        success: (res) => {
          const filePath = res.tempFilePath;
          this.isLoading = false;
          if (whiteFileTypes.includes(fileTypeFromUrl)) {
            // 打开文档
            uni.openDocument({
              filePath: filePath,
              showMenu: true,
              fileType: fileTypeFromUrl,
              success: () => {
              },
              fail: (err) => {
                uni.showToast({
                  title: err.errMsg,
                  icon: 'none'
                });
              }
            });
          } else if (whiteImages.includes(fileTypeFromUrl)) {
            // 预览图片
            uni.previewImage({
              urls: [filePath],
              longPressActions: {
                itemList: ['发送给朋友', '保存图片', '收藏'],
                success: (data) => {
                },
                fail: (err) => {
                  console.error(err.errMsg);
                  uni.showToast({
                    title: err.errMsg,
                    icon: 'none'
                  });
                }
              }
            });
          } else if (whiteMedia.includes(fileTypeFromUrl)) {
            // 预览视频或音频
            this.videoUrl = filePath;
            this.showVideo = true;
            this.$nextTick(() => {
              this.videoContext = uni.createVideoContext("videoId", this);
              // 默认不进入全屏模式
              // this.videoContext.requestFullScreen({ direction: 0 });
            });
          } else {
            uni.showToast({
              title: '不支持打开该类型文件',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          this.isLoading = false;
          console.error("下载失败--->", err);
          uni.showToast({
            title: err.errMsg,
            icon: 'none'
          });
        }
      });
    },
    playVideo(e) {
      this.videoContext = uni.createVideoContext("videoId", this);
      // this.videoContext.requestFullScreen({ direction: 0 });
    },
    fullscreenchange(e) {
      if (e.detail.fullScreen) {
        this.controls = true;
        this.isFullScreen = true;
      } else {
        this.controls = true; // 确保在退出全屏时也显示控件
        this.isFullScreen = false;
      }
    },
    closeVideo() {
      this.showVideo = false;
      if (this.videoContext) {
        this.videoContext.exitFullScreen();
      }
      // 重置视频 URL 和上下文以确保下次点击时可以重新加载
      this.videoUrl = '';
      this.videoContext = null;
      // 通过事件通知父组件重置 file
      this.$emit('resetFile');
    }
  }
};
</script>

<style scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  position: relative;
  background: #fff;
  padding: 48rpx;
  border-radius: 20rpx;
  z-index: 1001;
}

.video-box {
  width: 100%;
  height: auto;
}

.close-btn {
  position: absolute;
  top: -4rpx;
  right: -14rpx;
  border: none;
  padding: 10rpx 20rpx;
  cursor: pointer;
  border-radius: 10rpx;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
</style>
