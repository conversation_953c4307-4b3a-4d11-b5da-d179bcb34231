<template>
  <view class="lk-select" :style="[innerCustomStyle]" @tap.stop="click">
    <view class="lk-select-input" :style="[innerInputStyle]">
      <view v-if="text" class="lk-select-input-text" :style="[innerTextStyle]">{{ text }}</view>
      <view v-else class="lk-select-input-text lk-select-input-placeholder" :style="[innerPlaceholderStyle]">
        {{ placeholder }}
      </view>
    </view>
    <u-icon v-if="showArrow" name="arrow-down" :size="arrowSize" :color="innerArrowColor"></u-icon>

    <view>
      <u-picker ref="picker" :show="showPicker" immediate-change :columns="pickerColumns" :keyName="listLabelName || labelName"
        @confirm="pickerConfirm" @cancel="showPicker = false">
      </u-picker>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'lk-select',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      // 当前选择
      value: {
        type: [String, Number, Object],
        default: '',
      },
      // 选择列表
      list: {
        type: Array,
        default: null,
      },
      // 列表中值字段名称
      valueName: {
        type: String,
        default: ''
      },
      // 列表中在输入框显示字段名称
      labelName: {
        type: String,
        default: ''
      },
      // 列表中在选择列表中显示字段名称
      listLabelName: {
        type: String,
        default: ''
      },
      // 占位文本
      placeholder: {
        type: String,
        default: '',
      },
      // 文本颜色
      color: {
        type: String,
        default: ''
      },
      // 文本字体大小
      fontSize: {
        type: String,
        default: ''
      },
      // 文本行高
      lineHeight: {
        type: String,
        default: ''
      },
      // 文本多行显示
      multipleLine: {
        type: Boolean,
        default: false
      },
      // 边框颜色
      borderColor: {
        type: String,
        default: ''
      },
      activeBorderColor: {
        type: String,
        default: ''
      },
      // 边框半径
      borderRadius: {
        type: String,
        default: ''
      },
      // 显示箭头
      showArrow: {
        type: Boolean,
        default: true
      },
      // 箭头颜色
      arrowColor: {
        type: String,
        default: '#333333'
      },
      activeArrowColor: {
        type: String,
        default: ''
      },
      // 箭头大小
      arrowSize: {
        type: String,
        default: '26rpx'
      },
      // 组件外部样式
      customStyle: {
        type: Object,
        default: null
      },
      // 文本样式
      textStyle: {
        type: Object,
        default: null
      },
      // 占位文本样式
      placeholderStyle: {
        type: Object,
        default: null
      },
      // 内嵌
      inline: {
        type: Boolean,
        default: false
      },
      // 内容拉伸
      stretch: {
        type: Boolean,
        default: true
      },
      // 内容对齐
      align: {
        type: String,
        default: 'left'
      },
      // 禁用内部picker
      pickerDisabled: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
        showPicker: false,
      }
    },
    computed: {
      innerBorderColor() {
        return this.text && this.activeBorderColor ? this.activeBorderColor : this.borderColor
      },
      innerArrowColor() {
        return this.text && this.activeArrowColor ? this.activeArrowColor : this.arrowColor
      },
      innerCustomStyle() {
        const style = {}
        if (this.customStyle) {
          Object.assign(style, this.customStyle)
        }
        if (this.innerBorderColor) {
          style.borderColor = this.innerBorderColor
        }
        if (this.borderRadius) {
          style.borderRadius = this.borderRadius
        }
        if (this.inline) {
          style.display = 'inline-flex'
        } else {
          style.display = 'flex'
          if (!this.stretch) {
            if (this.align == 'center') {
              style.display = 'flex'
              style.justifyContent = 'center'
            } else if (this.align == 'right') {
              style.display = 'flex'
              style.justifyContent = 'flex-end'
            }
          }
        }
        return style
      },
      innerInputStyle() {
        const style = {}
        if (this.align == 'center') {
          style.display = 'flex'
          style.justifyContent = 'center'
        } else if (this.align == 'right') {
          style.display = 'flex'
          style.justifyContent = 'flex-end'
        }
        if (this.stretch) {
          style.flex = '1'
        }
        return style
      },
      innerTextStyle() {
        const style = {}
        if (this.textStyle) {
          Object.assign(style, this.textStyle)
        }
        if (this.color) {
          style.color = this.color
        }
        if (this.fontSize) {
          style.fontSize = this.fontSize
        }
        if (this.lineHeight) {
          style.lineHeight = this.lineHeight
        }
        if (style.fontSize && !style.lineHeight) {
          style.lineHeight = style.fontSize
        }
        if (style.fontSize && !style.lineHeight) {
          style.lineHeight = '1.2em'
        }
        if (!this.multipleLine) {
          style.whiteSpace = 'nowrap'
        }
        return style
      },
      innerPlaceholderStyle() {
        const style = {}
        if (this.placeholderStyle) {
          Object.assign(style, this.placeholderStyle)
        }
        if (this.fontSize) {
          style.fontSize = this.fontSize
        }
        if (this.lineHeight) {
          style.lineHeight = this.lineHeight
        }
        if (style.fontSize && !style.lineHeight) {
          style.lineHeight = '1.2em'
        }
        style.whiteSpace = 'nowrap'
        return style
      },
      pickerList() {
        return this.list ?? []
      },
      pickerColumns() {
        return [this.pickerList]
      },
      text() {
        const item = this.pickerList.find(it => it[this.valueName] == this.value)
        return item && !item.labelIgnored ? item[this.labelName] : this.value ?? ''
      },
    },
    methods: {
      click() {
        if (this.pickerDisabled) {
          this.$emit('click')
        } else {
          this.openPicker()
        }
      },
      openPicker() {
        this.showPicker = true
        const index = this.pickerList.findIndex(it => it[this.valueName] == this.value)
        this.$refs?.picker?.setIndexs([index >= 0 ? index : 0], true)
      },
      pickerConfirm(e) {
        const value = e.value[0][this.valueName]
        this.$emit('input', value)
        this.$emit('change', value)
        this.showPicker = false
      },
    }
  }
</script>

<style lang="scss" scoped>
  .lk-select {
    display: flex;
    align-items: center;
    padding: 10rpx 16rpx;
    border: 1px solid #dadbde;
    border-radius: 8rpx;
    overflow: hidden;
    box-sizing: border-box;

    &-input {
      overflow: hidden;

      &-text {
        font-size: 26rpx;
        line-height: 36rpx;
        min-height: 1.4em;
        color: #333333;
        overflow: scroll;
      }

      &-placeholder {
        color: #AAAAAA;
      }

    }

    ::v-deep .u-icon {
      margin-left: 10rpx;
    }
  }
</style>
