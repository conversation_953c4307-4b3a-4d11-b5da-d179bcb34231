<template>
  <view class="lk-page-list" :style="[customStyle]">  
      <slot v-if="loading" name="loading">
      <lk-loading v-if="loading" :custom-style="loadingStyle" :center="!embed"></lk-loading>
    </slot>
    <lk-empty v-else-if="pageData.failed" text="请求失败，请点击屏幕重试" :center="!embed" @click="refresh(true)"></lk-empty>
    <lk-empty v-else-if="pageData.empty" :text="emptyText" :center="!embed"></lk-empty>
    <template v-else>
      <slot></slot>
      <u-loadmore v-if="realShowMoreStatus" :status="realMoreStatus"></u-loadmore>
    </template>

  </view>
</template>

<script>
  import createPageData from '@/common/utils/page-data.js'

  export default {
    name: 'lk-page-list',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    props: {
      embed: {
        type: Boolean,
        default: false,
      },
      customStyle: {
        type: Object,
        default: () => {}
      },
      autoStopPullDownRefresh: {
        type: Boolean,
        default: true
      },
      emptyText: {
        type: String,
        default: '暂无数据'
      },
      showMoreStatus: {
        type: Boolean,
        default: true
      },
      // 每次加载显示大小，可优化超大页数据加载转圈时间过久问题
      showPageSize: {
        type: Number,
        default: 0
      },
      loadingStyle: {
        type: Object,
        default: null
      }
    },
    data() {
      return {
        loading: false,
        pageData: {
        },
        showLength: 0,
      }
    },
	mounted() {
		this.$emit('mounted')
	},
    computed: {
      realMoreStatus() {
        if (this.pageData.moreStatus == 'nomore' &&
          this.showPageSize > 0 && this.showLength < this.pageData.list?.length) {
          return 'loading'
        }
        return this.pageData.moreStatus || 'none'
      },
      realShowMoreStatus() {
        return this.showMoreStatus && this.realMoreStatus != 'none'
      },
      showList() {
        const list = this.pageData.list
        if (!list) {
          return []
        }
        if (this.showPageSize > 0) {
          return list.slice(0, this.showLength)
        }
        return list
      }
    },
    watch: {
      showList(val) {
        const e = val.every((e, i) => e == this.pageData.list[i])
        this.$emit('showListChange', val)
      },
    },
    methods: {
      // 配置返回list，替换slot传参方案
      // 微信小程序slot传参实际是JSON.stringify()和JSON.parse()拷贝数据
      config(configs) {
        if (this.pageData.clear) {
          this.pageData.clear()
        }
        this.showLength = 0

        if (!configs) {
          this.pageData = {}
        } else {
          this.pageData = createPageData({
            ...configs,
            onLoadMoreFinish: () => {
              if (this.showPageSize > 0) {
                this.showLength = Math.min(this.pageData.list.length,
                  this.showLength + this.showPageSize)
              }
              configs.onLoadMoreFinish?.()
            },
            onRefreshFinish: () => {
              if (this.showPageSize > 0) {
                this.showLength = Math.min(this.pageData.list.length,
                  this.showLength || this.showPageSize)
              }
              configs.onRefreshFinish?.()
            },
            onRefreshFinally: () => {
              this.loading = false
              if (this.autoStopPullDownRefresh) {
                uni.stopPullDownRefresh()
              }
              configs.onRefreshFinally?.()
            }
          })
        }
        return this.pageData.list
      },
      // 刷新第一页数据
      // loading显示加载指示
      refresh(loading = false) {
        if (!this.pageData.refresh) {
          return false
        }
        this.loading = loading
        if (loading) {
          this.pageData.clear()
        }
        return this.pageData.refresh()
      },
      // 加载下一页数据
      loadMore() {
        if (!this.pageData.loadMore) {
          return
        }
        if (this.showPageSize > 0) {
          this.showLength = Math.min(this.pageData.list?.length || 0,
            this.showLength + this.showPageSize)
        }
        return this.pageData.loadMore()
      },
      // 更新指定项或索引所在页
      updatePageOf(item, index) {
        if (!this.pageData.pageOf) {
          return
        }
        const page = this.pageData.pageOf(item, index)
        this.pageData.loadMore(page)
      },
      clear() {
        if (!this.pageData.clear) {
          return
        }
        if (this.showPageSize > 0) {
          this.showLength = 0
        }
        this.pageData.clear()
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>