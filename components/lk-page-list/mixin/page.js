/*

<template>
  <view>
    ...
    <lk-page-list ref="pageList">
      <view v-for="item in pageList">{{ item }}</view>
    </lk-page-list>
    ...
  </view>
</template>

export default {
  ...
  computed: {
    configConfigs() {
      return {
        request: api.page
      }
    }
  }
  ...
}

*/
export default {
  data() {
    return {
      pageList: [],
    }
  },
  computed: {
    pageListConfigs() {
      return null
    }
  },
  watch: {
    pageListConfigs() {
      this.configPageList()
    }
  },
  onPullDownRefresh() {
    this.$refs.pageList?.refresh()
  },
  onReachBottom() {
    this.$refs.pageList?.loadMore()
  },
  mounted() {
    this.configPageList()
  },
  methods: {
    configPageList() {
      if (!this.$refs.pageList?.config) {
        return false
      }
      const configs = this.pageListConfigs
      if (!configs) {
        this.$refs.pageList.clear()
        return false
      }
      this.pageList = this.$refs.pageList.config(configs)
      this.$refs.pageList.refresh(true)
      return true
    },
    onPageListItemChange(item) {
      this.$refs.pageList?.updatePageOf(item)
    },
    updateFirstPage() {
      this.$refs.pageList?.updatePageOf(null, 0)
    },
    updateLastPage() {
      this.$refs.pageList?.updatePageOf(null, Math.max(0, this.pageList.length - 1))
    },
  }
}