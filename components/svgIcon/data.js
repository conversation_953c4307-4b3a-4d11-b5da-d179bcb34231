const iconDatas = {
  fileTxt: () => import('./icons/file/txt.svg'),
  filePdf: () => import('./icons/file/pdf.svg'),
  fileDoc: () => import('./icons/file/doc.svg'),
  fileXlsx: () => import('./icons/file/xlsx.svg'),
  filePpt: () => import('./icons/file/ppt.svg'),
  fileMd: () => import('./icons/file/md.svg'),
  fileBlank: () => import('./icons/file/blank.svg'),
  fileError: () => import('./icons/file/error.svg'),
  file2Txt: () => import('./icons/file2/txt.svg'),
  file2Pdf: () => import('./icons/file2/pdf.svg'),
  file2Doc: () => import('./icons/file2/doc.svg'),
  file2Xlsx: () => import('./icons/file2/xlsx.svg'),
  file2Ppt: () => import('./icons/file2/ppt.svg'),
  file2Mp3: () => import('./icons/file2/mp3.svg'),
  file2Mp4: () => import('./icons/file2/mp4.svg'),
  file2Zip: () => import('./icons/file2/zip.svg'),
  file2Avi: () => import('./icons/file2/avi.svg'),
  file2Csv: () => import('./icons/file2/csv.svg'),
  file2Md: () => import('./icons/file2/md.svg'),
  file2Rar: () => import('./icons/file2/rar.svg'),
  file2Wav: () => import('./icons/file2/wav.svg'),
  file2Folder: () => import('./icons/file2/folder.svg'),
  file2Unknown: () => import('./icons/file2/unknown.svg'),
  file2Eye: () => import('./icons/file2/file_eye.svg'),
  file2Preview: () => import('./icons/file2/file_preview.svg'),
  file2Label: () => import('./icons/file2/label.svg'),
  file2Dynamic: () => import('./icons/file2/file_dynamic.svg'),
  file2Info: () => import('./icons/file2/file_info.svg'),
  file2Query: () => import('./icons/file2/query.svg'),
  file2Look: () => import('./icons/file2/look.svg'),
  file2Examine: () => import('./icons/file2/examine.svg'),
};

const loadIcons = async () => {
  const loadedIcons = {};
  for (const [key, value] of Object.entries(iconDatas)) {
     loadedIcons[key] = (await value()).default;
  }
  return loadedIcons;
};

export default loadIcons();