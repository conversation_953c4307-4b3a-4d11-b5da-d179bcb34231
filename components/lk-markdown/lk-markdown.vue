<template>
  <mp-html :content="htmlContent"></mp-html>
</template>

<script>
  import marked from '@/common/utils/marked.min.js'
  import mpMixin from '@/common/mixin/mp.js'

  export default {
    name: 'lk-markdown',
    mixins: [mpMixin],
    props: {
      content: {
        type: String,
        default: ''
      }
    },
    computed: {
      htmlContent() {
        return this.content ? marked(this.content) : ''
      }
    }
  }
</script>

<style>
</style>