<template>
  <u-popup 
    v-model="show" 
    mode="bottom" 
    height="70%" 
    border-radius="20"
    @close="handleClose"
  >
    <view class="instrument-picker">
      <!-- 弹窗头部 -->
      <view class="picker-header">
        <view class="header-title">选择仪器</view>
        <view class="header-close" @tap="handleClose">
          <u-icon name="close" size="20" color="#999"></u-icon>
        </view>
      </view>

      <!-- 分类标签 -->
      <view class="category-tabs" v-if="categories.length > 0">
        <scroll-view scroll-x class="category-scroll">
          <view class="category-list">
            <view 
              class="category-item"
              :class="{ active: activeCategory === index }"
              v-for="(category, index) in categories" 
              :key="category.id"
              @tap="selectCategory(index)"
            >
              {{ category.name }}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 仪器列表 -->
      <view class="instrument-content">
        <scroll-view 
          scroll-y 
          class="instrument-scroll"
          @scrolltolower="loadMore"
          :refresher-enabled="true"
          :refresher-triggered="refreshing"
          @refresherrefresh="onRefresh"
        >
          <view class="instrument-list">
            <view 
              class="instrument-item"
              v-for="instrument in instrumentList" 
              :key="instrument.id"
              @tap="selectInstrument(instrument)"
            >
              <view class="instrument-info">
                <view class="instrument-name">{{ instrument.name }}</view>
                <view class="instrument-desc" v-if="instrument.description">
                  {{ instrument.description }}
                </view>
                <view class="instrument-meta">
                  <text class="meta-item" v-if="instrument.location">
                    📍 {{ instrument.location }}
                  </text>
                  <text class="meta-item" v-if="instrument.status">
                    {{ getStatusText(instrument.status) }}
                  </text>
                </view>
              </view>
              <view class="instrument-action">
                <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
              </view>
            </view>
          </view>

          <!-- 加载状态 -->
          <view class="loading-more" v-if="loading">
            <u-loading-icon mode="flower"></u-loading-icon>
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 没有更多数据 -->
          <view class="no-more" v-if="!hasMore && instrumentList.length > 0">
            <text>没有更多数据了</text>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="!loading && instrumentList.length === 0">
            <text class="empty-text">暂无仪器数据</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import instrumentApi from '@/api/instrument';

export default {
  name: 'InstrumentPicker',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      categories: [],
      activeCategory: 0,
      instrumentList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 20,
      currentCategoryId: null
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.show = newVal;
        if (newVal) {
          this.initData();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.fetchCategories();
      if (this.categories.length > 0) {
        this.selectCategory(0);
      }
    },

    // 获取分类列表
    async fetchCategories() {
      try {
        const data = await instrumentApi.getInstrumentCategoryList();
        this.categories = data || [];
        console.log('获取到仪器分类:', this.categories);
      } catch (error) {
        console.error('获取仪器分类失败:', error);
        uni.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      }
    },

    // 选择分类
    selectCategory(index) {
      this.activeCategory = index;
      this.currentCategoryId = this.categories[index]?.id;
      this.resetList();
      this.fetchInstruments();
    },

    // 重置列表
    resetList() {
      this.instrumentList = [];
      this.currentPage = 1;
      this.hasMore = true;
    },

    // 获取仪器列表
    async fetchInstruments(isLoadMore = false) {
      if (this.loading) return;
      
      this.loading = true;
      
      try {
        const params = {
          current: this.currentPage,
          size: this.pageSize
        };
        
        if (this.currentCategoryId) {
          params.categoryId = this.currentCategoryId;
        }

        const result = await instrumentApi.getInstrumentPage(params);
        
        if (isLoadMore) {
          this.instrumentList = [...this.instrumentList, ...result.records];
        } else {
          this.instrumentList = result.records || [];
        }
        
        this.hasMore = this.instrumentList.length < result.total;
        
        console.log('获取到仪器列表:', result);
      } catch (error) {
        console.error('获取仪器列表失败:', error);
        uni.showToast({
          title: '获取仪器失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return;
      
      this.currentPage++;
      this.fetchInstruments(true);
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.resetList();
      this.fetchInstruments();
    },

    // 选择仪器
    selectInstrument(instrument) {
      this.$emit('select', instrument);
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('input', false);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '正常',
        2: '维护中',
        3: '故障',
        4: '停用'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style lang="scss" scoped>
.instrument-picker {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.header-close {
  padding: 10rpx;
}

.category-tabs {
  border-bottom: 1px solid #f0f0f0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 20rpx 30rpx;
}

.category-item {
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  white-space: nowrap;
  
  &.active {
    background-color: #26d1cb;
    color: #fff;
  }
}

.instrument-content {
  flex: 1;
  overflow: hidden;
}

.instrument-scroll {
  height: 100%;
}

.instrument-list {
  padding: 0 30rpx;
}

.instrument-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.instrument-info {
  flex: 1;
}

.instrument-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.instrument-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.instrument-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
  margin-bottom: 4rpx;
}

.instrument-action {
  padding: 10rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}

.loading-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
