<template>
  <view class="header-weekdays">
    <text v-for="(weekday, index) in weekdays" class="header-weekdays-weekday" :style="[innerStyle]"
      :key="index">{{ weekday }}</text>
  </view>
</template>

<script>
  import props from './props.js'

  export default {
    name: 'lk-calendar-header',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif    
    mixins: [props],
    props: {
      startWeekday: {
        type: Number,
        default: 0,
      }
    },
    computed: {
      weekdays() {
        let weekdays = ['日', '一', '二', '三', '四', '五', '六']
        if (this.startWeekday <= 0 || this.startWeekday >= weekdays.length) {
          return weekdays
        }
        return weekdays.concat(weekdays.splice(0, this.startWeekday))
      },
      innerStyle() {
        const style = {}
        if (this.weekColor) {
          style.color = this.weekColor
        }
        return style
      }
    }
  }
</script>

<style lang="scss" scoped>
  .header-weekdays {
    display: flex;
    flex-direction: row;
    align-items: center;

    &-weekday {
      text-align: center;
      height: 42px;
      line-height: 42px;
      flex: 1;
      font-size: 28rpx;
    }
  }
</style>
