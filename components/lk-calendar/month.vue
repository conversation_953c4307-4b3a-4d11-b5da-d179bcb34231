<template>
  <view class="box">
    <view class="month-box" :animation="monthBoxAnimation" ref="animation">

      <view class="month" :animation="monthAnimation" @touchstart="monthTouchStart" @touchmove="monthTouchMove"
        @touchend="monthTouchEnd">
        <!-- 当前显示日期 -->
        <view class="month-week" v-for="(week, index) in weeks" :key="index">
          <view class="month-date" v-for="date in week" :key="date.date" @tap.stop="clickDate(date)">
            <view class="month-date-text" :class="{
  	      'month-date-text-checked': date.date == currentDate && !date.disabled,
  	      'month-date-text-disabled': date.disabled,
          }" :style="[getDateStyle(date)]">
              {{ date.day }}
            </view>
            <view class="indicator-wrap">
              <view class="month-date-indicator" :style="[indicatorStyles && indicatorStyles[date.date]]"></view>
              <view v-if="indicatorStyles2 && indicatorStyles2[date.date]" class="month-date-indicator second" :style="[indicatorStyles2 && indicatorStyles2[date.date]]"></view>
            </view>
          </view>
        </view>

        <view v-if="realShowMonth" class="month-number" :style="[monthNumberStyle]">{{ monthNumber }}</view>

        <!-- 前一月或一周 -->
        <view v-if="swipeable && prevWeeks.length" class="month-swipe-prev" :style="[swipeWrapStyle]">

          <view v-if="realShowMonth" class="month-number" :style="[monthNumberStyle]">{{ prevMonthNumber }}</view>

          <view class="month-swipe-week" v-for="(week, index) in prevWeeks" :key="index">
            <view class="month-date" v-for="date in week" :key="date.date">
              <view class="month-date-text" :class="{
          'month-date-text-checked': date.date == currentDate && !date.disabled,
          'month-date-text-disabled': date.disabled,
          }" :style="[getDateStyle(date)]">
                {{ date.day }}
              </view>
            <view class="indicator-wrap">
              <view class="month-date-indicator" :style="[indicatorStyles && indicatorStyles[date.date]]"></view>
              <view v-if="indicatorStyles2 && indicatorStyles2[date.date]" class="month-date-indicator second" :style="[indicatorStyles2 && indicatorStyles2[date.date]]"></view>
            </view>            </view>
          </view>
        </view>

        <!-- 后一月或一周 -->
        <view v-if="swipeable && nextWeeks.length" class="month-swipe-next" :style="[swipeWrapStyle]">

          <view v-if="realShowMonth" class="month-number" :style="[monthNumberStyle]">{{ nextMonthNumber }}</view>

          <view class="month-swipe-week" v-for="(week, index) in nextWeeks" :key="index">
            <view class="month-date" v-for="date in week" :key="date.date">
              <view class="month-date-text" :class="{
          'month-date-text-checked': date.date == currentDate && !date.disabled,
          'month-date-text-disabled': date.disabled,
          }" :style="[getDateStyle(date)]">
                {{ date.day }}
              </view>
            <view class="indicator-wrap">
              <view class="month-date-indicator" :style="[indicatorStyles && indicatorStyles[date.date]]"></view>
              <view v-if="indicatorStyles2 && indicatorStyles2[date.date]" class="month-date-indicator second" :style="[indicatorStyles2 && indicatorStyles2[date.date]]"></view>
            </view>
            </view>
          </view>
        </view>

      </view>
    </view>

    <view class="footer" :style="[footerStyle]">
      <view class="footer-slider-box" @touchstart="sliderTouchStart" @touchmove="sliderTouchMove"
        @touchend="sliderTouchEnd">
        <slot name="slider">
				<view class="icon" @tap="handleExpand">
					
					<u-icon v-if="innerCollapsed" name="arrow-down"  size="30rpx"></u-icon>
					<u-icon v-if="!innerCollapsed" name="arrow-up"  size="30rpx"></u-icon>
					
				</view>
          <!-- <view class="footer-slider" :style="[footerSliderStyle]"></view> -->
        </slot>
      </view>
    </view>
  </view>

</template>

<script>
  import props from './props.js'
  import dayjs from '@/common/utils/day.js'

  const animationDuration = 200
  const swipeThreshold = 10
  const slideThreshold = 10

  export default {
    name: 'lk-calendar-month',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif    
    mixins: [props],
    data() {
      return {
        currentMonth: '', // 当前显示月
        currentWeekDate: '', // 当前显示周且属于当月的第一个日期（折叠时使用）
        currentDate: '', // 当前选中日期

        prevMonth: '',
        nextMonth: '',

        calendarDates: [], // 日历显示日期
        prevCalendarDates: [], // 前一月日历日期
        nextCalendarDates: [], // 下一月日历日期

        touchStartX: 0, // 触屏起始点x  
        touchStartY: 0, // 触屏起始点y,
        swiping: false, // 
        sliding: false, // 

        monthWidth: 0, // 月份显示宽度
        monthHeight: 0, // 展开月份高度
        weekHeight: 0, // 一周日期高度
        monthBoxAnimation: {},
        monthAnimation: {},
        monthAnimationId: 0,

        innerCollapsed: false, // 是否折叠

        emitedShowDates: null,
        isSelectFocusDateBySwipe: false,
      }
    },
    computed: {
      weeks() {
        if (this.innerCollapsed && this.monthHeight == 0) {
          // 折叠且未计算出高度时只渲染一周日期
          const index = this.calendarDates.findIndex(it => it.date == this.currentWeekDate)
          const start = index > 0 ? index - index % 7 : 0
          return [this.calendarDates.slice(start, start + 7)]
        } else {
          const weeks = []
          for (let i = 0; i < this.calendarDates.length; i += 7) {
            weeks.push(this.calendarDates.slice(i, i + 7))
          }
          return weeks
        }
      },
      prevWeeks() {
        if (this.innerCollapsed) {
          // 上一周日期
          const date = dayjs(this.currentWeekDate).subtract(1, 'day').format('YYYY-MM-DD')
          const index = this.calendarDates.findIndex(it => it.date == date)
          if (index < 0) {
            return [this.prevCalendarDates.slice(-7)]
          }
          const start = index - index % 7
          return [this.calendarDates.slice(start, start + 7)]
        } else {
          // 上一月日期
          const weeks = []
          for (let i = 0; i < this.prevCalendarDates.length; i += 7) {
            weeks.push(this.prevCalendarDates.slice(i, i + 7))
          }
          return weeks
        }
      },
      nextWeeks() {
        if (this.innerCollapsed) {
          // 下一周日期
          const date = dayjs(this.currentWeekDate).add(7, 'day').format('YYYY-MM-DD')
          const index = this.calendarDates.findIndex(it => it.date == date)
          if (index < 0) {
            return [this.nextCalendarDates.slice(0, 7)]
          }
          const start = index - index % 7
          return [this.calendarDates.slice(start, start + 7)]
        } else {
          // 下一月日期
          const weeks = []
          for (let i = 0; i < this.nextCalendarDates.length; i += 7) {
            weeks.push(this.nextCalendarDates.slice(i, i + 7))
          }
          return weeks
        }
      },
      showDates() {
        if (this.innerCollapsed) {
          const index = this.calendarDates.findIndex(it => it.date == this.currentWeekDate)
          if (index < 0) {
            return []
          }
          const start = index - index % 7
          return this.calendarDates.slice(start, start + 7).map(it => {
            return {
              ...it
            }
          })
        } else {
          return this.calendarDates.map(it => {
            return {
              ...it
            }
          })
        }
      },
      swipeWrapStyle() {
        const style = {}
        style.top = this.innerCollapsed ? this.getWeekTop(this.currentWeekDate) + 'px' : 0
        return style
      },
      monthNumber() {
        return this.currentMonth.slice(-2)
      },
      prevMonthNumber() {
        return this.prevMonth.slice(-2)
      },
      nextMonthNumber() {
        return this.nextMonth.slice(-2)
      },
      realShowMonth() {
        return this.showMonth && (!this.innerCollapsed || this.sliding)
      },
      monthNumberStyle() {
        const style = {
          width: this.monthWidth + 'px',
          height: this.monthHeight + 'px',
          fontSize: this.monthWidth * 0.3 + 'px',
        }
        if (this.monthColor) {
          style.color = this.monthColor
        }
        return style
      }
    },
    watch: {
      date: {
        immediate: true,
        handler(val) {
          this.setCurrentDate(val)
        }
      },
      collapsed: {
        immediate: true,
        handler(val) {
          if (val) {
            this.collapse()
          } else {
            this.expand()
          }
        }
      },
      month: {
        immediate: true,
        handler(val) {
          if (this.setCurrentMonth(this.month || this.date || dayjs())) {
            this.setCurrentWeekDate(this.calendarDates.some(it => !it.disabled && it.date == this.currentDate) ?
              this.currentDate : this.currentMonth + '-01')
            this.isSelectFocusDateBySwipe = false
            this.selectFocusDate()
          }
        }
      },
      focusDates(val) {
        this.selectFocusDate()
      },
      showDates(val) {
        this.$nextTick(() => {
          this.emitShowDatesChange()
        })
      }
    },
    created() {
      this.$nextTick(() => {
        uni.createSelectorQuery()
          .in(this)
          .select('.month-week')
          .boundingClientRect(data => {
            this.monthWidth = data.width
            this.monthHeight = data.height * 6
            this.weekHeight = data.height
            if (this.innerCollapsed) {
              this.animateMonthToHeight(this.weekHeight)
            }
          }).exec()
      })
      setTimeout(() => {
        console.log(this.indicatorStyles2);
      }, 1000);
    },
    methods: {
      getDateStyle(date) {
        const style = {}
        if (date.disabled) {
          if (this.disabledDateColor) {
            style.color = this.disabledDateColor
          }
        } else if (date.date == this.currentDate) {
          if (this.checkedDateColor) {
            style.color = this.checkedDateColor
          }
          if (this.checkedDateBackgroundColor) {
            style.backgroundColor = this.checkedDateBackgroundColor
          }
        } else if (this.dateColor) {
          style.color = this.dateColor
        }
        return style
      },
      getMonthDates(date) {
        const dateObj = dayjs(date)
        const dates = []
        const daysInMonth = dateObj.daysInMonth()
        const month = dateObj.format('YYYY-MM')
        for (let day = 1; day <= daysInMonth; day++) {
          const djs = dateObj.date(day)
          dates.push({
            day: day,
            date: djs.format('YYYY-MM-DD'),
            week: djs.day(),
            month: month,
          })
        }
        return dates
      },
      getCalendarDates(date) {
        const dateObj = dayjs(date)
        const month = dateObj.format('YYYY-MM')
        const prevDates = this.getMonthDates(dateObj.subtract(1, 'month'))
        const currentDates = this.getMonthDates(month)
        const nextDates = this.getMonthDates(dateObj.add(1, 'month'))
        // 上月显示日期
        const beforeDates = currentDates[0].week > 0 ? prevDates.splice(-currentDates[0].week) : []
        // 下月显示日期
        const afterDay = (beforeDates.length + currentDates.length <= 35 ? 13 : 6) -
          currentDates[currentDates.length - 1].week
        const afterDates = afterDay > 0 ? nextDates.splice(0, afterDay) : []

        beforeDates.forEach(it => it.disabled = true)
        currentDates.forEach(it => it.disabled = false)
        afterDates.forEach(it => it.disabled = true)
        // 合并所有日期
        return [...beforeDates, ...currentDates, ...afterDates]
      },
      setCurrentMonth(date) {
        const dateObj = dayjs(date)
        const month = dateObj.format('YYYY-MM')
        if (month == this.currentMonth) {
          return false
        }
        const prevMonthObj = dateObj.subtract(1, 'month')
        const nextMonthObj = dateObj.add(1, 'month')

        this.currentMonth = month
        this.prevMonth = prevMonthObj.format('YYYY-MM')
        this.nextMonth = nextMonthObj.format('YYYY-MM')

        this.calendarDates = this.getCalendarDates(dateObj)
        this.prevCalendarDates = this.getCalendarDates(prevMonthObj)
        this.nextCalendarDates = this.getCalendarDates(nextMonthObj)
        return true
      },
      setCurrentWeekDate(date) {
        const dateObj = dayjs(date)
        // 取周日作为一周的日期
        const weekDate = dateObj.subtract(Math.min(dateObj.day(), dateObj.date() - 1), 'day').format('YYYY-MM-DD')
        if (weekDate == this.currentWeekDate) {
          return
        }
        const translate = this.innerCollapsed &&
          this.getWeekTop(weekDate) != this.getWeekTop(this.currentWeekDate)
        this.currentWeekDate = weekDate
        if (translate) {
          this.translateCollapsedWeek()
        }
      },
      setCurrentDate(date) {
        if (!date || typeof date == 'string' && date?.length != 10) {
          if (this.currentDate) {
            this.currentDate = ''
            return true
          }
          return false
        }
        const dateObj = dayjs(date)
        const newDate = dateObj.format('YYYY-MM-DD')
        if (newDate == this.currentDate) {
          return false
        }
        this.currentDate = newDate
        // 选中日期变化同步月份
        this.setCurrentMonth(dateObj)
        this.setCurrentWeekDate(dateObj)
        return true
      },
      getWeekTop(date) {
        date = dayjs(date).format('YYYY-MM-DD')
        const index = this.calendarDates.findIndex(it => it.date == date)
        return index < 0 ? -1 : Math.floor(index / 7) * this.weekHeight
      },
      animateMonthToHeight(height, duration = 0) {
        if (!this.monthHeight) {
          return
        }
        const animation = uni.createAnimation({
          timingFunction: duration > 0 ? 'ease-in-out' : 'step-start',
          duration: duration,
        })
        animation.height(height).step()
        this.monthBoxAnimation = animation.export()
        this.animateWeekAdjustHeight(height, duration)
      },
      animateWeekAdjustHeight(height, duration) {
        if (this.monthHeight == 0) {
          return
        }
        const animationId = ++this.monthAnimationId
        const animation = uni.createAnimation({
          timingFunction: duration > 0 ? 'ease-in-out' : 'step-start',
          duration: duration,
        })
        const factor = 1 - (height - this.weekHeight) / (this.monthHeight - this.weekHeight)
        const translateY = -this.getWeekTop(this.currentWeekDate) * factor
        animation.translateY(translateY).step()
        this.monthAnimation = animation.export()
        if (duration > 0) {
          // 动画结束后避免切换后动画
          setTimeout(() => {
            if (animationId == this.monthAnimationId) {
              this.animateWeekAdjustHeight(height, 0)
            }
          }, duration)
        }
      },
      animateMonthToX(x, duration = 0, callback = null) {
        const animation = uni.createAnimation({
          timingFunction: duration > 0 ? 'ease-in-out' : 'step-start',
          duration: duration,
        })
        if (this.innerCollapsed) {
          animation.translateY(-this.getWeekTop(this.currentWeekDate))
        }
        animation.translateX(x).step()
        this.monthAnimation = animation.export()
        if (duration > 0) {
          // 动画结束后避免切换后动画
          setTimeout(() => {
            callback?.()
            this.animateMonthToX(0)
          }, duration)
        } else {
          callback?.()
        }
      },
      monthTouchStart(e) {
        if (!this.swipeable) {
          return
        }
        this.touchStartX = e.touches[0].clientX
        this.touchStartY = e.touches[0].clientY
        this.swiping = false
      },
      monthTouchMove(e) {
        if (!this.swipeable) {
          return
        }
        const deltaX = e.changedTouches[0].clientX - this.touchStartX
        const deltaY = e.changedTouches[0].clientY - this.touchStartY
        if (!this.swiping && Math.abs(deltaX) >= swipeThreshold && Math.abs(deltaY) < Math.abs(deltaX)) {
          this.swiping = true
        }
        if (!this.swiping) {
          return
        }
        this.animateMonthToX(deltaX, 0)
      },
      monthTouchEnd(e) {
        if (!this.swiping) {
          return
        }
        this.swiping = false
        const deltaX = e.changedTouches[0].clientX - this.touchStartX
        const threshold = this.monthWidth / 7
        if (deltaX >= threshold) {
          // 右滑
          this.animateMonthToX(this.monthWidth, animationDuration, () => {
            let monthChanged = false
            if (this.innerCollapsed) {
              // 切换上一周
              this.setCurrentWeekDate(dayjs(this.currentWeekDate).subtract(1, 'day'))
              monthChanged = this.setCurrentMonth(this.currentWeekDate)
            } else {
              // 切换上一月
              this.setCurrentMonth(dayjs(this.currentMonth).subtract(1, 'month'))
              monthChanged = true
            }
            if (monthChanged) {
              this.$emit('monthChange', this.currentMonth)
            }
            this.emitShowDatesChange()
            this.$emit('swipeChange', {
              to: 'prev',
              collapsed: this.innerCollapsed,
              monthChanged: monthChanged,
              currentMonth: this.currentMonth,
              showDates: this.showDates
            })
            this.isSelectFocusDateBySwipe = true
            this.selectFocusDate()
          })
        } else if (deltaX <= -threshold) {
          // 左滑
          this.animateMonthToX(-this.monthWidth, animationDuration, () => {
            let monthChanged = false
            if (this.innerCollapsed) {
              // 切换下一周
              const dateObj = dayjs(this.currentWeekDate)
              this.setCurrentWeekDate(dateObj.add(Math.min(7, dateObj.daysInMonth() - dateObj.date() + 1), 'day'))
              monthChanged = this.setCurrentMonth(this.currentWeekDate)
            } else {
              // 切换下一月
              this.setCurrentMonth(dayjs(this.currentMonth).add(1, 'month'))
              monthChanged = true
            }
            if (monthChanged) {
              this.$emit('monthChange', this.currentMonth)
            }
            this.emitShowDatesChange()
            this.$emit('swipeChange', {
              to: 'next',
              collapsed: this.innerCollapsed,
              monthChanged: monthChanged,
              currentMonth: this.currentMonth,
              showDates: this.showDates
            })
            this.isSelectFocusDateBySwipe = true
            this.selectFocusDate()
          })
        } else {
          // 复原
          this.animateMonthToX(0, animationDuration)
        }
      },
      translateCollapsedWeek() {
        if (this.innerCollapsed) {
          this.animateWeekAdjustHeight(this.weekHeight, 0)
        }
      },
      collapse() {
        this.setInnerCollapsed(true)
        this.animateMonthToHeight(this.weekHeight, animationDuration)
      },
      expand() {
        this.setInnerCollapsed(false)
        this.animateMonthToHeight(this.monthHeight, animationDuration)
      },
      setInnerCollapsed(collapsed) {
        if (collapsed == this.innerCollapsed) {
          return
        }
        this.innerCollapsed = collapsed
        if (this.calendarDates.some(it => !it.disabled && it.date == this.currentDate)) {
          this.setCurrentWeekDate(this.currentDate)
        } else if (this.currentMonth) {
          this.setCurrentWeekDate(this.currentMonth + '-01')
        }
      },
      sliderTouchStart(e) {
        this.touchStartX = e.touches[0].clientX
        this.touchStartY = e.touches[0].clientY
        this.sliding = false
      },
      sliderTouchMove(e) {
        const deltaX = e.changedTouches[0].clientX - this.touchStartX
        const deltaY = e.changedTouches[0].clientY - this.touchStartY
        if (!this.sliding && Math.abs(deltaY) >= slideThreshold && Math.abs(deltaX) < Math.abs(deltaY)) {
          this.sliding = true
        }
        if (!this.sliding) {
          return
        }
        let height = (this.innerCollapsed ? this.weekHeight : this.monthHeight) + deltaY
        if (height < this.weekHeight) {
          height = this.weekHeight
        } else if (height > this.monthHeight) {
          height = this.monthHeight
        }
        this.animateMonthToHeight(height, 0)
      },
      sliderTouchEnd(e) {
        if (!this.sliding) {
          return
        }
        this.sliding = false
        const deltaY = e.changedTouches[0].clientY - this.touchStartY
        const threshold = this.monthHeight / 12
        if ((this.innerCollapsed && deltaY >= threshold) ||
          (!this.innerCollapsed && deltaY > -threshold)) {
          this.expand()
        } else {
          this.collapse()
        }
        this.$emit('collapseChange', this.innerCollapsed)
      },
      clickDate(date) {
        if (date.disabled || !this.showDates.some(it => it.date == date.date)) {
          return
        }
        this.$emit('dateClick', date.date)
        if (date.date != this.currentDate) {
          this.setCurrentDate(date.date)
          this.$emit('dateChange', date.date)
        }
      },
      emitShowDatesChange() {
        if (this.showDates == this.emitedShowDates) {
          return
        }
        this.emitedShowDates = this.showDates
        this.$emit('showDatesChange', this.showDates)
      },
      selectFocusDate() {
        if (!this.focusDates) {
          return
        }
        if (this.showDates.some(it => !it.disabled && it.date == this.currentDate)) {
          return
        }
        let dateChanged = false
        if (this.innerCollapsed && this.isSelectFocusDateBySwipe) {
          const dateObj = dayjs()
          const dateStr = dateObj.format('YYYY-MM-DD')
          if (this.currentMonth == dateObj.format('YYYY-MM') &&
            this.showDates.some(it => !it.disabled && it.date == dateStr)) {
            dateChanged = this.setCurrentDate(dateObj)
          } else {
            const date = this.showDates.find(it => !it.disabled && this.focusDates.includes(it.date))
            dateChanged = this.setCurrentDate(date?.date || '')
          }
        } else {
          // 在日历日期中查找
          const dateObj = dayjs()
          if (this.currentMonth == dateObj.format('YYYY-MM')) {
            // 当月优先选择今天
            dateChanged = this.setCurrentDate(dateObj)
          } else {
            const date = this.calendarDates.find(it => !it.disabled && this.focusDates.includes(it.date))
            dateChanged = this.setCurrentDate(date?.date || '')
          }
        }
        if (dateChanged) {
          this.$emit('dateChange', this.currentDate)
        }
      },
	  handleExpand(){
		  if(!this.innerCollapsed){
			this.collapse()
		} else {
			  this.expand()
		  }
		  this.$emit('collapseChange', this.innerCollapsed)
	  }
    }
  }
</script>

<style lang="scss" scoped>
  .box {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .month-box {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    overflow: hidden;
  }

  .month {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;

    &-swipe-prev {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
    }

    &-swipe-next {
      position: absolute;
      top: 0;
      left: 100%;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
    }

    &-swipe-week {
      display: flex;
      align-items: stretch;
    }

    &-number {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 0;
      color: #00000010;
    }

    &-week {
      display: flex;
      align-items: stretch;
      z-index: 1;
    }

    &-date {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10rpx 0 20rpx 0;
      position: relative;
      width: calc(100% / 7);

      &-text {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
		font-weight: 600;
        max-width: 60rpx;
        max-height: 60rpx;
        min-height: 60rpx;
        border-radius: 16rpx;
        font-size: 32rpx;

        &-disabled {
          color: #999;
        }

        &-checked {
			background: linear-gradient( 314deg, #1CBE83 0%, #8DEDC8 100%);
			box-shadow: 0rpx 6rpx 7rpx 0rpx rgba(124,160,255,0.25);
			border-radius: 44rpx 44rpx 44rpx 44rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 48rpx;
			text-align: center;
			font-style: normal;
        }

      }
      .indicator-wrap{
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        bottom: 0rpx;
        width: 100%;
        .second{
          margin-left: 14rpx;
        }
      }

      &-indicator {
        $size: 10rpx;
        width: $size;
        height: $size;
        border-radius: 50%;
        display: block;
        margin-top: 10rpx;
        bottom: 0rpx;
      }

    }

  }
	.footer-slider-box{
		.icon{
			display: inline-flex;
		}
		display: flex;
		justify-content: center;
		align-items: center;
	}
  .footer {
    font-size: 48rpx;
    height: 60rpx;
    text-align: center;
    line-height: 28rpx;

    &-slider-box {
      display: inline-block;
      min-width: 180rpx;
      min-height: 60rpx;
    }

    &-slider {
      display: inline-block;
      width: 60rpx;
      height: 14rpx;
      background: #DCDCDC;
      border-radius: 6rpx;
    }

  }
</style>