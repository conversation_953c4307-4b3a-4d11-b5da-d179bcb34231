export default {
  props: {
    // 当前选中日期
    date: {
      type: [String, Number],
      default: '',
    },
    // 当前显示月份
    month: {
      type: [String],
      default: ''
    },
    // 是否折叠显示单周日期
    collapsed: {
      type: Boolean,
      default: false
    },
    // 可左右滑动切换月份、单周日期
    swipeable: {
      type: Boolean,
      default: false,
    },
    weekColor: {
      type: String,
      default: '',
    },
    dateColor: {
      type: String,
      default: ''
    },
    checkedDateColor: {
      type: String,
      default: ''
    },
    checkedDateBackgroundColor: {
      type: String,
      default: ''
    },
    disabledDateColor: {
      type: String,
      default: ''
    },
    showMonth: {
      type: Boolean,
      default: true
    },
    monthColor: {
      type: String,
      default: ''
    },
    showIndicator: {
      type: Boolean,
      default: true,
    },
    indicatorStyles: {
      type: Object,
      default: null,
    },
    
    indicatorStyles2: {
      type: Object,
      default: null,
    },
    footerStyle: {
      type: Object,
      default: null
    },
    footerSliderStyle: {
      type: Object,
      default: null
    },
    focusDates: {
      type: [Array, Object],
      default: null
    }
  }
}