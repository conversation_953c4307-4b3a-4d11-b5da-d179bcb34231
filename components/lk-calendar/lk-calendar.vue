<template>
  <view class="calendar">
    <lkHeader :weekColor="weekColor"></lkHeader>
    <lkMonth ref="month" :date="date" :month="month" :focusDates="focusDates" :collapsed="collapsed"
      :swipeable="swipeable" :dateColor="dateColor" :checkedDateColor="checkedDateColor"
      :checkedDateBackgroundColor="checkedDateBackgroundColor" :disabledDateColor="disabledDateColor"
      :showMonth="showMonth" :monthColor="monthColor" :showIndicator="showIndicator" :indicatorStyles2="indicatorStyles2"  :indicatorStyles="indicatorStyles"
      :footerStyle="footerStyle" :footerSliderStyle="footerSliderStyle"
      @collapseChange="e => $emit('collapseChange', e)" @monthChange="e => $emit('monthChange', e)"
      @swipeChange="e => $emit('swipeChange', e)" @showDatesChange="e => $emit('showDatesChange', e)"
      @dateClick="e => $emit('dateClick', e)" @dateChange="e => $emit('dateChange', e)">
    </lkMonth>
  </view>
</template>

<script>
  import props from './props.js'
  import lkHeader from './header.vue'
  import lkMonth from './month.vue'

  export default {
    name: 'lk-calendar',
    // #ifdef MP-WEIXIN
    options: {
      virtualHost: true,
      styleIsolation: 'shared',
    },
    // #endif
    mixins: [props],
    components: {
      lkHeader,
      lkMonth,
    },
    data() {
      return {}
    },
    methods: {}
  };
</script>

<style lang="scss" scoped>
  .calendar {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
</style>