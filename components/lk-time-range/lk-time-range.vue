<template>
  <u-popup
    :show="showPopup"
    @close="showPopup = false"
    mode="bottom"
    :mask="true"
  >
    <u-toolbar
      confirmColor="#3d7fff"
      @cancel="showPopup = false"
      @confirm="confirm"
    ></u-toolbar>
    <view class="time-picker-container">
      <lk-datetime-select
        v-model="startTime"
        :textStyle="{ fontSize: '32rpx' }"
        :customStyle="{ justifyContent: 'center' }"
        fontSize="32rpx"
        mode="time"
        align="center"
        placeholder="选择开始时间"
      >
      </lk-datetime-select>
      <text style="padding: 0 20rpx"> 至 </text>
      <lk-datetime-select
        v-model="endTime"
        mode="time"
        :textStyle="{ fontSize: '32rpx' }"
        :customStyle="{ justifyContent: 'center' }"
        fontSize="32rpx"
        align="center"
        placeholder="选择结束时间"
      >
      </lk-datetime-select>
    </view>
  </u-popup>
</template>

<script>
export default {
  data() {
    return {
      showPopup: false,
      startTime: "",
      endTime: "",
      start: "00:00",
      end: "23:59",
      showStartPicker: false,
      showEndPicker: false,
    };
  },
  watch: {
    visible() {
      if (!this.visible) {
        this.reject && this.reject();
      }
    },
  },
  methods: {
    onConfirmStart(value) {
      this.startTime = value;
    },
    open() {
      this.showPopup = true;
      this.startTime = ''
      this.endTime  =''
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    onConfirmEnd(value) {
      this.endTime = value;
    },
    confirm() {
      if (!this.endTime || !this.startTime) {
        return uni.showToast({
          title: "请选择时间",
          icon: "none",
        });
      }
      if (this.endTime < this.startTime) {
        return uni.showToast({
          title: "开始时间应小于结束时间",
          icon: "none",
        });
      }
      this.showPopup = false;
      this.$emit("confirm", {
        startTime: this.startTime,
        endTime: this.endTime,
      });
      this.resolve({
        startTime: this.startTime,
        endTime: this.endTime,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.time-picker-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20px;
  background-color: #fff;

}
</style>
