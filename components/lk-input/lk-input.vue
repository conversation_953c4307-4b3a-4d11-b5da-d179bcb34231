<template>
  <view class='lk-input'>
    <uvInput ref="input" :value="value" :type="type" :fixed="fixed" :disabled="disabled" :disabledColor="disabledColor"
      :clearable="clearable" :password="password" :maxlength="maxlength" :placeholder="placeholder"
      :placeholderClass="placeholderClass + ' lk-input-placeholder'" :placeholderStyle="placeholderStyle"
      :showWordLimit="showWordLimit" :confirmType="confirmType" :confirmHold="confirmHold" :holdKeyboard="holdKeyboard"
      :focus="focus" :autoBlur="autoBlur" :disableDefaultPadding="disableDefaultPadding" :cursor="cursor"
      :cursorSpacing="cursorSpacing" :selectionStart="selectionStart" :selectionEnd="selectionEnd"
      :adjustPosition="adjustPosition" :inputAlign="inputAlign" :fontSize="fontSize" :color="color"
      :prefixIcon="prefixIcon" :suffixIcon="suffixIcon" :suffixIconStyle="suffixIconStyle"
      :prefixIconStyle="prefixIconStyle" :border="border" :readonly="readonly" :shape="shape" :customStyle="customStyle"
      :formatter="formatter" @focus="inputFocus" @blur="inputBlur" @keyboardheightchange="$emit('keyboardheightchange')"
      @change="e => $emit('change', e)" @input="e => $emit('input', e)" @confirm="e => $emit('confirm', e)"
      @clear="$emit('clear')" @click="$emit('click')">
      <!-- #ifdef MP -->
      <slot name="prefix"></slot>
      <slot name="suffix"></slot>
      <!-- #endif -->
      <!-- #ifndef MP -->
      <slot name="prefix" slot="prefix"></slot>
      <slot name="suffix" slot="suffix"></slot>
      <!-- #endif -->
    </uvInput>

    <view v-if="showDropdown" class="lk-input__dropdown-mask" @tap.stop="closeDropdown" @touchmove.stop.prevent="noop">
    </view>

    <view v-if="showDropdown" class="lk-input__dropdown" :style="[innerDropdownStyle]">
      <slot name="dropdown">
        <view class="lk-input__dropdown-item" v-for="(item, index) in dropdownList" :key="index"
          @tap.stop="clickDropdownItem(item)">
          {{ item }}
        </view>
      </slot>
    </view>

  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  import uvInput from 'uview-ui/components/u-input/u-input.vue'

  export default {
    name: 'lk-input',
    mixins: [mpMixin, props],
    components: {
      uvInput
    },
    data() {
      return {
        showDropdown: false,
        inputRect: null
      }
    },
    computed: {
      hasDropdown() {
        return this.dropdownList?.length || this.$slots.dropdown
      },
      innerDropdownStyle() {
        const style = Object.assign({}, this.dropdownStyle)
        if (this.inputRect) {
          style.top = this.inputRect.bottom + 'px'
          style.left = this.inputRect.left + 'px'
          if (!style.width) {
            style.width = this.inputRect.width + 'px'
          }
        }
        return style
      }
    },
    methods: {
      inputFocus() {
        if (this.hasDropdown) {
          this.openDropdown()
        }
        this.$emit('focus')
      },
      inputBlur() {
        this.$emit('blur')
      },
      openDropdown() {
        uni.createSelectorQuery()
          .in(this)
          .select('.lk-input')
          .boundingClientRect(data => {
            this.inputRect = data
            this.showDropdown = this.hasDropdown
          }).exec()
      },
      closeDropdown() {
        this.showDropdown = false
      },
      clickDropdownItem(item) {
        if (this.value != item) {
          if (this.$refs.input?.onInput) {
            this.$nextTick(() => {
              this.$refs.input.onInput({
                detail: {
                  value: item
                }
              })
            })
          } else {
            this.$emit('input', item)
            this.$emit('change', item)
            this.$nextTick(() => {
              uni.$u.formValidate(this, 'change')
            })
          }
        }
        this.closeDropdown()
      },
    }
  }
</script>

<style lang="scss" scoped>
  .lk-input {
    display: flex;
    flex: 1;

    ::v-deep .u-input {
      padding: 16rpx 16rpx !important;

      &.u-border {
        border-width: 1px !important;

        &.u-input--radius.u-input--square {
          border-radius: 8rpx;
        }
      }

      .u-input__content__field-wrapper__field {
        height: auto !important;

        .uni-input-wrapper {
          height: auto !important;
        }

      }

      input {
        font-size: 26rpx;
        line-height: 1.4em;
        min-height: 1.4em;
        height: auto !important;
        color: #fff;
      }

    }

    &__dropdown-mask {
      position: fixed;
      z-index: 9998;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    &__dropdown {
      position: fixed;
      z-index: 9999;
      background-color: #FFFFFF;
      border: 1px solid #DADBDE;
      box-sizing: border-box;
      border-radius: 8rpx;
      max-height: 200rpx;
      overflow: auto;

      &-item {
        padding: 0 16rpx;
        font-size: 26rpx;
        height: 50rpx;
        display: flex;
        align-items: center;
      }
    }

  }
</style>
<style>
  .lk-input-placeholder {
    font-size: 26rpx;
    line-height: 1.4em;
    min-height: 1.4em;
    height: auto !important;
    color: rgb(192, 196, 204);
  }
</style>