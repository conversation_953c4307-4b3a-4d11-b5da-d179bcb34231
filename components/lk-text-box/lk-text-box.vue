<template>
  <view class="lk-text-box" :style="[innerCustomStyle]" @tap.stop="$emit('click')">

    <slot name="prefix">
      <view v-if="innerPrefixIconName || innerPrefixIconClass" class="lk-text-box-icon-wrapper"
        :style="[innerPrefixIconWrapperStyle]" @tap.stop="clickIcon('clickPrefix')">
        <u-icon v-if="innerPrefixIconName" :name="innerPrefixIconName" :size="innerIconSize" :color="innerIconColor"
          :customStyle="innerPrefixIconStyle"></u-icon>
        <view v-else-if="innerPrefixIconClass" :class="[innerPrefixIconName]" :style="[innerPrefixIconStyle]"></view>
      </view>
    </slot>

    <view class="lk-text-box-content" :style="[innerContentStyle]">
      <view v-if="text" class="lk-text-box-content-text" :style="[innerTextStyle]">
        {{ text }}
      </view>
      <view v-else class="lk-text-box-content-placeholder" :style="[innerPlaceholderStyle]">
        {{ placeholder }}
      </view>
    </view>

    <slot name="suffix">
      <view v-if="innerSuffixIconName || innerSuffixIconClass" class="lk-text-box-icon-wrapper"
        :style="[innerSuffixIconWrapperStyle]" @tap.stop="clickIcon('clickSuffix')">
        <u-icon v-if="innerSuffixIconName" :name="innerSuffixIconName" :size="innerIconSize" :color="innerIconColor"
          :customStyle="innerSuffixIconStyle"></u-icon>
        <view v-else-if="innerSuffixIconClass" :class="[innerSuffixIconClass]" :style="[innerSuffixIconStyle]"></view>
      </view>
    </slot>

    <slot name="extend"></slot>

  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  export default {
    name: 'lk-text-box',
    mixins: [mpMixin, props],
    data() {
      return {}
    },
    computed: {
      innerPrefixIconName() {
        const icon = this.text && this.validIcon(this.activePrefixIcon) ? this.activePrefixIcon :
          (this.validIcon(this.prefixIcon) ? this.prefixIcon : '')
        return icon && icon[0] != '.' ? icon : ''
      },
      innerPrefixIconClass() {
        const icon = this.text && this.validIcon(this.activePrefixIcon) ? this.activePrefixIcon :
          (this.validIcon(this.prefixIcon) ? this.prefixIcon : '')
        return icon && icon[0] == '.' ? icon.replaceAll('.', '') : ''
      },
      innerSuffixIconName() {
        const icon = this.text && this.validIcon(this.activeSuffixIcon) ? this.activeSuffixIcon :
          (this.validIcon(this.suffixIcon) ? this.suffixIcon : '')
        return icon && icon[0] != '.' ? icon : ''
      },
      innerSuffixIconClass() {
        const icon = this.text && this.validIcon(this.activeSuffixIcon) ? this.activeSuffixIcon :
          (this.validIcon(this.suffixIcon) ? this.suffixIcon : '')
        return icon && icon[0] == '.' ? icon.replaceAll('.', '') : ''
      },
      innerIconColor() {
        return (this.text && this.activeIconColor ? this.activeIconColor : this.iconColor) || this.color
      },
      innerIconSize() {
        return this.iconSize || this.fontSize
      },
      innerCustomStyle() {
        const style = {}
        if (this.borderRadius) {
          style.borderRadius = this.borderRadius
        }
        const borderColor = this.text && this.activeBorderColor ? this.activeBorderColor : this.borderColor
        if (this.noBorder) {
          style.border = 'none'
        } else {
          style.border = '1px solid ' + borderColor
        }
        if (this.customStyle) {
          Object.assign(style, this.customStyle)
        }
        if (this.display == 'inline-flex') {
          style.display = 'inline-flex'
          style.maxWidth = '100%'
        } else {
          style.display = 'flex'
          if (!this.stretch) {
            if (this.align == 'center') {
              style.display = 'flex'
              style.justifyContent = 'center'
            } else if (this.align == 'right') {
              style.display = 'flex'
              style.justifyContent = 'flex-end'
            }
          }
        }
        return style
      },
      innerContentStyle() {
        const style = {}
        if (this.contentStyle) {
          Object.assign(style, this.contentStyle)
        }
        if (this.align == 'center') {
          style.display = 'flex'
          style.justifyContent = 'center'
        } else if (this.align == 'right') {
          style.display = 'flex'
          style.justifyContent = 'flex-end'
        }
        if (this.stretch) {
          style.flex = '1'
        }
        return style
      },
      innerTextStyle() {
        const style = {}
        if (this.color) {
          style.color = this.color
        }
        if (this.fontSize) {
          style.fontSize = this.fontSize
        }
        if (this.lineHeight) {
          style.lineHeight = this.lineHeight
        }
        if (this.textStyle) {
          Object.assign(style, this.textStyle)
        }
        if (!this.multipleLine) {
          style.whiteSpace = 'nowrap'
        } else {
          style.wordBreak = "break-all"
        }
        return style
      },
      innerPlaceholderStyle() {
        const style = {}
        if (this.placeholderColor) {
          style.color = this.placeholderColor
        }
        if (this.fontSize) {
          style.fontSize = this.fontSize
        }
        if (this.lineHeight) {
          style.lineHeight = this.lineHeight
        }
        if (this.placeholderStyle) {
          Object.assign(style, this.placeholderStyle)
        }
        if (!this.multipleLine) {
          style.whiteSpace = 'nowrap'
        } else {
          style.wordBreak = "break-all"
        }
        return style
      },
      innerPrefixIconWrapperStyle() {
        const style = {}
        style.fontSize = this.innerPrefixIconStyle.fontSize
        style.background = this.innerPrefixIconStyle.background
        style.backgroundColor = this.innerPrefixIconStyle.backgroundColor
        style.width = this.innerPrefixIconStyle.width
        style.height = this.innerPrefixIconStyle.height
        style.marginRight = '10rpx'
        return style
      },
      innerPrefixIconStyle() {
        const style = {}
        style.fontSize = this.innerIconSize
        if (this.innerPrefixIconClass) {
          style.color = this.innerIconColor
        }
        style.display = 'flex'
        style.justifyContent = 'center'
        style.alignItems = 'center'
        if (this.iconStyle) {
          Object.assign(style, this.iconStyle)
        }
        if (this.prefixIconStyle) {
          Object.assign(style, this.prefixIconStyle)
        }
        return style
      },
      innerSuffixIconWrapperStyle() {
        const style = {}
        style.fontSize = this.innerSuffixIconStyle.fontSize
        style.background = this.innerSuffixIconStyle.background
        style.backgroundColor = this.innerSuffixIconStyle.backgroundColor
        style.width = this.innerSuffixIconStyle.width
        style.height = this.innerSuffixIconStyle.height
        style.marginLeft = '10rpx'
        return style
      },
      innerSuffixIconStyle() {
        const style = {}
        style.fontSize = this.innerIconSize
        if (this.innerSuffixIconClass) {
          style.color = this.innerIconColor
        }
        style.display = 'flex'
        style.justifyContent = 'center'
        style.alignItems = 'center'
        if (this.iconStyle) {
          Object.assign(style, this.iconStyle)
        }
        if (this.suffixIconStyle) {
          Object.assign(style, this.suffixIconStyle)
        }
        return style
      },
    },
    methods: {
      validIcon(icon) {
        return icon && icon != 'none' && icon != ' '
      },
      clickIcon(event) {
        const e = {
          stop: false
        }
        this.$emit(event, e)
        if (!e.stop) {
          this.$emit('click')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .lk-text-box {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 16rpx 16rpx;
    overflow: hidden;
    box-sizing: border-box;

    &-icon-wrapper {
      position: relative;
      width: 1em;
      height: 1.4em;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    &-content {
      overflow: hidden;

      &-text {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        line-height: 1.4em;
        min-height: 1.4em;
        height: auto !important;
        color: #333333;
        overflow: scroll;
      }

      &-placeholder {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        line-height: 1.4em;
        min-height: 1.4em;
        height: auto !important;
        color: '#C0C4CC';
        overflow: scroll;
      }

    }

  }
</style>
