export default {
  props: {
    // 文本内容
    text: {
      type: String,
      default: ''
    },
    // 文本颜色
    color: {
      type: String,
      default: '#333333'
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    // 占位文本内容
    placeholder: {
      type: String,
      default: ''
    },
    // 点位文本颜色
    placeholderColor: {
      type: String,
      default: '#C0C4CC'
    },
    // 文本字体大小
    fontSize: {
      type: String,
      default: ''
    },
    // 文本行高
    lineHeight: {
      type: String,
      default: ''
    },
    // 文本多行显示
    multipleLine: {
      type: Boolean,
      default: false
    },
    noBorder: {
      type: Boolean,
      default: false
    },
    // 边框颜色
    borderColor: {
      type: String,
      default: '#DADBDE'
    },
    // 边框半径
    borderRadius: {
      type: String,
      default: '8rpx'
    },
    // 文本非空时边框颜色
    activeBorderColor: {
      type: String,
      default: ''
    },
    // 前置图标，uview图标名，或'.classNameA classNameB'
    prefixIcon: {
      type: String,
      default: ''
    },
    // 文本非空时前置图标，uview图标名，或'.classNameA classNameB'
    activePrefixIcon: {
      type: String,
      default: ''
    },
    // 后置图标，uview图标名，或'.classNameA classNameB'
    suffixIcon: {
      type: String,
      default: ''
    },
    // 文本非空后置图标，uview图标名，或'.classNameA classNameB'
    activeSuffixIcon: {
      type: String,
      default: '',
    },
    // 图标颜色
    iconColor: {
      type: String,
      default: ''
    },
    // 文本非空时图标颜色
    activeIconColor: {
      type: String,
      default: ''
    },
    // 图标大小
    iconSize: {
      type: String,
      default: '26rpx'
    },
    // display属性 flex inline-flex
    display: {
      type: String,
      default: 'flex'
    },
    // 拉伸文本内容
    stretch: {
      type: Boolean,
      default: true
    },
    // 内容对齐 left center right
    align: {
      type: String,
      default: 'left'
    },
    // 组件外部样式
    customStyle: {
      type: Object,
      default: null
    },
    // 文本样式
    textStyle: {
      type: Object,
      default: null
    },
    // 占位文本样式
    placeholderStyle: {
      type: Object,
      default: null
    },
    // 内容样式
    contentStyle: {
      type: Object,
      default: null
    },
    // 图标样式
    iconStyle: {
      type: Object,
      default: null
    },
    // 前置图标样式
    prefixIconStyle: {
      type: Object,
      default: null
    },
    // 后置图标样式
    suffixIconStyle: {
      type: Object,
      default: null
    },
  }
}
