<template>
  <view>
    <u-popup :show="show" mode="center" round="24rpx" :safeAreaInsetBottom="false">
      <view class="privacy">
        <view class="content">
          <!-- 登录 注册 -->
          <view v-if="['login', 'register'].includes(type)">
            【{{ appName }}小程序】面向的用户群体为学校教师用户，在你使用【{{ appName }}小程序】服务之前，请仔细阅读
          </view>
          <!-- 其他 -->
          <view v-else>感谢你使用【{{ appName }}小程序】，在授权【{{ appName }}小程序】前请仔细阅读</view>

          <view class="link" @tap.stop="openPrivacy">《{{ appName }}小程序隐私保护指引》</view>
          <view>如果你同意《{{ appName }}小程序隐私保护指引》，请点击“{{ agreeText }}”开始使用【{{ appName }}小程序】。</view>
        </view>
        <view class="footer">
          <button id="disagree-btn" class="disagree-btn footer-btn" @click="disagree">{{ disagreeText }}</button>
          <button id="agree-btn" class="agree-btn footer-btn" :open-type="finalOpenType"
            @getphonenumber="getPhoneNumber" @agreeprivacyauthorization="agree"
            @click="clickAgree">{{ agreeText }}</button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'

  let privacyHandler = null
  const privacyResolves = new Set()
  const closeOtherHooks = new Set()

  const isPrivacyVersion = !!wx.onNeedPrivacyAuthorization

  if (isPrivacyVersion) {
    wx.onNeedPrivacyAuthorization(resolve => {
      console.log('onNeedPrivacyAuthorization')
      if (typeof privacyHandler === 'function') {
        privacyHandler(resolve)
      }
    })
  }

  const closeOther = (close) => {
    closeOtherHooks.forEach(hook => {
      if (hook !== close) {
        hook()
      }
    })
  }

  export default {
    mixins: [mpMixin],
    props: {
      // login register
      type: {
        type: String,
        default: ''
      },
      openType: {
        type: String,
        default: '',
      },
      disagreeText: {
        type: String,
        default: '不同意'
      },
      agreeText: {
        type: String,
        default: '同意并继续'
      },
    },
    computed: {
      innerOpenType() {
        return this.options?.openType ?? this.openType
      },
      finalOpenType() {
        if (isPrivacyVersion) {
          return this.innerOpenType ? `${this.innerOpenType}|agreePrivacyAuthorization` : 'agreePrivacyAuthorization'
        } else {
          return this.innerOpenType
        }
      },
    },
    data() {
      return {
        show: false,
        options: null
      }
    },
    created() {
      closeOtherHooks.add(this.close)
    },
    onPageShow() {
      privacyHandler = this.handler
    },
    onPageHide() {
      if (privacyHandler === this.handler) {
        privacyHandler = null
      }
    },
    beforeDestroy() {
      closeOtherHooks.delete(this.close)
      if (privacyHandler === this.handler) {
        privacyHandler = null
      }
    },
    methods: {
      handler(resolve) {
        privacyResolves.add(resolve)
        this.open(null, true)
        closeOther(this.close)
      },
      getPhoneNumber(e) {
        if (e.detail.code) {
          console.log('privacy getPhoneNumber agree', e)
          this.options?.resolve?.(e)
        } else {
          console.log('privacy getPhoneNumber disagree', e)
          this.options?.reject?.()
          if (e.detail.errMsg == 'getPhoneNumber:ok') {
            this.$u.toast('微信版本过低不支持此功能')
          }
        }
        this.close()
      },
      clickAgree() {
        if (this.innerOpenType) {
          return
        }
        if (!isPrivacyVersion) {
          console.log('clickAgree no privacy version')
          this.options?.resolve?.()
          this.close()
        }
      },
      agree(e) {
        console.log('privacy agree', e)
        if (!this.innerOpenType) {
          this.options?.resolve?.()
          this.close()
        }
        privacyResolves.forEach(resolve => {
          resolve({
            event: 'agree',
            buttonId: 'agree-btn'
          })
        })
        privacyResolves.clear()
      },
      disagree(e) {
        console.log('privacy disagree', e)
        this.options?.reject?.()
        this.close()
        privacyResolves.forEach(resolve => {
          resolve({
            event: 'disagree',
          })
        })
        privacyResolves.clear()
      },
      open(options, inner = false) {
        this.show = true
        console.log('privacy open')
        if (!inner) {
          return new Promise((resolve, reject) => {
            this.options = {
              ...(options || {}),
              resolve,
              reject,
            }
          })
        }
      },
      close() {
        this.show = false
        this.options = null
        console.log('privacy close')
      },
      openPrivacy() {
        wx.openPrivacyContract({
          success: res => {
            console.log('openPrivacyContract success')
          },
          fail: res => {
            console.error('openPrivacyContract fail', res)
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .privacy {
    padding: 40rpx;
    width: 550rpx;

    .content {
      font-size: 30rpx;
      line-height: 1.8em;

      .link {
        color: #26D1CB;
      }

    }

    .footer {
      display: flex;
      justify-content: space-around;
      margin-top: 40rpx;

      &-btn {
        width: 240rpx;
        height: 80rpx;
        margin: 0;
        border-radius: 12rpx;
        font-size: 30rpx;
        border: 1px solid #26D1CB;
      }

      .disagree-btn {
        color: #26D1CB;
        background-color: #FFFFFF;

        &:hover {
          background-color: #dadbde;
        }
      }

      .agree-btn {
        color: #FFFFFF;
        background-color: #26D1CB;

        &:hover {
          background-color: #26D1CB;
          border-color: #26D1CB;
        }
      }

    }

  }
</style>