<template>
  <lk-text-box :text="innerText" :color="color" :backgroundColor="backgroundColor" :placeholder="placeholder"
    :placeholderColor="placeholderColor" :fontSize="fontSize" :lineHeight="lineHeight" :multipleLine="multipleLine"
    :noBorder="noBorder" :borderColor="borderColor" :borderRadius="borderRadius" :activeBorderColor="activeBorderColor"
    :prefixIcon="prefixIcon" :activePrefixIcon="activePrefixIcon" :suffixIcon="suffixIcon"
    :activeSuffixIcon="clearable ? 'close-circle' : activeSuffixIcon" :iconColor="iconColor"
    :activeIconColor="activeIconColor" :iconSize="iconSize" :display="display" :stretch="stretch" :align="align"
    :customStyle="customStyle" :textStyle="textStyle" :placeholderStyle="placeholderStyle" :contentStyle="contentStyle"
    :iconStyle="iconStyle" :prefixIconStyle="prefixIconStyle" :suffixIconStyle="suffixIconStyle" @click="openPicker"
    @clickSuffix="clickSuffix">

    <view slot="extend">

      <u-picker ref="picker" :show="showPicker" :visibleItemCount="6" immediate-change :columns="pickerColumns"
        :keyName="pickerLabelName || labelName" @confirm="pickerConfirm" @cancel="showPicker = false">
      </u-picker>

    </view>

  </lk-text-box>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  export default {
    name: 'lk-list-select',
    mixins: [mpMixin, props],
    data() {
      return {
        innerValue: null,
        showPicker: false,
      }
    },
    computed: {
      innerText() {
        const item = this.pickerList.find(it => it[this.valueName] == this.innerValue)
        return String(item ? (item.labelIgnored ? '' : item[this.labelName]) : this.innerValue ?? '')
      },
      pickerList() {
        return this.list ?? []
      },
      pickerColumns() {
        return [this.pickerList]
      },
    },
    watch: {
      value(val) {
        this.innerValue = val
      }
    },
    created() {
      this.innerValue = this.value
    },
    methods: {
      emitValue(value) {
        this.innerValue = value
        this.$emit('input', this.innerValue)
        this.$emit('change', this.innerValue)
        this.$nextTick(() => {
          uni.$u.formValidate(this, "change")
        })
      },
      openPicker() {
        this.showPicker = true
        const index = this.pickerList.findIndex(it => it[this.valueName] == this.innerValue)
        this.$refs?.picker?.setIndexs([index >= 0 ? index : 0], true)
      },
      pickerConfirm(e) {
        this.showPicker = false
        this.emitValue(e.value[0][this.valueName])
      },
      clickSuffix(e) {
        if (this.clearable && this.innerValue) {
          e.stop = true
          this.emitValue(null)
          this.$emit('clear')
        }
      }
    }
  }
</script>

<style>
</style>
