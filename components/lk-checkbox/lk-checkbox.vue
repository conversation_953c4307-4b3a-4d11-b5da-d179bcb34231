<template>
  <view class="lk-checkbox" @tap="click" :style="[style]">
    <view class="lk-checkbox-inner">
      <u-icon v-if="innerValue" name="checkbox-mark" :color="checkColor" :size="iconSize"></u-icon>
    </view>
  </view>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  export default {
    name: 'lk-checkbox',
    mixins: [mpMixin, props],
    data() {
      return {
        innerValue: false
      }
    },
    computed: {
      style() {
        const size = typeof this.size == 'number' ? `${this.size}px` : this.size
        const style = {
          width: size,
          height: size,
          border: this.innerValue ? this.checkBgColor : `1px solid ${this.borderColor}`,
          borderRadius: this.shape == 'square' ? '4rpx' : '50%',
          backgroundColor: this.innerValue ? this.checkBgColor : this.bgColor,
          ...this.customStyle
        }
        return style
      },
      iconSize() {
        const size = typeof this.size == 'number' ? this.size :
          (this.size.endsWith('rpx') ? uni.upx2px(parseFloat(this.size)) : parseFloat(this.size))
        return `${size * .75}px`
      },
    },
    watch: {
      value: {
        handler(val) {
          this.innerValue = val
        },
        immediate: true,
      }
    },
    methods: {
      click() {
        if (!this.enabled || (this.innerValue && !this.uncheckable)) {
          return
        }
        if (!this.manual) {
          this.innerValue = !this.innerValue
        }
        this.$emit('input', !this.innerValue)
        this.$emit('change', !this.innerValue)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .lk-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }
</style>