export default {
  props: {
    enabled: {
      type: Boolean,
      default: true,
    },
    uncheckable: {
      type: Boolean,
      default: true,
    },
    manual: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Boolean,
      default: false,
    },
    // circle square
    shape: {
      type: String,
      default: 'square'
    },
    size: {
      type: [Number, String],
      default: '30rpx',
    },
    checkColor: {
      type: String,
      default: '#ffffff',
    },
    bgColor: {
      type: String,
      default: '#ffffff',
    },
    checkBgColor: {
      default: '#3366FF',
    },
    borderColor: {
      type: String,
      default: 'rgb(200, 201, 204)',
    },
    customStyle: {
      type: [Object, null],
      default: null
    },

  },
}