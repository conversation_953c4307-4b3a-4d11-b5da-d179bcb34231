import textBoxProps from "@/components/lk-text-box/props.js";

export default {
  mixins: [textBoxProps],
  props: {
    value: {
      type: [String, Number,Array],
      default: "",
    },
    // 选择列表
    list: {
      type: Array,
      default: null,
    },

    // 列表中在选择列表中显示字段名称
    pickerLabelName: {
      type: String,
      default: "",
    },
    suffixIcon: {
      type: String,
      default: "arrow-down",
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    textField: {
      type: String,
      default: "name",
    },
    treeProps: {
      type: Object,
      default: () => {
        return { label: "name", children: "children" };
      },
    },

    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
};
