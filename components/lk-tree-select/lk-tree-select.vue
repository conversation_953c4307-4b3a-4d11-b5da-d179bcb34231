<template>
  <lk-text-box
    :text="innerText"
    :color="color"
    :backgroundColor="backgroundColor"
    :placeholder="placeholder"
    :placeholderColor="placeholderColor"
    :fontSize="fontSize"
    :lineHeight="lineHeight"
    :multipleLine="multipleLine"
    :noBorder="noBorder"
    :borderColor="borderColor"
    :borderRadius="borderRadius"
    :activeBorderColor="activeBorderColor"
    :prefixIcon="prefixIcon"
    :activePrefixIcon="activePrefixIcon"
    :suffixIcon="suffixIcon"
    :activeSuffixIcon="clearable ? 'close-circle' : activeSuffixIcon"
    :iconColor="iconColor"
    :activeIconColor="activeIconColor"
    :iconSize="iconSize"
    :display="display"
    :stretch="stretch"
    :align="align"
    :customStyle="customStyle"
    :textStyle="textStyle"
    :placeholderStyle="placeholderStyle"
    :contentStyle="contentStyle"
    :iconStyle="iconStyle"
    :prefixIconStyle="prefixIconStyle"
    :suffixIconStyle="suffixIconStyle"
    @click="showPicker = true"
    @clickSuffix="clickSuffix"
  >
    <view slot="extend">
      <lk-tree-picker
        :show="showPicker"
        :treeData="treeData"
        :treeProps="treeProps"
        :multiple="multiple"
        :defaultCheckedKeys="defaultCheckedKeys_"
        @confirm="pickerConfirm"
        @cancel="showPicker = false"
      >
      </lk-tree-picker>
    </view>
  </lk-text-box>
</template>

<script>

import mpMixin from "@/common/mixin/mp.js";
import props from "./props.js";

export default {
  name: "lk-calendar-select",
  mixins: [mpMixin, props],
  data() {
    return {
      innerValue: [],
      showPicker: false,
      defaultCheckedKeys_:[]
    };
  },
  computed: {
    innerText() {
      return this.innerValue&&this.innerValue.map(item=>item[this.treeProps.label])
    },

  },
  watch: {
    value(val) {
      this.innerValue = val;
    },
    showPicker(val){
      if(val){
        this.defaultCheckedKeys_ = this.innerValue.map(item=>item[this.treeProps.key || 'id'])
      }
    }
  },
  created() {
    this.innerValue = this.value;
  },
  methods: {


    emitValue(value) {
      this.innerValue = value;
      this.$emit("input", this.innerValue);
      this.$emit("change",this.innerValue);
      this.$nextTick(() => {
        uni.$u.formValidate(this, "change");
      });
    },
    pickerConfirm(e) {
      this.showPicker = false;
      this.emitValue(e);
    },
    clickSuffix(e) {
      if (this.clearable && this.innerValue) {
        e.stop = true;
        this.emitValue([]);
        this.$emit("clear");
      }
    },
  },
};
</script>

<style>
</style>
