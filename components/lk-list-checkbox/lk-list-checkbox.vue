<template>
  <lk-text-box :text="innerText" :color="color" :backgroundColor="backgroundColor" :placeholder="placeholder"
    :placeholderColor="placeholderColor" :fontSize="fontSize" :lineHeight="lineHeight" :multipleLine="multipleLine"
    :noBorder="noBorder" :borderColor="borderColor" :borderRadius="borderRadius" :activeBorderColor="activeBorderColor"
    :prefixIcon="prefixIcon" :activePrefixIcon="activePrefixIcon" :suffixIcon="suffixIcon" :iconColor="iconColor"
    :activeIconColor="activeIconColor" :iconSize="iconSize" :display="display" :stretch="stretch" :align="align"
    :customStyle="customStyle" :textStyle="textStyle" :placeholderStyle="placeholderStyle" :contentStyle="contentStyle"
    :iconStyle="iconStyle" :prefixIconStyle="prefixIconStyle" :suffixIconStyle="suffixIconStyle" @click="openPicker">

    <view slot="extend" @tap.stop="noop">

      <lk-confirm :show="showPicker" mode="bottom" themeButton width="100vw" @close="showPicker = false"
        @confirm="confirm">
		
		<view class="list-keyword">
		  <view class="search">
					<u-search v-model="listKeyword" height="65rpx"  :show-action="false"></u-search>
				</view>
		</view>
        <view class="cascader">
          <view class="column" v-for="(column, cIndex) in columnList" :key="cIndex">
            <view class="item" v-for="(item, index) in column"  :key="index" :class="[{
                strictly: checkStrictly,
                expanded: expandedIndexList[cIndex] == index, 
                leaf: !item[childrenName] || !item[childrenName].length,
                checked: valueIncludes(checkedValue, item[valueName]), 
                lighted: valueIncludes(lightedValue, item[valueName]),
                center: columnCount == 1,
                disabled: item.disabled
              }]" @tap.stop="clickItem(item, index, cIndex)">

              <view class="checkbox" @tap.stop="clickCheckBox(item, index, cIndex)">
                <view class="checkbox-inner">
                  <u-icon name="checkbox-mark" color="#1CBE83" size="24rpx"></u-icon>
                </view>
              </view>

              <view class="label">
                {{ item[labelName] }}
              </view>

              <view class="arrow">
                <u-icon name="arrow-right" size="24rpx"></u-icon>
              </view>

            </view>
          </view>
        </view>
      </lk-confirm>
    </view>

  </lk-text-box>
</template>

<script>
  import mpMixin from '@/common/mixin/mp.js'
  import selectMixin from '@/components/lk-text-box/props.js'

  export default {
    name: 'lk-cascader',
    mixins: [mpMixin, selectMixin],
    props: {
      value: {
        type: [String, Number, Object, Array],
        default: null,
      },
      options: {
        type: Array,
        default: () => []
      },
      labelName: {
        type: String,
        default: 'label'
      },
      valueName: {
        type: String,
        default: 'value'
      },
      childrenName: {
        type: String,
        default: 'children',
      },
      suffixIcon: {
        type: String,
        default: 'arrow-down'
      },
      checkStrictly: {
        type: Boolean,
        default: false,
      },
      multiple: {
        type: Boolean,
        default: false,
      },
      uncheckable: {
        type: Boolean,
        default: false
      },
      emitPath: {
        type: Boolean,
        default: false,
      },
      showFullLabel: {
        type: Boolean,
        default: false,
      },
      labelSeparator: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        showPicker: false,
        checkedValue: [],
        expandedIndexList: [],
        innerValue: [],
		listKeyword:''
      }
    },
    computed: {

      innerText() {
        return this.innerValue.map(value => {
          const itemList = this.getValueItemList(value)
          if (!itemList.length) {
            return ''
          }
          if (this.showFullLabel) {
            return itemList.map(it => it[this.labelName]).join(this.labelSeparator || '')
          } else {
            return itemList[itemList.length - 1][this.labelName] || ''
          }
        }).filter(it => it).join(' ')
      },
      columnList() {
        if (!this.options) {
          return []
        }
        let list = []
        list.push(this.options)
        let last = this.options
        for (let i = 0; i < this.expandedIndexList.length; i++) {
          last = last[this.expandedIndexList[i]]?.[this.childrenName]
          if (!last) {
            break
          }
          list.push(last)
        }
		if(list[0]){
			
		 list[0] = this.listKeyword ? list[0].filter(item => {
			return  (item[this.labelName]||'').includes(this.listKeyword)
		}) : list[0]
		
		}
        return list
      },
      columnCount() {
        let max = 0
        const tr = (list, depth) => {
          if (!list?.length) {
            return
          }
          if (depth > max) {
            max = depth
          }
          list.forEach(it => {
            const children = it[this.childrenName]
            if (children?.length) {
              tr(children, depth + 1)
            }
          })
        }
        tr(this.options, 1)
        return max
      },
      lightedValue() {
        return this.checkedValue.reduce((list, it) => {
          this.getValueItemList(it).forEach(it => {
            const value = it[this.valueName]
            if (!this.valueIncludes(this.checkedValue, value)) {
              list.push(value)
            }
          })
          return list
        }, [])
      },
    },
    watch: {
      value: {
        handler(val) {
          if (Array.isArray(val)) {
            if (this.multiple) {
              if (this.emitPath) {
                this.innerValue = val.map(it => Array.isArray(it) ? it[it.length - 1] : it)
              } else {
                this.innerValue = val.slice()
              }
            } else {
              this.innerValue = val.length ? [val[val.length - 1]] : []
            }
          } else if (!this.emitPath && typeof val == 'string') {
            this.innerValue = val ? val.split(',') : []
          } else {
            this.innerValue = val != null && val != '' ? [val] : []
          }
        },
        immediate: true
      }
    },
    methods: {
      valueIncludes(list, value) {
        return list.some(it => it == value)
      },
      valueIndexOf(list, value) {
        return list.findIndex(it => it == value)
      },
      emitValue(value) {
        this.innerValue = value
        let emitValue = null
        if (this.multiple) {
          if (this.emitPath) {
            emitValue = value.map(it => this.getValueItemList(it).map(it => it[this.valueName]))
          } else if (typeof this.value == 'string') {
            emitValue = value.slice().join(',')
          } else {
            emitValue = value.slice()
          }
        } else if (this.emitPath) {
          emitValue = value[0] != null ? this.getValueItemList(value[0]).map(it => it[this.valueName]) : null
        } else {
          emitValue = value[0] ?? null
        }

        this.$emit('input', emitValue)
        this.$emit('change', emitValue)
        this.$nextTick(() => {
          uni.$u.formValidate(this, 'change')
        })
      },
      getValueItemList(value) {
        let itemList = []
        const tr = list => list?.some(it => {
          if (it[this.valueName] == value) {
            itemList.push(it)
            return true
          }
          if (tr(it[this.childrenName])) {
            itemList.push(it)
            return true
          }
          return false
        })
        tr(this.options)
        return itemList.reverse()
      },
      setDefaultExpandedList() {
        let expandedList = []
        const tr = list => list?.some((it, index) => {
          if (it[this.valueName] == this.innerValue[0]) {
            let c = it[it.childrenName]
            if (c?.length) {
              expandedList.push(index)
              while ((c = c[0][this.childrenName])?.length) {
                expandedList.push(0)
              }
            }
            return true
          }
          if (tr(it[this.childrenName])) {
            expandedList.unshift(index)
            return true
          }
          return false
        })

        tr(this.options)

        if (!expandedList.length) {
          let c = this.options
          while ((c = c?.[0]?.[this.childrenName])?.length) {
            expandedList.push(0)
          }
        }

        this.expandedIndexList = expandedList
      },
      openPicker() {
        this.showPicker = true
        this.checkedValue = this.innerValue.slice()
        this.setDefaultExpandedList()
      },
      checkItem(item) {
        if (item.disabled) {
          return
        }
        const value = item[this.valueName]
        const valueIndex = this.valueIndexOf(this.checkedValue, value)
        if (valueIndex < 0) {
          if (!this.multiple) {
            this.checkedValue.splice(0)
          }
          this.checkedValue.push(value)
        } else if (this.multiple || this.uncheckable) {
          this.checkedValue.splice(valueIndex, 1)
        }
      },
      expand(item, index, cIndex) {
        if (this.expandedIndexList[cIndex] != index) {
          this.expandedIndexList.splice(cIndex)
          if (item[this.childrenName]?.length) {
            this.expandedIndexList.push(index)
          }
        }
      },
      clickCheckBox(item, index, cIndex) {
        this.expand(item, index, cIndex)
        this.checkItem(item)
      },
      clickItem(item, index, cIndex) {
        this.expand(item, index, cIndex)
        if (!item[this.childrenName]?.length) {
          this.checkItem(item)
        }
      },
      confirm() {
        this.emitValue(this.checkedValue)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .cascader {
    display: flex;
    min-height: 200rpx;
    height: 50vh;
    justify-content: center;

    .column {
      flex: 1;
      display: flex;
      flex-direction: column;
      font-size: 26rpx;
      overflow: auto;

      .item {
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        padding: 30rpx 10rpx;
        color: #333333;
        display: flex;
        align-items: center;

        $checkbox-size: 32rpx;

        .checkbox {
          position: absolute;
          height: 100%;
          width: 50rpx;
          display: none;
          justify-content: flex-start;
          align-items: center;

          .checkbox-inner {
            width: $checkbox-size;
            height: $checkbox-size;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;

            ::v-deep .u-icon {
              display: none;
            }
          }
        }

        .label {
          flex: 1;
          overflow: auto;
          font-size: 32rpx;
        }

        .arrow {
          ::v-deep .u-icon__icon {
            opacity: .6 !important;
          }
        }

        &.strictly {

          .checkbox {
            display: flex;

            .checkbox-inner {
              border: 1px solid #aaa;
            }
          }

          .label {
            margin-left: calc(#{$checkbox-size} + 10rpx);
          }

        }

        &.expanded {
          font-weight: bold;

          .arrow {
            ::v-deep .u-icon__icon {
              opacity: 1 !important;
              font-weight: bold !important;
            }
          }
        }

        &.leaf {

          .checkbox {
            display: flex;
          }

          .label {
            margin-left: calc(#{$checkbox-size} + 10rpx);
          }

          .arrow {
            display: none;
          }
        }

        &.checked {
          color: #1CBE83;

          .checkbox-inner {
            ::v-deep .u-icon {
              display: block;
            }
          }

        }

        &.lighted {
          color: #1CBE83;

          .arrow {
            ::v-deep .u-icon__icon {
              color: #1CBE83 !important;
            }
          }
        }

        &.center {
          .label {
            text-align: center;
          }
        }

        &.disabled {
          .checkbox {
            display: none;
          }
        }

      }

      &:last-child {
        border-right: none;

        .item {
          border-right: none;
        }
      }

    }
  }
</style>