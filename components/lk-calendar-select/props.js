import textBoxProps from '@/components/lk-text-box/props.js'

export default {
  mixins: [textBoxProps],
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    range: {
      type: Boolean,
      default: false
    },
    minDate: {
      type: [String, Number],
      default: ''
    },
    maxDate: {
      type: [String, Number],
      default: ''
    },
    dateSeparator: {
      type: [String, Array],
      default: ''
    },
    rangeSeparator: {
      type: String,
      default: ''
    },
    suffixIcon: {
      type: String,
      default: 'arrow-down'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    sameShowFirst: {
      type: Boolean,
      default: true
    },
  }
}
