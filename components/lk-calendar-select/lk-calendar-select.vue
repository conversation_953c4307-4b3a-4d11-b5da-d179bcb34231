<template>
  <lk-text-box :text="innerText" :color="color" :backgroundColor="backgroundColor" :placeholder="placeholder"
    :placeholderColor="placeholderColor" :fontSize="fontSize" :lineHeight="lineHeight" :multipleLine="multipleLine"
    :noBorder="noBorder" :borderColor="borderColor" :borderRadius="borderRadius" :activeBorderColor="activeBorderColor"
    :prefixIcon="prefixIcon" :activePrefixIcon="activePrefixIcon" :suffixIcon="suffixIcon"
    :activeSuffixIcon="clearable ? 'close-circle' : activeSuffixIcon" :iconColor="iconColor"
    :activeIconColor="activeIconColor" :iconSize="iconSize" :display="display" :stretch="stretch" :align="align"
    :customStyle="customStyle" :textStyle="textStyle" :placeholderStyle="placeholderStyle" :contentStyle="contentStyle"
    :iconStyle="iconStyle" :prefixIconStyle="prefixIconStyle" :suffixIconStyle="suffixIconStyle"
    @click="showPicker = true" @clickSuffix="clickSuffix">

    <view slot="extend">
      <lk-calendar-picker :show="showPicker" :date="pickerDate" :default-range="pickerRange" @confirm="pickerConfirm"
        :range="range" :minDate="minDate" :maxDate="maxDate" @cancel="showPicker = false">
      </lk-calendar-picker>
    </view>

  </lk-text-box>
</template>

<script>
  import dayjs from '@/common/utils/day.js'

  import mpMixin from '@/common/mixin/mp.js'
  import props from './props.js'

  export default {
    name: 'lk-calendar-select',
    mixins: [mpMixin, props],
    data() {
      return {
        innerValue: [],
        showPicker: false,
      }
    },
    computed: {
      innerText() {
        if (!this.innerValue.length) {
          return ''
        }
        if (this.range) {
          const first = this.innerValue[0]
          const last = this.innerValue[this.innerValue.length - 1]
          if (first == last && this.sameShowFirst) {
            return this.toDateText(first)
          }
          return `${this.toDateText(first)}${this.rangeSeparator || ' - '}${this.toDateText(last)}`
        } else {
          return this.toDateText(this.innerValue[0])
        }
      },
      pickerDate() {
        if (!this.innerValue.length) {
          return dayjs().format('YYYY-MM-DD')
        }
        return this.innerValue[0]
      },
      pickerRange() {
        if (!this.range) {
          return null
        }
        if (this.innerValue.length > 0) {
          return [this.innerValue[0], this.innerValue[this.innerValue.length - 1]]
        }
        return null
      }
    },
    watch: {
      value(val) {
        this.innerValue = this.toInnerValue(val)
      }
    },
    created() {
      this.innerValue = this.toInnerValue(this.value)
    },
    methods: {
      toInnerValue(value) {
        if (Array.isArray(value)) {
          return value.map(it => dayjs(it).format('YYYY-MM-DD'))
        } else {
          return value ? [dayjs(value).format('YYYY-MM-DD')] : []
        }
      },
      toDateText(date) {
        if (this.dateSeparator && date) {
          if (Array.isArray(this.dateSeparator)) {
            const arr = date.split('-')
            const list = [arr[0]]
            for (let i = 0; i < arr.length - 1; i++) {
              list.push(i < this.dateSeparator.length ? this.dateSeparator[i] : '-')
              list.push(arr[i + 1])
            }
            return list.join('')
          }
          return date.replaceAll('-', this.dateSeparator)
        }
        return date
      },
      emitValue(value) {
        this.innerValue = value
        this.$emit('input', this.innerValue)
        this.$emit('change', this.innerValue)
        this.$nextTick(() => {
          uni.$u.formValidate(this, 'change')
        })
      },
      pickerConfirm(e) {
        this.showPicker = false
        this.emitValue(e)
      },
      clickSuffix(e) {
        if (this.clearable && this.innerValue) {
          e.stop = true
          this.emitValue([])
          this.$emit('clear')
        }
      }
    }
  }
</script>

<style>
</style>
