const http = uni.$u.http;

/**
 * 创建订单
 * @param {Object} request - 订单创建请求
 * @param {string} [request.appointForm] - 预约申请表
 * @param {Array} [request.consumables] - 耗材列表
 * @param {number} request.consumables[].id - 耗材ID
 * @param {string} request.consumables[].name - 耗材名称
 * @param {number} request.consumables[].orderId - 订单ID
 * @param {number} request.consumables[].price - 价格
 * @param {string} request.consumables[].unit - 单位
 * @param {number} request.consumables[].num - 数量
 * @param {string} request.experimentName - 实验名称
 * @param {number} request.experimentType - 实验类型 1:培训 2:试测 3:正式
 * @param {number} [request.id] - 订单ID
 * @param {Array} [request.incrementServices] - 增值服务列表
 * @param {number} request.incrementServices[].incrementServiceId - 增值服务ID
 * @param {string} request.incrementServices[].name - 增值服务名称
 * @param {number} request.incrementServices[].num - 个数
 * @param {number} request.incrementServices[].price - 价格（元）
 * @param {string} request.incrementServices[].unit - 单位
 * @param {Object} request.instruments[] - 订单仪器关联请求
 * @param {string} request.instruments[].endTime - 结束时间
 * @param {number} request.instruments[].instrumentId - 设备ID
 * @param {number} request.instruments[].orderId - 订单ID
 * @param {string} request.instruments[].startTime - 开始时间
 * @param {string} [request.orderNo] - 订单编号
 * @param {number} [request.parentId] - 父订单ID
 * @param {number} [request.instrumentId] - 设备ID
 * @param {number} request.payType - 付款方式 1:个人账户 2:团队余额 3:团队额度
 * @param {string} [request.remark] - 备注
 * @param {number} [request.returnAddressId] - 样品寄回地址
 * @param {number} request.sampleReturnable - 样品寄回 0:不寄回 1:寄回
 * @param {number} request.status - 状态 0:草稿 1:提交
 * @param {number} [request.teamId] - 团队ID
 * @param {number} request.totalAmount - 总金额（原价）
 * @param {number} request.amount - 折后价格
 * @param {number} request.type - 订单类型 1:送样申请 2:预约检测-工程师操作 3:预约检测-自主操作 4:需求订单
 * @param {number} request.userId - 用户ID
 * @param {string} [request.appointForm] - 预约申请单
 * @param {number} [request.actualDuration] - 实际消耗时长（小时）
 * @param {number} [request.shipStatus] - 邮寄状态 0:自带 1:待寄出 2:已寄出 3:已签收 4:已寄回
 * @returns {Promise} 返回创建结果（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 内容）:
 * boolean - 创建是否成功
 */
const createOrder = (request) => {
  return http.post("/instrument/mini/order/create", request);
};

/**
 * 创建需求订单
 * @param {Object} request - 需求订单创建请求
 * @param {string} request.experimentName - 需求名称
 * @param {Array} request.instruments - 仪器订单列表
 * @param {number} request.instruments[].actualDuration - 实际时长
 * @param {number} request.instruments[].amount - 折后价格
 * @param {string} [request.instruments[].appointForm] - 预约申请单
 * @param {Array} [request.instruments[].consumables] - 耗材列表
 * @param {number} request.instruments[].consumables[].id - 耗材ID
 * @param {string} request.instruments[].consumables[].name - 耗材名称
 * @param {number} request.instruments[].consumables[].num - 数量
 * @param {number} request.instruments[].consumables[].price - 价格
 * @param {string} request.instruments[].consumables[].unit - 单位
 * @param {string} request.instruments[].experimentName - 实验名称
 * @param {number} request.instruments[].experimentType - 实验类型
 * @param {number} request.instruments[].id - 仪器ID
 * @param {Array} [request.instruments[].incrementServices] - 增值服务列表
 * @param {number} request.instruments[].incrementServices[].incrementServiceId - 增值服务ID
 * @param {string} request.instruments[].incrementServices[].name - 服务名称
 * @param {number} request.instruments[].incrementServices[].num - 数量
 * @param {number} request.instruments[].incrementServices[].price - 价格
 * @param {string} request.instruments[].incrementServices[].unit - 单位
 * @param {number} request.instruments[].instrumentId - 仪器ID
 * @param {Array} [request.instruments[].instruments] - 预约时间段
 * @param {string} request.instruments[].instruments[].endTime - 结束时间
 * @param {number} request.instruments[].instruments[].instrumentId - 仪器ID
 * @param {string} request.instruments[].instruments[].startTime - 开始时间
 * @param {string} [request.instruments[].orderNo] - 订单号
 * @param {number} [request.instruments[].parentId] - 父订单ID
 * @param {number} request.instruments[].payType - 支付方式
 * @param {string} [request.instruments[].remark] - 备注
 * @param {number} [request.instruments[].returnAddressId] - 退回地址ID
 * @param {number} request.instruments[].sampleReturnable - 样品是否可退回 0:不可退回 1:可退回
 * @param {number} [request.instruments[].shipStatus] - 邮寄状态 0:自带 1:待寄出 2:已寄出 3:已签收 4:已寄回
 * @param {number} request.instruments[].status - 订单状态
 * @param {number} [request.instruments[].teamId] - 团队ID
 * @param {number} request.instruments[].totalAmount - 总金额（原价）
 * @param {number} request.instruments[].type - 订单类型
 * @param {number} request.instruments[].userId - 用户ID
 * @param {number} request.payType - 支付方式
 * @param {string} [request.remark] - 需求描述
 * @param {number} request.status - 状态 0:草稿 1:提交
 * @param {number} [request.teamId] - 团队ID
 * @param {number} request.userId - 用户ID
 * @returns {Promise} 返回创建结果（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 内容）:
 * boolean - 创建是否成功
 */
const createDemandOrder = (request) => {
  return http.post("/instrument/mini/order/demandCreate", request);
};

/**
 * 获取订单详情
 * @param {Object} request - 详情请求参数
 * @param {number} request.id - 订单ID
 * @param {number} [request.tmbId] - TMB ID
 * @returns {Promise} 返回订单详情（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 对象）:
 * {
 *   id: number, // 订单ID
 *     orderNo: string, // 订单编号
 *     mainOrderNo: string, // 主订单编号
 *     type: number, // 订单类型 1:送样申请 2:预约检测-工程师操作 3:预约检测-自主操作 4:需求订单
 *     status: number, // 状态 0:草稿 1:待审核 2:待确认 3:待实验 4:实验中 5:待结算 6:已结算 7:已完成 8:已取消 9:已关闭 10:已驳回 11:已退款 12:培训通过 13:培训不通过
 *     experimentName: string, // 实验名称
 *     experimentType: number, // 实验类型 1:培训 2:试测 3:正式
 *     appointForm: string, // 预约申请表
 *     remark: string, // 备注
 *     sampleReturnable: number, // 样品寄回 0:不寄回 1:寄回
 *     returnAddressId: number, // 样品寄回地址
 *     shipStatus: number, // 邮寄状态 0:自带 1:待寄出 2:已寄出 3:已签收 4:已寄回
 *     userId: number, // 用户ID
 *     createTime: string, // 创建时间
 *     updateTime: string, // 更新时间
 *     instrument: {
 *       instrumentId: number, // 设备ID
 *       orderId: number, // 订单ID
 *       startTime: string, // 开始时间
 *       endTime: string, // 结束时间
 *       // ... 其他仪器相关字段
 *     },
 *     consumables: Array<{
 *       id: number,
 *       name: string,
 *       orderId: number,
 *       price: number,
 *       unit: string,
 *       // ... 其他耗材字段
 *     }>,
 *     incrementServices: Array<{
 *       incrementServiceId: number,
 *       name: string,
 *       num: number,
 *       price: number,
 *       unit: string,
 *       // ... 其他增值服务字段
 *     }>,
 *     subOrders: Array<Object> // 子订单列表
 * }
 */
const getOrderDetail = (request) => {
  return http.post("/instrument/mini/order/detail", request);
};

/**
 * 查询仪器在指定日期已预约的时间段
 * @param {Object} request - 查询请求参数
 * @param {number} request.instrumentId - 仪器ID
 * @param {string} request.queryDate - 查询日期 (格式: YYYY-MM-DD)
 * @returns {Promise} 返回已预约的时间段列表（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 数组）:
 * Array<{
 *   orderId: number, // 订单ID
 *   startTime: string, // 开始时间 (ISO 8601格式)
 *   endTime: string, // 结束时间 (ISO 8601格式)
 *   status: number // 订单状态
 * }>
 */
const getInstrumentTimeSlots = (request) => {
  return http.post("/instrument/mini/order/getInstrumentTimeSlots", request);
};

/**
 * 获取用户付款方式
 * @returns {Promise} 返回用户付款方式列表（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 数组）:
 * Array<{
 *   id: number, // 用户ID
 *   userId: number, // 用户ID
 *   name: string, // 用户名称
 *   phoneNumber: string, // 手机号
 *   payType: number, // 付款方式 1:个人账户 2:团队余额 3:团队额度
 *   balance: number, // 余额
 *   discount: number, // 折扣
 *   teamId: number, // 团队ID
 *   teamName: string // 团队名称
 * }>
 */
const getUserPayment = () => {
  return http.post("/instrument/mini/order/getUserPayment");
};

/**
 * 获取节假日列表
 * @param {Object} request - 查询请求参数
 * @param {string} request.startDate - 开始日期 (格式: YYYY-MM-DD)
 * @param {string} request.endDate - 结束日期 (格式: YYYY-MM-DD)
 * @returns {Promise} 返回节假日列表（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 数组）:
 * Array<{
 *   id: number, // 节假日ID
 *   year: number, // 年份
 *   month: number, // 月份
 *   day: number, // 日期
 *   type: number, // 节假日类型 1:节假日 2:工作日 3:正常周末
 *   createTime: string, // 创建时间 (ISO 8601格式)
 *   updateTime: string, // 更新时间 (ISO 8601格式)
 *   status: number, // 状态 1:正常 2:禁用
 *   holidayDate: string // 节假日日期 (格式: YYYY-MM-DD)
 * }>
 */
const getHolidayList = (request) => {
  return http.post("/instrument/system/holiday/list", request);
};

/**
 * 检查用户是否需要培训
 * @param {Object} request - 查询请求参数
 * @param {number} request.instrumentId - 仪器ID
 * @param {number} request.userId - 用户ID
 * @returns {Promise} 返回培训检查结果（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 对象）:
 * {
 *   needTraining: boolean, // 是否需要培训
 *   reason: string // 需要培训的原因
 * }
 */
const checkTrainingNeeded = (request) => {
  return http.post("/instrument/mini/order/checkTrainingNeeded", request);
};

/**
 * 计算预估金额
 * @param {Object} request - 预估金额请求
 * @param {number} request.actualDuration - 实际消耗时长（小时）
 * @param {Array} [request.consumables] - 耗材列表
 * @param {number} request.consumables[].id - 耗材ID
 * @param {string} request.consumables[].name - 耗材名称
 * @param {number} request.consumables[].num - 数量
 * @param {number} request.consumables[].orderId - 订单ID
 * @param {number} request.consumables[].price - 价格
 * @param {string} request.consumables[].unit - 单位
 * @param {Array} [request.incrementServices] - 增值服务列表
 * @param {number} request.incrementServices[].incrementServiceId - 增值服务ID
 * @param {string} request.incrementServices[].name - 增值服务名称
 * @param {number} request.incrementServices[].num - 个数
 * @param {number} request.incrementServices[].price - 价格（元）
 * @param {string} request.incrementServices[].unit - 单位
 * @param {number} request.instrumentId - 仪器ID
 * @param {number} [request.teamId] - 团队ID
 * @param {number} request.type - 订单类型 1:送样申请 2:预约检测-工程师操作 3:预约检测-自主操作
 * @param {number} request.userId - 用户ID
 * @returns {Promise} 返回预估金额结果（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 对象）:
 * {
 *   discountPrice: number, // 折扣金额
 *   discountRate: number, // 折扣率
 *   estimatePrice: number // 预估价格
 * }
 */
const estimatePrice = (request) => {
  return http.post("/instrument/mini/order/estimatePrice", request);
};

/**
 * 生成默认实验名称
 * @param {Object} request - 生成实验名称请求
 * @param {number} request.instrumentId - 仪器ID
 * @param {number} request.userId - 用户ID
 * @returns {Promise} 返回生成的实验名称（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 对象）:
 * {
 *   experimentName: string // 实验名称
 * }
 */
const generateExperimentName = (request) => {
  return http.post("/instrument/mini/order/generateExperimentName", request);
};

/**
 * 获取其他仪器在指定日期已预约的时间段
 * @param {Object} request - 查询请求参数
 * @param {number} request.excludeInstrumentId - 排除的仪器ID（当前仪器）
 * @param {string} request.queryDate - 查询日期 (格式: YYYY-MM-DD)
 * @returns {Promise} 返回其他仪器预约时间段列表（经过请求拦截器处理，直接返回 data 内容）
 *
 * 返回数据结构（直接返回 data 数组）:
 * Array<{
 *   orderId: number, // 订单ID
 *   instrumentId: number, // 仪器ID
 *   instrumentName: string, // 仪器名称
 *   startTime: string, // 开始时间 (ISO 8601格式)
 *   endTime: string, // 结束时间 (ISO 8601格式)
 *   status: number // 订单状态
 * }>
 */
const getOtherInstrumentTimeSlots = (request) => {
  return http.post("/instrument/mini/order/getOtherInstrumentTimeSlots", request);
};

 // 订单列表查询
  const getOrderList = (request) => {
  return http.post('/instrument/mini/order/page ', request)
}

export default {
  createOrder,
  createDemandOrder,
  getOrderDetail,
  getInstrumentTimeSlots,
  getUserPayment,
  getHolidayList,
  getOrderList,
  checkTrainingNeeded,
  estimatePrice,
  generateExperimentName,
  getOtherInstrumentTimeSlots,
};
