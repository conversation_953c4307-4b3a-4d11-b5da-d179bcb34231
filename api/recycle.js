const http = uni.$u.http

export default {

	// 获取回收站列表
	getCloudRecyclePage: (params) => http.post('/instrument/mini/cloud/recycle/page', params),
  
  // 子文件夹列表
  getCloudRecycleSubListPage: (params) => http.post('/instrument/mini/cloud/recycle/subListPage', params),
  
  //批量删除 
  setCloudRecycleBatchDelete: (params) => http.post('/instrument/mini/cloud/recycle/batchDelete', params),
  
  //彻底删除
  setCloudRecycleDelete: (params) => http.post('/instrument/mini/cloud/recycle/delete', params),
  
  //清空回收站
  setCloudRecycleClearAll: (params) => http.post('/instrument/mini/cloud/recycle/clearAll', params),
  
  //恢复
  setCloudRecycleRecovery: (params) => http.post('/instrument/mini/cloud/recycle/recovery', params),
  
  //批量恢复
  setCloudRecycleBatchRecovery: (params) => http.post('/instrument/mini/cloud/recycle/batchRecovery', params),
  
}