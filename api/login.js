const http = uni.$u.http

export default {
  // 获取登陆验证码
  getSmsCode: params => http.post('/instrument/mini/auth/sms/code', params),
  // 获取租户列表
  getAuthValid: (params) => http.post('/instrument/mini/auth/valid', params),

  // 获取验证码
  getCaptcha: (params, config = {}) => http.post('/instrument/mini/auth/getCaptcha', params, {
    ...config,
    header: {
      'content-type': "application/json"
    }
  }),
  // 微信小程序openid
  getWxOpenId: params => http.post('/huayun-parentai/auth/login/applets', params, {
    header: {
      'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  }),
  // 微信小程序注册
  wxRegister: params => http.post('/huayun-parentai/parentchildren/registWeChat', params),

  // #ifdef BRAND_LONGYAN
  // 龙岩外部跳转登录
  getLongyanToken: params => http.get('/huayun-parentai/longyan/loginByToken', {
    params,
    custom: {
      dontRefreshToken: true
    }
  }),
  // #endif

  updateTypeBySex: data => http.post('/huayun-parentai//parentchildren/updateTypeBySex?sex=' + data.sex + '&userId=' +
    data.userId, data),

  // 手机号码登陆

  getLogin: params => http.post('/instrument/mini/auth/login', params),

  updatePassword: data => http.post('/instrument/mini/auth/updatePassword', data),

  // 手机密码
  tenantListByPassword: params => http.post('/instrument/mini/auth/tenantListByPassword', params),

  resetPwd: params => http.post('/instrument/mini/auth/resetPwd', params),

  resetPwdSmsCode: params => http.post('/instrument/mini/auth/resetPwdSmsCode', params),

  //首次登陆修改密码
  setFirstLoginUpdatePwd: params => http.post('/instrument/mini/auth/first/update', params),

  // 微信绑定手机号
  wxBindPhone: params => http.post('/instrument/mini/auth/wechat/bindPhone', params),

  wxLogin: params => http.post('/instrument/mini/auth/wxLogin', params, {
    custom: {
      toast: false // 禁用错误toast提示
    }
  }),
}