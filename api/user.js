const http = uni.$u.http

export default {
	//上传图片
	uploadFile: (params) => http.post('/instrument/system/file/public/upload', {
		params
	}, {
		header: {
			'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
		}
	}),
	getCurrentSemester: params => http.get('/huayun-parentai/anxun-patriarch/chooseCourse/getCurrentSemester', {
		params
	}),

	  // 退出登录
	logout: params => http.post('/instrument/mini/auth/logout', params),

	// 跳转管理员
	toAdmin: params => http.post('/instrument/mini/auth/toAdmin', params),

	// 跳转用户
	toUser: params => http.post('/instrument/mini/auth/toUser', params),

	// 获取用户信息
	getUserInfo: params => http.post('/instrument/mini/user/getDetail', params),
}