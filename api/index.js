const http = uni.$u.http

const getBannerList = (params) => {
	return http.post('/instrument/mini/home/<USER>', params)
}

const getInstrumentList = () => {
	return http.post('/instrument/mini/home/<USER>', {})
}

const getNoticePage = (params) => {
	return http.post('/instrument/mini/home/<USER>', params)
}

const getSystemSettingDetail = (params) => {
	return http.post('/instrument/mini/home/<USER>', params)
}

// 获取系统设置并存储到本地
const fetchAndStoreSystemSettings = async () => {
	try {
		const result = await getSystemSettingDetail()

		if (result) {
			// 尝试多种可能的数据结构
			let data = result.data || result
			const { financeQrCode, seekQrCode, homeNotice } = data

			// 存储二维码到本地
			if (financeQrCode) {
				uni.setStorageSync('financeQrCode', financeQrCode)
			}
			if (seekQrCode) {
				uni.setStorageSync('seekQrCode', seekQrCode)
			}

			return { financeQrCode, seekQrCode, homeNotice }
		}
		return null
	} catch (error) {
		console.error('获取系统设置失败:', error)
		return null
	}
}

export default {
	getBannerList,
	getInstrumentList,
	getNoticePage,
	getSystemSettingDetail,
	fetchAndStoreSystemSettings
}