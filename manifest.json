{
    "name" : "松山湖材料实验室检测中心",
    "appid" : "__UNI__7DCF1D0",
    "description" : "松山湖材料实验室检测中心",
    "versionName" : "1.0",
    "versionCode" : 2200,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : false,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Push" : {},
            "VideoPlayer" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"
                ],
                "permissionExternalStorage" : {
                    "request" : "always",
                    "prompt" : "应用需要访问相册来选择头像图片，请允许。"
                },
                "permissionCamera" : {
                    "request" : "always",
                    "prompt" : "应用需要使用相机来拍摄头像，请允许。"
                },
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码使用权限，请允许。"
                },
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "autoSdkPermissions" : false
            },
            /* ios打包配置 */
            "ios" : {
                "privacyDescription" : {
                    "NSMicrophoneUsageDescription" : "请在iPhone的”设置-隐私-麦克风“选项中，允许访问你的手机麦克风。",
                    "NSLocalNetworkUsageDescription" : "您可以在“设置”中为此App打开无线局域网。",
                    "NSPhotoLibraryUsageDescription" : "读取相册信息进行个人信息头像修改。",
                    "NSCameraUsageDescription" : "拍照进行个人信息头像修改"
                },
                "idfa" : false,
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "speech" : {
                    "ifly" : {}
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxe8a8ccc633000090",
                        "UniversalLinks" : ""
                    }
                },
                "push" : {},
                "geolocation" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "useOriginalMsgbox" : true
            }
        },
        "nativePlugins" : {}
    },
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "lazyCodeLoading" : "requiredComponents",
        "appid" : "wx0a9998706208fe66",
        "setting" : {
            "urlCheck" : true,
            "es6" : true,
            "postcss" : true,
            "minified" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "__usePrivacyCheck__" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "title" : "松山湖材料实验室检测中心",
        "template" : "index.html",
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "async" : {
            "loading" : "AsyncLoading",
            "error" : "AsyncError",
            "delay" : 200,
            "timeout" : 60000
        },
        "devServer" : {
            "port" : 8080,
            "disableHostCheck" : true
        },
        "publicPath" : "./",
        "sdkConfigs" : {},
        "optimization" : {
            "prefetch" : true,
            "preload" : true,
            "treeShaking" : {
                "enable" : true
            }
        },
        "uniStatistics" : {
            "enable" : false
        }
    }
}
