<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约申请单</title>
    <script src="./jweixin-1.6.0.js"></script>
    <!-- UEditor 富文本编辑器 -->
    <script type="text/javascript" charset="utf-8" src="ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="lang/zh-cn/zh-cn.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 隐藏页面级别的滚动条 */
        }
        
        /* 底部按钮样式 - 学习reserve.vue */
        .bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            padding: 10px 10px 30px 10px; /* 20rpx = 10px, 60rpx = 30px */
            background-color: #ffffff;
            box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05); /* -2rpx = -1px, 10rpx = 5px */
            justify-content: space-between;
            z-index: 3;
        }

        .cancel-btn,
        .clear-btn,
        .save-btn {
            flex: 1;
            height: 44px; /* 88rpx = 44px */
            line-height: 44px;
            text-align: center;
            border-radius: 6px; /* 12rpx = 6px */
            font-size: 16px; /* 32rpx = 16px */
            margin: 0 5px; /* 10rpx = 5px */
            font-weight: bold;
            border: none;
            cursor: pointer;
        }

        .cancel-btn {
            background-color: #ffffff;
            color: #4e5969;
            border: 1px solid #e5e6eb;
        }

        .cancel-btn:hover {
            background-color: #f5f5f5;
        }

        .clear-btn {
            background-color: #fff;
            color: #26d1cb;
            border: 1px solid #26d1cb;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .clear-btn:hover {
            background-color: #f0fffe;
        }

        .save-btn {
            background-color: #26d1cb;
            color: #fff;
            border: none;
        }

        .save-btn:hover {
            background-color: #1fb5b0;
        }

        .save-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        


        .editor-container {
            flex: 1;
            background: white;
            margin: 0 0 100px 0; /* 底部留出按钮空间 */
            padding: 0;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 容器本身不显示滚动条 */
        }

        .editor {
            width: 100%;
            min-height: 400px;
            border: 1px solid #e5e5e5;
        }
        
        .template-section {
            background: white;
            margin: 0 20px 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .template-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .template-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .template-btn {
            background: #f0f0f0;
            border: 1px solid #ddd;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .template-btn:hover {
            background: #e0e0e0;
            border-color: #26D1CB;
        }
        
        .status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }


        
        /* 隐藏 UEditor 的工具栏和其他界面元素 */
        .edui-default .edui-toolbar {
            display: none !important;
        }
        
        .edui-default .edui-editor-toolbarbox {
            display: none !important;
        }
        
        .edui-default .edui-editor-bottomContainer {
            display: none !important;
        }
        
        .edui-default .edui-editor-wordcount {
            display: none !important;
        }
        
        .edui-default .edui-editor-scalelayer {
            display: none !important;
            height: 0 !important;
            overflow: hidden !important;
        }
        
        /* 强制隐藏所有可能的缩放层 */
        div[id*="scalelayer"] {
            display: none !important;
            height: 0 !important;
            position: absolute !important;
            left: -9999px !important;
        }
        
        /* 让UEditor容器占满剩余高度，只在内容区域启用滚动 */
        .edui-default {
            height: 100% !important;
            overflow: hidden !important; /* 外层不显示滚动条 */
        }
        
        .edui-default .edui-editor-iframeholder {
            height: 100% !important;
            overflow: hidden !important; /* iframe容器不显示滚动条 */
        }
        
        .edui-default .edui-editor-body {
            height: 100% !important;
            overflow: hidden !important; /* 编辑器body不显示滚动条 */
        }
        
        .edui-default iframe {
            height: 100% !important;
        }
    </style>
</head>
<body>



    <div class="editor-container">
        <script id="editor" type="text/plain" style="width:100%;height:100%;"></script>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
        <button class="cancel-btn" onclick="handleCancel()">取消</button>
        <button class="clear-btn" onclick="resetContent()">清空</button>
        <button class="save-btn" onclick="saveContent()">保存</button>
    </div>

    <div class="status" id="status"></div>

    <script>
        let ue;
        let params = {};
        let hasChanges = false;
        let isMiniProgram = false;
        
        // 解析URL参数
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const result = {};
            for (const [key, value] of urlParams) {
                try {
                    // 尝试解码，如果失败则使用原始值
                    result[key] = decodeURIComponent(value);
                } catch (e) {
                    console.warn('URL参数解码失败，使用原始值:', key, value);
                    result[key] = value;
                }
            }
            console.log('解析的URL参数:', result);
            return result;
        }
        
        // 初始化
        function init() {
            params = getUrlParams();
            console.log('页面参数:', params);

            // 检查是否在小程序环境
            if (typeof wx !== 'undefined' && wx.miniProgram) {
                wx.miniProgram.getEnv(function(res) {
                    isMiniProgram = res.miniprogram;
                    console.log('是否在小程序环境:', isMiniProgram);

                    if (isMiniProgram) {
                        // 通知小程序页面已准备就绪
                        wx.miniProgram.postMessage({
                            data: { type: 'ready' }
                        });
                    }
                });
            } else {
                // 尝试另一种检测方式
                try {
                    if (window.__wxjs_environment === 'miniprogram') {
                        isMiniProgram = true;
                        console.log('通过 __wxjs_environment 检测到小程序环境');
                    }
                } catch (e) {
                    console.log('不在小程序环境中');
                }
            }

            // 设置页面标题
            if (params.instrumentName) {
                document.title = params.instrumentName + ' - 申请单';
            }

            // 初始化编辑器
            initEditor();
        }
        
        // 初始化 UEditor 编辑器
        function initEditor() {
            try {
                if (typeof UE === 'undefined') {
                    throw new Error('UEditor is not loaded');
                }

                // 配置UEditor
                window.UEDITOR_CONFIG = window.UEDITOR_CONFIG || {};
                window.UEDITOR_CONFIG.serverUrl = '';
                window.UEDITOR_CONFIG.UEDITOR_HOME_URL = './';
                window.UEDITOR_CONFIG.toolbars = [[]]; // 空工具栏，隐藏所有按钮

                ue = UE.getEditor('editor', {
                    initialFrameHeight: '100%',
                    initialFrameWidth: '100%',
                    autoHeightEnabled: false,   // 禁用自动高度，保持固定高度
                    elementPathEnabled: false,  // 隐藏元素路径
                    wordCount: false,           // 隐藏字数统计
                    maximumWords: 50000,
                    placeholder: '请填写预约申请单内容...',
                    toolbars: [[]],            // 确保工具栏为空
                    menubar: false,            // 隐藏菜单栏
                    enableContextMenu: false,  // 禁用右键菜单
                    enableAutoSave: false,     // 禁用自动保存提示
                    scaleEnabled: false,       // 禁用缩放
                    retainOnlyLabelPasted: false,
                    pasteplain: false,
                    filterTxtRules: function () {
                        return false;
                    }
                });

                // 监听编辑器准备就绪
                ue.ready(function() {
                    console.log('UEditor 编辑器初始化成功');

                    // 强制移除缩放层
                    setTimeout(function() {
                        var scaleLayers = document.querySelectorAll('div[id*="scalelayer"]');
                        scaleLayers.forEach(function(layer) {
                            if (layer && layer.parentNode) {
                                layer.parentNode.removeChild(layer);
                            }
                        });
                        console.log('已移除缩放层');
                    }, 100);

                    // 编辑器准备就绪后设置内容
                    setInitialContent();

                    // 监听内容变化
                    ue.addListener('contentChange', function() {
                        hasChanges = true;
                        showStatus('内容已修改');

                        // 通知小程序内容发生变化
                        if (isMiniProgram) {
                            wx.miniProgram.postMessage({
                                data: {
                                    type: 'contentChange',
                                    hasUnsavedChanges: true
                                }
                            });
                        }
                    });
                }); 

            } catch (error) {
                console.error('UEditor 编辑器初始化失败:', error);
                // 使用简单的 textarea 作为备用方案
                initSimpleEditor();
            }
        }

        // 简单编辑器备用方案
        function initSimpleEditor() {
            const editorContainer = document.getElementById('editor').parentNode;
            editorContainer.innerHTML = `
                <textarea
                    id="simpleEditor"
                    placeholder="请填写预约申请单内容..."
                    style="width: 100%; height: 400px; border: 1px solid #ccc; padding: 10px; font-size: 14px; resize: vertical;"
                ></textarea>
            `;

            const textarea = document.getElementById('simpleEditor');
            textarea.addEventListener('input', function() {
                hasChanges = true;
                showStatus('内容已修改');

                // 通知小程序内容发生变化
                if (isMiniProgram) {
                    wx.miniProgram.postMessage({
                        data: { type: 'contentChange' }
                    });
                }
            });

            // 创建一个兼容的 ue 对象
            ue = {
                getContent: function() {
                    return textarea.value.replace(/\n/g, '<br>');
                },
                setContent: function(content) {
                    textarea.value = content.replace(/<br>/g, '\n').replace(/<[^>]*>/g, '');
                },
                ready: function(callback) {
                    callback();
                }
            };

            // 简单编辑器初始化完成后设置内容
            setTimeout(() => {
                setInitialContent();
            }, 100);

            console.log('简单编辑器初始化成功');
        }

        // 设置初始内容
        function setInitialContent() {
            let content = '';
            
            if (params.content && params.content.trim()) {
                content = params.content;
                console.log('设置初始内容:', content);
            } else {
                // 默认内容：一个表格显示"暂无预约单"
                content = `
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0; border: 1px solid #ddd;">
                        <thead>
                            <tr style="background-color: #f5f5f5;">
                                <th style="border: 1px solid #ddd; padding: 15px; text-align: center; font-size: 16px; color: #666;">
                                    预约申请单
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 30px; text-align: center; font-size: 14px; color: #999;">
                                    暂无预约单内容，请点击上方工具栏开始编辑
                                </td>
                            </tr>
                        </tbody>
                    </table>
                `;
                console.log('设置默认内容');
            }
            
            try {
                // 确保编辑器已经完全初始化
                if (ue && typeof ue.setContent === 'function') {
                    ue.setContent(content);
                    console.log('内容设置成功');
                    // 重置变更标记，因为这是初始内容，不算用户修改
                    hasChanges = false;
                } else {
                    console.error('编辑器未正确初始化');
                    // 如果编辑器还没准备好，再等一会儿重试
                    setTimeout(() => {
                        setInitialContent();
                    }, 200);
                }
            } catch (error) {
                console.error('设置内容失败:', error);
            }
        }


        // 重置内容为初始content
        function resetContent() {
            if (confirm('确定要重置为初始内容吗？')) {
                setInitialContent();
                showStatus('内容已重置');
            }
        }

        // 取消按钮处理
        function handleCancel() {
            if (hasChanges) {
                const result = confirm('您有未保存的更改，确定要离开吗？');
                if (result) {
                    // 用户确认离开
                    hasChanges = false; // 重置标记，避免再次提示
                    if (isMiniProgram && typeof wx !== 'undefined' && wx.miniProgram) {
                        wx.miniProgram.navigateBack();
                    } else {
                        window.history.back();
                    }
                }
            } else {
                // 没有未保存更改，直接返回
                if (isMiniProgram && typeof wx !== 'undefined' && wx.miniProgram) {
                    wx.miniProgram.navigateBack();
                } else {
                    window.history.back();
                }
            }
        }

        // 保存内容
        function saveContent() {
            console.log('🔴 保存按钮被点击');
            console.log('🔍 当前小程序环境:', isMiniProgram);
            console.log('🔍 wx对象存在:', typeof wx !== 'undefined');
            console.log('🔍 wx.miniProgram存在:', typeof wx !== 'undefined' && wx.miniProgram);
            
            let content = '';
            if (ue && typeof ue.getContent === 'function') {
                content = ue.getContent();
                console.log('📝 获取到的内容长度:', content.length);
            } else {
                console.error('❌ 编辑器未初始化，无法获取内容');
                alert('编辑器未初始化，无法获取内容');
                return;
            }

            console.log('💾 保存内容:', content.substring(0, 100) + '...');
            
            if (isMiniProgram && typeof wx !== 'undefined' && wx.miniProgram) {
                console.log('📱 在小程序环境中，发送消息给小程序');
                try {
                    // 发送postMessage
                    wx.miniProgram.postMessage({
                        data: { 
                            type: 'save', 
                            content: content,
                            timestamp: Date.now()
                        }
                    });
                    console.log('✅ 消息发送成功，准备触发返回来接收消息');
                    showStatus('保存成功，即将返回...');
                    
                    // 等待短暂时间后主动触发返回，这会触发小程序的@message事件
                    setTimeout(() => {
                        console.log('🔙 主动触发返回操作以接收postMessage');
                        wx.miniProgram.navigateBack();
                    }, 1000);
                    
                } catch (error) {
                    console.error('❌ 发送消息失败:', error);
                    alert('保存失败：' + error.message);
                }
            } else {
                console.log('🌐 非小程序环境，保存到本地存储');
                // 非小程序环境的处理
                localStorage.setItem('appointForm', content);
                alert('内容已保存到本地存储');
            }
            
            hasChanges = false;
        }
        
        // 显示状态信息
        function showStatus(message) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';

            setTimeout(() => {
                status.style.display = 'none';
            }, 2000);
        }


        
        // 页面离开前检查
        window.addEventListener('beforeunload', function(e) {
            if (hasChanges) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
            }
        });

        // 小程序环境下的返回处理
        // 由于web-view的限制，返回拦截主要在小程序端处理
        // H5页面只负责通知小程序当前的未保存状态
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
</script>
</body>
</html>
