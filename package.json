{"name": "hw-material-reservation", "version": "1.0.0", "description": "松山湖材料实验室检测中心预约系统", "scripts": {"dev:h5": "echo '🚀 启动H5开发服务器...' && echo '📖 请使用HBuilderX运行到浏览器' && echo '🌐 或者访问: http://localhost:8080' && echo '💡 建议使用HBuilderX进行uni-app开发'", "serve:h5": "echo '⚠️  此项目为uni-app项目，建议使用HBuilderX开发工具'", "build:h5": "echo '⚠️  此项目为uni-app项目，建议使用HBuilderX进行构建'", "serve": "echo '⚠️  此项目为uni-app项目，建议使用HBuilderX开发工具'", "info": "echo '📱 这是一个uni-app项目' && echo '🛠️  推荐使用HBuilderX开发工具' && echo '🌐 支持H5、小程序、App等多端开发' && echo '📖 使用方法：' && echo '   1. 下载安装HBuilderX' && echo '   2. 打开项目' && echo '   3. 点击运行 -> 运行到浏览器'"}, "dependencies": {"crypto-js": "^4.1.1", "js-base64": "^3.0.0", "uview-ui": "^2.0.30"}, "uni-app": {"scripts": {"huayun-mp-weixin-dev": {"title": "华云-微信小程序-开发环境", "browser": "", "env": {"UNI_PLATFORM": "mp-weixin", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_DEV": true, "BRAND_HUAYUN": true}}, "huayun-mp-weixin-test": {"title": "华云-微信小程序-测试环境", "browser": "", "env": {"UNI_PLATFORM": "mp-weixin", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_TEST": true, "BRAND_HUAYUN": true}}, "huayun-mp-weixin-pre": {"title": "华云-微信小程序-预生产环境", "browser": "", "env": {"UNI_PLATFORM": "mp-weixin", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_PRE": true, "BRAND_HUAYUN": true}}, "huayun-mp-weixin-cloud": {"title": "华云-微信小程序-演示环境", "browser": "", "env": {"UNI_PLATFORM": "mp-weixin", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_CLOUD": true, "BRAND_HUAYUN": true}}, "huayun-mp-weixin-apass": {"title": "华云-微信小程序-apaas环境", "browser": "", "env": {"UNI_PLATFORM": "mp-weixin", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_APAAS": true, "BRAND_HUAYUN": true}}, "huayun-mp-weixin-prod": {"title": "华云-微信小程序-生产环境", "browser": "", "env": {"UNI_PLATFORM": "mp-weixin", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_PROD": true, "BRAND_HUAYUN": true}}, "huayun-h5-dev": {"title": "华云-H5-开发环境", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_DEV": true, "BRAND_HUAYUN": true}}, "huayun-h5-test": {"title": "华云-H5-测试环境", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_TEST": true, "BRAND_HUAYUN": true}}, "huayun-h5-pre": {"title": "华云-H5-预生产环境", "browser": "", "env": {"UNI_PLATFORM": "h5", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_PRE": true, "BRAND_HUAYUN": true}}, "huayun-h5-cloud": {"title": "华云-H5-演示环境", "browser": "", "env": {"UNI_PLATFORM": "h5", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_CLOUD": true, "BRAND_HUAYUN": true}}, "huayun-h5-apaas": {"title": "华云-H5-apaas环境", "browser": "", "env": {"UNI_PLATFORM": "h5", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_APAAS": true, "BRAND_HUAYUN": true}}, "huayun-h5-prod": {"title": "华云-H5-生产环境", "browser": "", "env": {"UNI_PLATFORM": "h5", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"ENV_PROD": true, "BRAND_HUAYUN": true}}, "huayun-app": {"title": "华云-APP", "browser": "", "env": {"UNI_PLATFORM": "app", "APP_NAME": "松山湖材料实验室检测中心", "MP_WEIXIN_APPID": "wx0a9998706208fe66"}, "define": {"BRAND_HUAYUN": true}}}}, "devDependencies": {"@vue/cli-service": "^5.0.8", "webpack": "^5.100.0", "webpack-dev-server": "^5.2.2"}}