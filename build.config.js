const fs = require('fs')

function loge() {
  console.error('================================================')
  console.error('*  ', ...arguments)
  console.error('================================================')
}

function configManifest(scriptEnv = process.UNI_SCRIPT_ENV) {
  // 根据package.json scripts定义的编译条件修改manifest.json
  // ['name', '应用名称']
  // ['mp-weixin.appid', 'wx0a9998706208fe66']
  const replaceList = [
    ['name', scriptEnv.APP_NAME],
    ['description', `${scriptEnv.APP_NAME}`],
    ['mp-weixin.appid', scriptEnv.MP_WEIXIN_APPID]
  ]

  const manifestPath = process.env.UNI_INPUT_DIR + '/manifest.json'
  let manifestText = fs.readFileSync(manifestPath, {
    encoding: 'utf-8'
  })

  function setValue(path, value) {
    const arr = path.split('.')
    const len = arr.length
    const lastItem = arr[len - 1]

    let i = 0
    let manifestArr = manifestText.split(/\n/)
    let changed = false

    for (let index = 0; index < manifestArr.length; index++) {
      const item = manifestArr[index]
      if (new RegExp(`"${arr[i]}"`).test(item)) ++i;
      if (i === len) {
        manifestArr[index] = item.replace(new RegExp(`("${lastItem}"[\\s\\S]*:)([\\s\\S]*)`), (m, g1, g2) => {
          g2 = g2.trim()
          const hasComma = g2.endsWith(',')
          changed = hasComma ? value != g2.substring(0, g2.length - 1) : value != g2
          return g1 + ' ' + value + (hasComma ? ',' : '')
        })
        break;
      }
    }

    manifestText = manifestArr.join('\n')
    return changed;
  }

  let changed = false

  replaceList.forEach(it => {
    changed = setValue(it[0], typeof it[1] == 'string' ? `"${it[1]}"` : it[1]) || changed
  })

  if (changed) {
    fs.writeFileSync(manifestPath, manifestText, {
      "flag": "w"
    })
    loge('manifest.json已修改，请重新编译')
    process.exit(0)
  }

}

function configApp() {
  const configPath = process.env.UNI_INPUT_DIR + '/config'

  const varPath = configPath + '/app/var.js'
  const varText = fs.readFileSync(varPath, {
    encoding: 'utf-8'
  })
  const env = /\/\/\s*env\s*=\s*([A-Z_]+)\s*/.exec(varText)?.[1]
  const brand = /\/\/\s*brand\s*=\s*([A-Z_]+)\s*/.exec(varText)?.[1]
  if (!env) {
    loge('/config/app/var.js中未设置[env=ENV_XXX]')
    process.exit(0)
  }
  if (!brand) {
    loge('/config/app/var.js中未设置[brand=BRAND_XXX]')
    process.exit(0)
  }
  console.log(`已设置env=${env}`)
  console.log(`已设置brand=${brand}`)

  const h5mpPath = configPath + '/h5mp/' + brand.replace(/.*_/, '').toLocaleLowerCase() + '.js'
  const h5mpText = fs.readFileSync(h5mpPath, {
    encoding: 'utf-8'
  })
  if (!h5mpText.includes(env)) {
    loge(`${h5mpPath}中不存在${env}配置`)
    process.exit(0)
  }
  const genCode = h5mpText.replaceAll(env, '1').replace(/ENV_[A-Z]*/g, '0')

  const appPath = configPath + '/app/index.js'
  const curCode = fs.readFileSync(appPath, {
    encoding: 'utf-8'
  })

  if (genCode != curCode) {
    fs.writeFileSync(appPath, genCode, {
      "flag": "w"
    })
    console.log('/config/app/index.js已更新')
  } else {
    console.log('/config/app/index.js无须更新')
  }

  const packagePath = process.env.UNI_INPUT_DIR + '/package.json'
  const packageText = fs.readFileSync(packagePath, {
    encoding: 'utf-8'
  })
  const scripts = JSON.parse(packageText)['uni-app']['scripts']
  const scriptEnv = Object.values(scripts).find(it => it?.env?.UNI_PLATFORM == 'app' && it?.define?.[brand] == true)?.env
  if (!scriptEnv) {
    loge(`未能在package.json中匹配到对就的配置 "UNI_PLATFORM": "app", "${brand}": true`)
    process.exit(0)
  }
  configManifest(scriptEnv)
}


console.log('================================================')
console.log('=', process.env.UNI_APP_NAME, process.env.VUE_APP_PLATFORM,
  process.UNI_SCRIPT_DEFINE && Object.keys(process.UNI_SCRIPT_DEFINE)
  .filter(key => process.UNI_SCRIPT_DEFINE[key]).join(' ') || ''
)
console.log('================================================')

if (process.env.VUE_APP_PLATFORM != 'app-plus') {
  if (!process.UNI_SCRIPT_ENV) {
    loge('未选择编译环境')
    process.exit(0)
  }
  configManifest()
} else {
  configApp()
}