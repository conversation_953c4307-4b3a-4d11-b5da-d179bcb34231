/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
 @import 'uview-ui/theme.scss';
 
 $u-primary: #1CBE83;
 $u-primary-dark: #16B078;
 $u-primary-disabled: #8DEDC8;
 $u-primary-light: #ecf5ff;
 

 /* 蓝青UI规范 */ 
/* 颜色 */
$lq-fontFamily:PingFangSC-Regular, PingFang SC;
$lq-color-primary:#1CBE83;             // 主色
$lq-color-primary-keydown:#16B078;     // 主色-按下
$lq-color-primary-disabled:#8DEDC8;    // 主色-禁用 
$lq-color-warning:#ff7d00;             // 辐色-警告
$lq-color-success:#00bf60;             // 辐色-成功
$lq-color-error:#f53f3f;               // 辐色-失败
$lq-color-title:#121212;               // 标题，正文
$lq-color-text:#555555;                // 次级文字
$lq-color-assist:#86909c;              // 辅组文字
$lq-color-line:#eeeeee;                // 分割线
$lq-color-bg:#f7f7f7;                  // 背景色
$lq-color-write:#fff;                  // 白色

/* 中性色 */
$lq-color-neutral-1: #121212;          // 深色文字
$lq-color-neutral-2: #555555;          // 次级文字
$lq-color-neutral-3: #86909c;          // 辅助文字
$lq-color-neutral-4: #c9cdd4;          // 浅色文字、分割线

/* 布局 */
$lq-border-radius-lg:20rpx;            // 圆角
$lq-border-radius-base:16rpx;          
$lq-border-radius-sm:12rpx;            
$lq-size-base:30rpx;                   // 边距大小 

/* 字体大小 */ 
$lq-font-size-title-lg:38rpx;          // 特大号标题
$lq-font-size-title-base:36rpx;        // 导航栏、大标题
$lq-font-size-title-sm:32rpx;          // 小标题
$lq-font-size-text-base:30rpx;         // 正文
$lq-font-size-text-sub:26rpx;          // 次级文字
$lq-font-size-text-assist:24rpx;       // 辅助文字
$lq-font-size-text-tab:22rpx;          // 标签栏文字 

.use-navbar{
	//#ifdef MP-WEIXIN
	padding-right: 200rpx !important;
	//#endif
}