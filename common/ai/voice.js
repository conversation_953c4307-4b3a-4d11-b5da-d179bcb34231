import {
	resolveUrl
} from './url'



let activeVoice = null

export const startVoice = (options) => {
	const control = {
		state: '',
	}
	let stopReason = ''
	let httpTask = null

	const recorder = uni.getRecorderManager()

	const clean = () => {
		if (activeVoice == control) {
			activeVoice = null
		}
		control.state = ''
		httpTask?.abort()
		httpTask = null
	}

	const onStart = () => {
		console.log('voice start')
		control.state = 'recording'
		options?.onStart?.()
	}

	const onStop = (res) => {
		console.log('voice stop', stopReason, res)
		if (stopReason == 'cancel') {
			clean()
			options?.onCancel?.()
			return
		}
		(async () => {
			// const token = (await getAuth()).token
			const token = uni.getStorageSync("token")
			if (!control.state) {
				return
			}
			control.state = 'transcription'
			httpTask = uni.uploadFile({
				url: resolveUrl('/instrument/mini/chat/huawei/cloud/audio/transcriptions'),
				filePath: res.tempFilePath,
				name: 'file',
				header: {
					Authorization: uni.getStorageSync("token")
				},
				formData: {
					metadata: {
						duration: res.duration / 1000
					},
					language: 'zh',
					prompt: '以下是普通话'
				},
				success: res => {
					console.log('voice input result', res.data)
					clean()
					if (stopReason == 'cancel') {
						options?.onCancel()
					} else {
						options?.onResult?.(JSON.parse(res.data).data)
					}
				},
				fail: err => {
					console.log('voice input transcription error', err)
					clean()
					if (stopReason === 'cancel') {
						options?.onCancel()
					} else {
						options?.onError(err)
					}
				}
			})
		})().catch(err => {
			console.log('voice catch', err)
			clean()
		})
	}

	const onError = err => {
		console.log('voice error', err)
		if (err?.errMsg?.includes('NotFoundError')) {
			uni.$u.toast('未检测到麦克风')
		} else if (err?.errMsg?.includes('not declared in the privacy agreement')) {
			uni.$u.toast('未添加录音权限')
		} else {
			uni.$u.toast('录音错误')
		}
		clean()
		options?.onError?.(err)
	}

	recorder.onStart(onStart)
	recorder.onStop(onStop)
	recorder.onError(onError)

	control.stop = (reason) => {
		if (!control.state) {
			return
		}
		stopReason = reason
		recorder.stop()
		if (reason == 'cancel') {
			clean()
		}
	}

	activeVoice?.stop('cancel')
	activeVoice = control

	control.state = 'starting'
	recorder.start({
		duration: 600000,
		sampleRate: 8000,
		format: 'wav',
	})

	return control
}

export const stopVoice = (reason = '') => {
	activeVoice?.stop(reason)
}