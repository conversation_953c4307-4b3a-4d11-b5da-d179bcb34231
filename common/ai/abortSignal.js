export default function AbortSignal() {
  const signal = {
    aborted: false,
    reason: '',
    listeners: []
  }

  signal.addEventListener = listener => {
    listener && !signal.listeners.includes(listener) && signal.listeners.push(listener)
  }

  signal.removeEventListener = listener => {
    const index = signal.listeners.indexOf(listener)
    if (index >= 0) {
      signal.listeners.splice(index, 1)
    }
  }

  signal.abort = reason => {
    if (signal.aborted) {
      return
    }
    signal.aborted = true
    signal.reason = reason
    signal.listeners.forEach(it => it(reason))
    signal.listeners = []
  }

  return signal
}
