import {
  resolveUrl
} from "./url"



let activeTts = null

export const startTts = (options) => {
  const control = {
    state: ''
  }
  let httpTask = null
  let audioContext = null

  const clean = () => {
    if (activeTts == control) {
      activeTts = null
    }
    control.state = ''
    httpTask?.abort()
    httpTask = null
    audioContext?.stop()
    audioContext?.destroy()
    audioContext = null
  }

  const getAudioUrl = (buffer) => {
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager()
      const dirPath = `${wx.env.USER_DATA_PATH}/chatTts`
      const filename = `${Date.now()}.mp3`
      const filePath = `${dirPath}/${filename}`
      try {
        fs.mkdirSync(dirPath, true)
      } catch {}
      fs.writeFile({
        filePath,
        data: buffer,
        encoding: 'binary',
        success: () => {
          resolve(filePath)
        },
        fail: err => {
          console.log('ai tts save file error', err)
          reject(err)
        }
      })
      fs.readdir({
        dirPath,
        success: (res) => {
          res?.files?.forEach(it => {
            if (it != filename) {
              fs.unlink({
                filePath: `${dirPath}/${it}`
              })
            }
          })
        }
      })
    })
    // #endif

    // #ifdef H5
    const blob = new Blob([buffer], {
      type: 'audio/mp3'
    })
    return Promise.resolve(URL.createObjectURL(blob))
    // #endif
  }

  const playAudio = async (buffer) => {
    let url = null
    try {
      url = await getAudioUrl(buffer)
    } catch {
      clean()
      return
    }
    if (control.state != 'starting') {
      return
    }
    audioContext = uni.createInnerAudioContext()
    audioContext.src = url
    audioContext.play()
    audioContext.onPlay(() => {
      if (!control.state) {
        return
      }
      console.log('ai tts playAudio onPlay')
      control.state = 'playing'
    })
    audioContext.onStop(() => {
      if (!control.state) {
        return
      }
      console.log('ai tts playAudio onStop')
      clean()
    })
    audioContext.onEnded(() => {
      if (!control.state) {
        return
      }
      console.log('ai tts playAudio onEnded')
      clean()
    })
    audioContext.onError(err => {
      if (!control.state) {
        return
      }
      console.log('ai tts playAudio onError', err)
      clean()
    })
  }

  const getAudioData = (token) => {
    if (control.state != 'starting') {
      return
    }
	console.log(options)
    httpTask = uni.request({
      url: resolveUrl('/instrument/mini/chat/item/speech'),
      method: 'POST',
      responseType: 'arraybuffer',
      header: {
          Authorization : uni.getStorageSync("token")
      },
      data: {
        input: options.input,
        appId:options.appId,
        ttsConfig: {
          model: 'tts-1',
          type: 'model',
          voice: 'alloy'
        },
		chatItemId:options.chatItemId
      },
      success: res => {
        console.log('ai tts data success')
        playAudio(res.data)
      },
      fail: err => {
        console.log('ai tts data fail', err)
        clean()
      }
    })
  }

  control.stop = () => {
    if (!control.state) {
      return
    }
    console.log('ai tts stop')
    clean()
  }

  activeTts?.stop()
  activeTts = control

  control.state = 'starting'
  // getAuth().then(res => {
    getAudioData(uni.getStorageSync("token"))
  // }).catch(err => {
  //   console.log('ai tts token error', err)
  //   clean()
  // })

  return control
}

export const stopTts = () => {
  activeTts?.stop()
}