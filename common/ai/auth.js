import { resolveUrl } from './url'

const tokenExpireTime = 4 * 3600 * 1000

const auth = {
  token: '',
  appIds: {},
  time: 0
}

export const getAuth = (chatType) => {
  if (auth.token && Date.now() - auth.time < tokenExpireTime &&
    (!chatType || auth.appIds[chatType])) {
    return Promise.resolve(auth)
  }
  return new Promise((resolve, reject) => {
    uni.request({
      url: resolveUrl('/api/thirdlogin/parentAiLogin'),
      success: res => {
        auth.token = res.data.data.aiToken

        const {
          interestAppId,
          parentTipsAppId,
          expertAppId,
          otherAppId
        } = res.data.data

        auth.appIds = {
          '1': interestAppId,
          '2': parentTipsAppId,
          '3': expertAppId,
          '4': otherAppId,
        }

        auth.time = Date.now()
        resolve(auth)
      },
      fail: err => {
        console.log('ai login fail', err)
        reject(err)
      }
    })
  })
}

export const resetAuth = () => {
  auth.token = ''
  auth.appIds = {}
  auth.time = 0
}
