import {
  resolveUrl
} from './url';
import EventSource from '@/common/network/event-source';
import dayjs from '@/common/utils/day.js';
import { handle401Error, is401Error } from '@/common/utils/auth.js';

export const ChatRoleEnum = {
  system: 'System',
  human: 'Human',
  ai: 'AI',
  function: 'Function',
  tool: 'Tool'
};

export const MessageRoleEnum = {
  system: 'system',
  user: 'user',
  assistant: 'assistant',
  function: 'function',
  tool: 'tool'
};

export const SseEventEnum = {
  error: 'error',
  answer: 'answer',
  fastAnswer: 'fastAnswer',
  flowNodeStatus: 'flowNodeStatus',
  toolCall: 'toolCall',
  toolParams: 'toolParams',
  toolResponse: 'toolResponse',
  flowResponses: 'flowResponses',
  updateVariables: 'updateVariables'
};

const chatMessageRoleMap = {
  [ChatRoleEnum.system]: MessageRoleEnum.system,
  [ChatRoleEnum.human]: MessageRoleEnum.user,
  [ChatRoleEnum.ai]: MessageRoleEnum.assistant,
  [ChatRoleEnum.function]: MessageRoleEnum.function,
  [ChatRoleEnum.tool]: MessageRoleEnum.tool,
};

const messageChatRoleMap = {
  [MessageRoleEnum.system]: ChatRoleEnum.system,
  [MessageRoleEnum.user]: ChatRoleEnum.human,
  [MessageRoleEnum.assistant]: ChatRoleEnum.ai,
  [MessageRoleEnum.function]: ChatRoleEnum.function,
  [MessageRoleEnum.tool]: ChatRoleEnum.tool,
};

export function adaptChat2Messages(messages, reserveId = true) {
  // console.log('跟踪200', messages);
  return messages.map(it => ({
    ...(reserveId && {
      dataId: it.dataId
    }),
    content: it.content || '',
    role: chatMessageRoleMap[it.obj]
  }));
}

const requestAnimationFrame = (callback) => {
  setTimeout(callback, 1000 / 60);
};

/* replace sensitive link */
const replaceSensitiveLink = (text) => {
  const urlRegex = /(?<=https?:\/\/)[^\s]+/g;
  return text.replace(urlRegex, 'xxx');
};

const getErrText = (err, def = '') => {
  // console.log('跟踪20', err);
  const msg = typeof err === 'string' ? err : err?.message || def || '';
  msg && console.log('error =>', msg);
  return replaceSensitiveLink(msg);
};

export const fetch = ({
  url = '/instrument/client/chat/completions',
  data,
  onMessage,
  abortSignal
}) => {
  return new Promise((resolve, reject) => {
    let responseText = '';
    let responseQueue = [];
    let errMsg = '';
    let responseData = [];
    let finished = false;

    const timeoutId = setTimeout(() => {
      abortSignal?.abort('Time out');
    }, 120000);

    const finish = () => {
      if (errMsg) {
        return failedFinish();
      }
      resolve({
        responseText,
        responseData
      });
    };

    const failedFinish = (err) => {
			try {
				throw new Error()
			}catch(e) {
				console.log('xxxxxx',e)
			}
      finished = true;
      reject(new Error(getErrText(err, errMsg || '响应过程出现异常~')));
    };

    const isAnswerEvent = (event) => 
      event === SseEventEnum.answer || event === SseEventEnum.fastAnswer;

    const animateResponseText = () => {
      if (abortSignal?.aborted) {
        responseQueue.forEach((item) => {
          onMessage(item);
          if (isAnswerEvent(item.event)) {
            responseText += item.text;
          }
        });
        return finish();
      }

      if (responseQueue.length > 0) {
        const fetchCount = Math.max(1, Math.round(responseQueue.length / 30));
        for (let i = 0; i < fetchCount; i++) {
          const item = responseQueue[i];
          onMessage(item);
          if (isAnswerEvent(item.event)) {
            responseText += item.text;
          }
        }
        responseQueue = responseQueue.slice(fetchCount);
      }

      if (finished && responseQueue.length === 0) {
        return finish();
      }

      requestAnimationFrame(animateResponseText);
    };

    animateResponseText();

    try {
      const variables = data?.variables || {};
      variables.cTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

      const eventSource = new EventSource({
        url: resolveUrl(url),
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          Authorization: uni.getStorageSync("token")
        },
        data: {
          ...data,
          variables,
          detail: true,
          stream: true
        }
      });

      eventSource.onOpen = (res) => {
        clearTimeout(timeoutId);
        if (is401Error(res.statusCode)) {
          handle401Error('登录已过期，请重新登录');
          failedFinish('登录已过期，请重新登录');
          return;
        }
        if (!(res.header['Content-Type'] || res.header['content-type'])?.startsWith('text/event-stream') || res.statusCode !== 200) {
          failedFinish();
        }
      };

      eventSource.onMessage = ({ event, data }) => {
        if (data === '[DONE]') return;

        const parseJson = (() => {
          try {
            return JSON.parse(data);
          } catch (error) {
            return {};
          }
        })();

        if (event === SseEventEnum.answer || event === SseEventEnum.fastAnswer) {
          const text = parseJson?.choices?.[0]?.delta?.content || '';
          if (event === SseEventEnum.answer) {
            for (const item of text) {
              responseQueue.push({ event, text: item });
            }
            const reasoningText = parseJson.choices?.[0]?.delta?.reasoning_content || '';
            
            for (const item of reasoningText) {
              responseQueue.push({
                event,
                reasoningText: item
              });
            }
          } else {
            responseQueue.push({ event, text });
          }
        } else if (
          event === SseEventEnum.toolCall ||
          event === SseEventEnum.toolParams ||
          event === SseEventEnum.toolResponse
        ) {
          responseQueue.push({ event, ...parseJson });
        } else if (event === SseEventEnum.flowNodeStatus) {
          onMessage({ event, ...parseJson });
        } else if (event === SseEventEnum.flowResponses && Array.isArray(parseJson)) {
          responseData = parseJson;
        } else if (event === SseEventEnum.updateVariables) {
          onMessage({ event, variables: parseJson });
        } else if (event === SseEventEnum.error) {
          errMsg = getErrText(parseJson, '流响应错误');
        }
      };

      eventSource.onError = (err) => {
        clearTimeout(timeoutId);
        failedFinish(getErrText(err));
      };

      eventSource.onEnd = () => {
        finished = true;
      };

      abortSignal?.addEventListener(() => eventSource.close());

    } catch (err) {
      clearTimeout(timeoutId);

      if (abortSignal?.aborted) {
        finished = true;
        return;
      }
      console.log(err, 'fetch error');

      failedFinish(err);
    }
  });
};
