	page {
    background: #F8F8F8;
  }
  /*每个页面公共css */
  /*每个页面公共css */
  /* flex布局兼容性写法
  flex：定义布局为盒模型
  flex-v：盒模型垂直布局
  flex-l：盒模型水平布局
  flex-1：子元素占据剩余的空间
  flex-align-center：子元素垂直居中
  flex-pack-center：子元素水平居中
  flex-pack-justify：子元素两端对齐 
  border-box：元素padding不影响
  flex-text：按钮文本居中
  */

  .flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
  
  .flex-v {
    -webkit-box-orient: vertical;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  
  .flex-l {
    -webkit-box-orient: horizontal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  
  .flex-1 {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }
  
  .flex-align-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
  }
  
  .flex-pack-center {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  
  .flex-pack-justify {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
  
  .border-box {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
  }
  
  .flex-text {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  ::v-deep .uni-input-placeholder {
    color: #aaa;
    font-size: 32rpx;
    font-weight: 400;
}

.vertical_line,.line_vertical{
  display: inline-flex;
  width: 2rpx;
  height: 25rpx;
  background-color: #DEDEDE;
  margin: 0 18rpx;
}

.text-overflow-ellipsis-2{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 定义文本的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal; /* 重置white-space */
}


.u-popup{
  .u-popup__content{
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    .u-toolbar{
      padding-top: 20rpx;
      .u-toolbar__wrapper__confirm{
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #3D7FFF!important;
        line-height: 44rpx;
        text-align: right;
        font-style: normal;
      }

    }
    .uni-picker-view-indicator{
      background-color: #f1f1f1;
      color: #05101F;
      margin: 0 20rpx;
      width: calc(100% - 60rpx);
      border-radius: 20rpx;
      z-index: 0;
    }
    

    

    
    
  }
}

.float-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient( 219deg, #1CBE83 0%, #8DEDC8 100%);
  box-shadow: 2rpx 4rpx 10rpx 0rpx rgba(150,150,150,0.56);

  z-index: 999;
  border-radius: 50%;
  position: fixed;
  bottom: 152rpx;
  right: 32rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 26rpx;
  color: #FFFFFF;
  line-height: 33rpx;
  text-align: center;
  font-style: normal;
  white-space: pre-wrap;
}


.lk-calendar{
  .months{
    .day-content_checked {
      width: 60rpx;
      height: 60rpx;
      background: linear-gradient(314deg, #3d7fff 0%, #3da2ff 100%);
      box-shadow: 0rpx 6rpx 7rpx 0rpx rgba(124, 160, 255, 0.25);
      border-radius: 44rpx 44rpx 44rpx 44rpx!important;
      color: #fff !important;
    }
  }


   .my-course__header_box {
    background-color: #fff;
    color: #000;
    // position: fixed;
    // top: 298rpx;
  }

   .header-weekdays-weekday {
    color: rgba(0, 0, 0, 0.6)!important;
  }
  
  .day-content {
    color: #000!important;
  }


   .months .day-content_disable {
    color: rgba(0, 0, 0, 0.6) !important;
  }

   .my-course__class-num_show-time {
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #000000!important;
    display: flex;
    align-items: center;
  }

   .month_box .up_down {
    border-radius: 0;
  }

}

.childrenFile{

  .u-navbar {
  .u-fixed {
    background: linear-gradient(180deg, #1982fe 0%, #5fa8f7 100%);
    z-index: 999;
  }
}
}




.layout-column{
  ::v-deep .u-form-item {      
    .u-form-item__body {
      flex-direction: column !important;
  
      .u-form-item__body__left {
        position: relative;
        margin-bottom: 20rpx !important;
        width: 100%!important;
        .u-form-item__body__left__content__required{
          position: absolute !important;
          left: auto !important; 
          right: -20rpx !important; 
          float: none;
          color: #F53F3F !important;
          line-height: 20px !important;
          font-size: 32rpx !important;
          top: 50% !important;
          transform: translateY(-50%) !important; 
        }
      }
      .u-form-item__body__right {
        width: 100%!important;
      }
    }
    .u-form-item__body__right__message {
      margin-left: 0 !important;
    }
  }
}



::v-deep .u-search {
  .u-search__content {
    background-color: rgba(0, 0, 0, 0.03) !important; 
    border-radius: 8px !important;
    input {
      background-color: initial !important; 
    } 
  }
}

::v-deep .u-button {
  &--primary {
    background: #26D1CB !important;
    color: #fff !important;
    border: none !important;
  }
}

::v-deep .u-tabs__wrapper__nav__line {
  background-color: #26D1CB !important;
}