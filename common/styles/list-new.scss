@mixin list {
  .page-fixed-top {
    position: fixed;
    top: 0;
    background: #fff;
    left: 0;
    padding: 16rpx 32rpx;
    width: 100%;
    z-index: 1;
    box-sizing: border-box;
    .search-header {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
      justify-content: space-between;
      ::v-deep .lk-text-box-content {
        justify-content: flex-start !important;
      }
      .search-input {
        flex: 1;

        margin-left: 20rpx;

        ::v-deep .u-icon__icon {
          font-size: 64rpx !important;
          color: #cecece !important;
        }
      }

      .select-input {
        width: 250rpx;
        margin-left: 20rpx;
      }
    }
  }

  @include second-filter-pane;

  .tab-header {
    background-color: #ffffff;
    border-bottom: 1rpx solid #e7e7e7;
    ::v-deep .u-tabs__wrapper__nav__line {
      bottom: 0px;
    }
  }
  .text-left-list {
    .list-item__bar {
      justify-content: flex-start !important;
    }
  }
  .list {
    padding: 0 30rpx;
    overflow: hidden;
    .info-wrap {
      background: #f5f6f8;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      margin: 0 28rpx;
      padding: 11rpx 22rpx 9rpx 24rpx;
      .line {
        background: #e7e7e7;
        height: 1rpx;
        padding: 0 22rpx;
      }
    }
    &-item {
      background: #ffffff;
      border-radius: 20rpx;
      margin: 24rpx 0;
      overflow: hidden;
      &:nth-child(1) {
        margin-top: 0rpx;
      }
      &__header {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eeeeee;
        padding: 30rpx 30rpx 18rpx 30rpx;
        position: relative;
        .list-item-header-status {
          position: absolute;
          right: 0;
          top: 0;

          border-radius: 0rpx 16rpx 0rpx 16rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          line-height: 40rpx;
          text-align: center;
          font-style: normal;
          padding: 4rpx 24rpx;
          &.primary{
            background: #1CBE83;
            color: #FFFFFF;
          }
        }

        &--border-bottom {
          border-bottom: none;
          padding-bottom: 10rpx;
        }

        &__title {
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 32rpx;
          color: #000000;
          width: 100%;
          line-height: 45rpx;
          text-align: left;
          font-style: normal;
          margin-bottom: 10rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .left{
            max-width: 400rpx;
          }
          .list-item-content-cover {
            width: 96rpx;
            height: 54rpx;
            margin-right: 10rpx;
            border-radius: 8rpx 8rpx 8rpx 8rpx;
          }
          .right {
            .button {
              padding: 10rpx 26rpx;
              background: linear-gradient(314deg, #3d7fff 0%, #3da2ff 100%);
              border-radius: 12rpx 12rpx 12rpx 12rpx;
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 28rpx;
              color: #ffffff;
              line-height: 44rpx;
              text-align: center;
              font-style: normal;
            }
          }
        }

        &__center {
          margin-left: 30rpx;
        }

        &__status {
          margin-left: auto;
          display: flex;
          align-tiems: center;

          &__text {
            font-size: 28rpx;
            line-height: 44rpx;
          }
        }

        &__buttons {
          margin-left: auto;
          display: flex;
          align-items: center;
          min-height: 58rpx;

          ::v-deep .u-button {
            margin: 0 0 0 12rpx;
            width: auto;
            height: auto;
            font-size: 24rpx;
            line-height: 24rpx;
            border-radius: 12rpx;
            padding: 14rpx 22rpx;
          }

          @for $i from 3 through 8 {
            &--#{$i}em {
              ::v-deep .u-button {
                min-width: #{$i}em;
              }
            }
          }
        }
      }

      &__row {
        display: flex;
        align-items: center;
        margin: 0 30rpx;
        overflow: hidden;

        &__divider {
          flex-shrink: 0;
          width: 1px;
          height: 20rpx;
          margin: 0 16rpx;
          background-color: #aaaaaa;
        }
      }

      &__bar {
        display: flex;
        padding: 20rpx 0;
        overflow: hidden;
        justify-content: space-between;
        align-items: center;

        &__label {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #6a6a6d;
          line-height: 39rpx;
          text-align: left;
          font-style: normal;
          min-width: 144rpx;
        }

        &__content {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #05101f;
          line-height: 39rpx;
          text-align: right;
          font-style: normal;
        }
      }

      & > .list-item__bar {
        margin: 0 30rpx;
      }

      & > .list-item__row:first-child,
      & > .list-item__bar:first-child {
        margin-top: 20rpx;
      }

      & > .list-item__row:last-child,
      & > .list-item__bar:last-child {
        margin-bottom: 20rpx;
      }

      &__footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border-top: 1px solid #EEEEEE;
        // margin-top: 10rpx;
        padding: 18rpx 30rpx 30rpx 30rpx;

        .left-status {
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }

        &--border-top {
          border-top: none;
          padding-top: 10rpx;
        }

        &__status {
          font-size: 28rpx;
          line-height: 44rpx;
          color: #333333;
          border-radius: 6rpx 6rpx 6rpx 6rpx;
          padding: 4rpx 16rpx;
        }
        &__buttons {
          margin-left: auto;
          display: flex;
          align-items: center;
          min-height: 58rpx;
        }
        &__buttons,
        .left-status {
          ::v-deep .u-button + .u-button {
            margin-left: 14rpx;
          }
          ::v-deep .u-button.u-button--plain {
            background: #e9f1fe;
            border-color: #e9f1fe !important;
          }
          ::v-deep .u-button {
            width: auto;
            white-space: nowrap;
            height: 64rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 44rpx;
            text-align: center;
            font-style: normal;
            border-radius: 12rpx 12rpx 12rpx 12rpx;
            &.u-button--primary {
              background: linear-gradient(314deg, #3d7fff 0%, #3da2ff 100%);
              color: #ffffff;
            }
            border: none;
            &:last-child {
            }

            &:nth-child(2) {
            }
          }

          @for $i from 3 through 8 {
            &--#{$i}em {
              ::v-deep .u-button {
                min-width: #{$i}em;
              }
            }
          }
        }
      }
    }

    &--scroll {
      overflow: scroll;
    }

    &--wrap {
      flex-wrap: wrap;
      white-space: normal;
    }

    .list-item__row.list--scroll {
      .list-item__bar {
        overflow: visible;
        .list-item__bar__content {
          overflow: visible;
        }
      }
    }

    .list-item__bar.list--scroll {
      .list-item__bar__content {
        overflow: visible;
      }
    }

    &--color-blue {
      color: #3d7fff;
    }

    &--color-green {
      height: 48rpx;
      background: #e3f9e9;
      padding: 0 20rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #2ba471;
      line-height: 48rpx;
      text-align: center;
      font-style: normal;
    }

    &--color-yellow {
      height: 48rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #e37318;
      line-height: 48rpx;
      padding: 0 20rpx;
      text-align: center;
      font-style: normal;
      background: #fff1e9;
    }

    &--color-gray {
      color: #aaaaaa;
    }

    &--color-red {
      color: #f53f3f;
      // width: 152rpx;
      height: 48rpx;
      padding: 0 20rpx;
      background: #fff0ed;
      border-radius: 6rpx 6rpx 6rpx 6rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #d54941;
      line-height: 48rpx;
      text-align: center;
      font-style: normal;
    }
  }
}

@mixin second-filter-pane {
  .second-filter-pane {
    background: #ffffff;
    border-radius: 200rpx 200rpx 200rpx 200rpx;
    margin: 28rpx 32rpx 28rpx 32rpx;
    height: 64rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    ::v-deep .lk-text-box {
      border: none !important;
      .lk-text-box-icon-wrapper {
        font-size: 30rpx !important;
        .u-icon__icon.uicon-close-circle {
          font-size: 30rpx !important;
        }
      }
    }

    .pane {
      width: calc(33.3% - 30rpx);
    }
  }
}
