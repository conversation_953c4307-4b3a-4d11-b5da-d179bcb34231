@mixin form {
  .form-item--top-label {
    ::v-deep .u-form-item__body__left {
      height: 60rpx;
    }
  }

  .editor--readonly-content {
    ::v-deep .u-form-item__body {
      // padding: 0;
    }
  }

  .logo {
    ::v-deep .lk-album {
      width: 154rpx !important;
      height: 154rpx !important;
    }
  }

  .picture-wrap {
    ::v-deep .u-form-item__body {
      padding: 20rpx 0rpx;
      // 图片左靠边
      &__right {
        &__content__slot {
          justify-content: flex-start !important;
        }
      }
    }
  }

  .edit-title {
    padding: 20rpx 0 20rpx 32rpx;
    &:first-child {
      background: #fff;
    }
  }

  .picture-wrap {
    padding: 20rpx 40rpx;
    background: #fff;
  }

  ::v-deep .u-form-item {
    padding-left: 15rpx;
    .lk-text-box {
      border-radius: 0 !important;
    }

    .lk-text-box-content-placeholder {
      font-size: 32rpx !important;
      background: #f8f8f8;
      color: rgb(192, 196, 204) !important;
    }
    .lk-text-box-content-text {
      font-size: 32rpx !important;
    }
    .lk-input-placeholder {
      font-size: 32rpx !important;
    }
    .u-form-item:not(:last-child) {
      border-bottom: 1rpx solid #e7e7e7;
    }

    .u-form-item:last-child {
      border-bottom: none;
    }

    .u-form-item__body {
      $label-width: 228rpx;

      &__left {
        justify-content: flex-start;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;

        color: #3d7fff;
        line-height: 48rpx;
        text-align: left;

        &__content {
          flex: 0;

          &__label {
            flex: 0;
            white-space: nowrap;
            font-size: 32rpx !important;

            span,
            text {
              font-size: 32rpx !important;
            }
          }
        }
      }

      &__right {
        width: 0;
        .lk-text-box,
        .lk-input {
          border: none !important;
          background: #f8f8f8;
        }

        // .lk-text-box-icon-wrapper{
        // 	display: block;
        // }
        &__content__slot {
          width: 100%;

          .static-text {
            padding-right: 15rpx;
            font-size: 32rpx;
            color: #aeaeb2;
            height: 80rpx;
            line-height: 80rpx;

            font-style: normal;
          }
          .readonly-text {
            padding-right: 15rpx;
            font-size: 32rpx;
            color: #05101f;

            font-style: normal;
          }
          .lk-input .u-input {
            border: none !important;
            padding-right: 0 !important;
          }

          .uni-select-cy {
          }


          .lk-input-placeholder{
            font-size: 32rpx !important;
            color:rgb(192, 196, 204);
          }

          .u-textarea {
            background: #f8f8f8;
            border: none !important;
            font-size: 32rpx;
            color: #333333;
          }
          .uni-textarea-placeholder {
            font-size: 32rpx !important;
          }
          .u-textarea__field {
            min-height: 4em;
            font-size: 32rpx;
          }

          .lk-album {
            // 第一个是logo

            width: 280rpx;
            height: 210rpx;
            // border-radius: 8rpx 8rpx 8rpx 8rpx;
            .lk-album-item-box {
              height: 100% !important;
              padding-top: 0 !important;
              border-radius: 8rpx 8rpx 8rpx 8rpx;
              .image-placeholder {
                width: 168rpx;
                height: 92rpx;
                margin-bottom: 56rpx;
              }

              .bottom-tip {
                position: absolute;
                bottom: 0;
                height: 60rpx;
                width: 100%;
                background: rgba(166, 166, 166, 0.5);
                // border-radius: 0rpx 0rpx 8rpx 8rpx;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #ffffff;
                line-height: 44rpx;
                text-align: center;
                font-style: normal;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
          }

          .uni-input-input,
          .lk-text-box-content-text {
            font-weight: 400;
            font-size: 32rpx;
            color: rgba(0, 0, 0, 0.9);
            line-height: 48rpx;

            white-space: pre-wrap !important;
          }
          .uni-textarea-textarea {
            font-weight: 400;
            font-size: 32rpx;
            color: #05101f;
            line-height: 48rpx;
            text-align: left;
          }
          .u-radio-group--row {
            .u-radio {
              margin-right: 20rpx;
            }
          }

          .lk-text-box-content-placeholder,
          .u-input__content__field-wrapper__field {
            font-size: 32rpx !important;
            font-weight: 400;
          }
        }
      }

      &__right {
        &__message {
          // margin-left: $label-width !important;
        }
      }
    }
  }
 

  // 可读
  .editor--readonly-content {
    // background-color: #000;

    ::v-deep .u-form-item:not(:last-child) {
      border-bottom: none;
    }

    ::v-deep .u-form-item {
      .u-form-item__body {
        &__left {
          justify-content: flex-start;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;

          color: #3d7fff;
          line-height: 48rpx;
          text-align: left;
          &__content {
            flex: auto;
            &__label {
              flex: auto;
              white-space: pre-wrap;
              span {
                font-size: 32rpx;
              }
            }
          }
        }

        &__right {
          // .lk-text-box-icon-wrapper{
          // 	display: none;
          // }
          &__content__slot {
            justify-content: flex-start !important;
          }
        }
      }
    }
  }
}

@mixin footer_buttons {
  .footer {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    padding: 30rpx 30rpx 0 30rpx;
    border-top: 1rpx solid #e8e8e8;
    bottom: 0;
    position: fixed;
    width: 100%;
    left: 0;
    box-sizing: border-box;
    z-index: 2;
    .buttons {
      display: flex;
      justify-content: space-around;
      width: 100%;
      ::v-deep .u-button {
        padding: 0 !important;
        margin: 0 20rpx;
        height: 80rpx;
        border-radius: 12rpx 12rpx 12rpx 12rpx !important;
        font-weight: 600;
        font-size: 32rpx;
        border: none;
        line-height: 48rpx;
        text-align: center;

        &.u-button--primary {
          background: linear-gradient(219deg, #3d7fff 0%, #3daeff 100%);
          color: #ffffff;
        }
        &.u-button--plain {
          background: #e9f1fe !important;
          color: #3d7fff !important;
        }
      }
    }
  }
}
