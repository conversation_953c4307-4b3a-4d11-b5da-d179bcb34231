import { url<PERSON>lphabet as scopedUrl<PERSON>lphabet } from './url-alphabet/index.js'

export async function random(length) {
  return new Promise((resolve, reject) => {
    uni.getRandomValues({
      length,
      success: (res) => {
        resolve(new Uint8Array(res.randomValues));
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

export function customRandom(alphabet, defaultSize, getRandom) {
  let mask = (2 << (31 - Math.clz32((alphabet.length - 1) | 1))) - 1
  let step = Math.ceil((1.6 * mask * defaultSize) / alphabet.length)
  return async (size = defaultSize) => {
    let id = ''
    while (true) {
      let bytes = await getRandom(step)
      let i = step
      while (i--) {
        id += alphabet[bytes[i] & mask] || ''
        if (id.length === size) return id
      }
    }
  }
}

export function customAlphabet(alphabet, size = 21) {
  return customRandom(alphabet, size, random)
}

export async function nanoid(size = 21) {
  const bytes = await random((size -= 0))
  let id = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    id += scopedUrlAlphabet[bytes[i] & 63]
  }
  return id
}
