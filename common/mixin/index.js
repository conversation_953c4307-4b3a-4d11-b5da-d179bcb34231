import envcfg from '@/common/config'
import notice from '@/common/utils/notice.js'
import { mapState } from 'vuex'

export default {
  // #ifdef MP-WEIXIN
  onShareAppMessage(data) {
    const pages = getCurrentPages()
    const path = pages[pages.length - 1].$page.fullPath
    return {
      title: this.appName,
      path: path,
    }
  },
  onShareTimeline() {
    return {
      title: this.appName
    }
  },
  // #endif

  computed: {
    ...mapState(['userInfo']),
    loginValid() {
      return !!this.userInfo?.userId
    },
    // 黑白悼念
    mourningStyle() {
      return {
        // filter: 'grayscale(1)'
      }
    },
    appName() {
      return this.$u.sys().appName
    },
    uNavbarHeight() {
      return this.$u.getPx(uni.$u.props.navbar.height) + this.statusBarHeight
    },
    statusBarHeight() {
      return this.$u.sys().statusBarHeight
    },
    safeBottomHeight() {
      return this.$u.sys().safeAreaInsets.bottom
    },
    isAuditEnv() {
      // #ifdef MP-WEIXIN
      return ['prod'].includes(envcfg.env) && uni.getAccountInfoSync()?.miniProgram?.envVersion != 'release'
      // #endif
      // #ifndef MP-WEIXIN
      return false
      // #endif
    }
  },
  methods: {
    noop() {},
    checkLogin() {
      if (this.loginValid) {
        return true
      }
      const pages = getCurrentPages()
      if (pages?.length) {
        this.$store.commit('SET_LOGIN_BACK_PATH', pages[pages.length - 1].$page.fullPath)
      }
			uni.redirectTo({
			  url: '/pages/login/login',
			});
      return false
    },
    // 订阅通知
    subscribeNotice(options) {
      notice.subscribe(options)
    },
    navTo(url) {
      url && uni.navigateTo({
        url
      })
    },
    navTab(url) {
      url && uni.switchTab({
        url
      })
    },
    navRedirect(url) {
      url && uni.redirectTo({
        url
      })
    },
    navLaunch(url) {
      url && uni.reLaunch({
        url
      })
    },
    navBack(delta = 1) {
      uni.navigateBack({
        delta
      })
    },
    navToIfLogin(url) {
      this.checkLogin() && this.navTo(url)
    },
    resolveImageUrl(path) {
      // #ifdef MP
      try {
        const fs = uni.getFileSystemManager()
        const base64 = fs.readFileSync(path, 'base64')
        const dotIndex = path.lastIndexOf('.')
        const suffix = dotIndex >= 0 && path.substring(dotIndex + 1) || 'png'
        return `data:image/${suffix == 'svg' ? 'svg+xml' : suffix};base64,${base64}`
      } catch (e) {
        console.log('resolveImageUrl error', e)
      }
      // #endif
      // #ifndef MP
      return path
      // #endif
    },
    isTabBarPage() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      console.log('currentPage',currentPage);
      const currentRoute = `/${currentPage.route}`;
      const tabBarPages = this.getTabBarPages();

      return tabBarPages.includes(currentRoute);
    },
    getTabBarPages() {
      // 这里列出所有的 TabBar 页面路径
      return [
        '/pages/home/<USER>',
        '/pages/instrument/instrument',
        '/pages/message/message',
        '/pages/user/user',
      ];
    },
  },
}
