// 搜索，支持延迟搜索,
// data中必须提供query对象
// 发送search事件，参数为query
export default {
  data() {
    return {
      emitedQuery: {},
      emitTimeoutId: null,
    }
  },
  methods: {
    innerEmitSearch(always = false) {
      if (this.emitTimeoutId) {
        clearTimeout(this.emitTimeoutId)
        this.emitTimeoutId = null
      }

      const query = {}
      for (let key in this.query) {
        const value = this.query[key]
        if (Array.isArray(value)) {
          if (value.length) {
            query[key] = value
          }
        } else if (value != '' && value != null) {
          query[key] = value
        }
      }
      if (!always) {
        const keyList = Object.keys(query)
        if (keyList.length == Object.keys(this.emitedQuery).length &&
          keyList.every(key => query[key] == this.emitedQuery[key])) {
          return
        }
      }
      this.emitedQuery = query
      this.$emit('search', query)
    },
    emitSearch() {
      this.innerEmitSearch(false)
    },
    emitSearchAlways() {
      this.innerEmitSearch(true)
    },
    emitSearchTimeout(timeout = 2000, always = false) {
      if (this.emitTimeoutId) {
        clearTimeout(this.emitTimeoutId)
      }
      this.emitTimeoutId = setTimeout(() => {
        this.emitSearch(always)
      }, timeout)
    },
    emitSearchLater() {
      this.emitSearchTimeout()
    },
  }
}
