export const fileMixin = {
  data() {
    return {
      videoUrl: '',
      videoContext: null,
      seek: 1000, // 默认获取视频的第一帧作为封面
      showVideo: false, // 控制视频组件的显示
      isFullScreen: false, // 控制全屏状态
      controls: false
    };
  },
  methods: {
    openFile(url, fileType) {
      if (fileType !== 1) {
        return;
      }

      const whiteFileTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'];
      const whiteImages = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'ico', 'wbmp', 'fax', 'net', 'rp', 'jfif', 'jpe', 'tiff', 'tif'];
      const whiteMedia = ['mp3', 'wav', 'flac', 'aac', 'mp4', 'avi', 'wmv', 'mov', 'flv'];

      const index = url.lastIndexOf('.');
      const fileTypeFromUrl = url.substring(index + 1, url.length).toLowerCase();

      uni.downloadFile({
        url: url,
        success: (res) => {
          const filePath = res.tempFilePath;

          if (whiteFileTypes.includes(fileTypeFromUrl)) {
            // 打开文档
            uni.openDocument({
              filePath: filePath,
              fileType: fileTypeFromUrl,
              showMenu: true,
              success: () => {
                console.log('打开文档成功');
              },
              fail: (err) => {
                console.error('打开文档失败--->', err);
                uni.showToast({
                  title: err.errMsg,
                  icon: 'none'
                });
              }
            });
          } else if (whiteImages.includes(fileTypeFromUrl)) {
            // 预览图片
            uni.previewImage({
              urls: [filePath],
              longPressActions: {
                itemList: ['发送给朋友', '保存图片', '收藏'],
                success: (data) => {
                  console.log('长按操作成功', data);
                },
                fail: (err) => {
                  console.error(err.errMsg);
                  uni.showToast({
                    title: err.errMsg,
                    icon: 'none'
                  });
                }
              }
            });
          } else if (whiteMedia.includes(fileTypeFromUrl)) {
            // 预览视频或音频
            this.videoUrl = filePath;
            this.showVideo = true;
            this.$nextTick(() => {
              this.videoContext = uni.createVideoContext("videoId", this);
              this.videoContext.requestFullScreen({ direction: 0 });
            });
          } else {
            uni.showToast({
              title: '不支持打开该类型文件',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error("下载失败--->", err);
          uni.showToast({
            title: err.errMsg,
            icon: 'none'
          });
        }
      });
    },
    playVideo(e) {
      this.videoContext = uni.createVideoContext("videoId", this);
      this.videoContext.requestFullScreen({ direction: 0 });
    },
    fullscreenchange(e) {
      // if (e.detail.fullScreen) {
      //   this.controls = true;
      // } else {
      //   this.controls = false;
      // }
    },
    closeVideo() {
      this.showVideo = false;
      if (this.isFullScreen) {
        this.videoContext.exitFullScreen();
      }
    },
    // renderVideoModal() {
    //   return (
    //     <view v-if="this.showVideo" class="modal">
    //       <view class="modal-content">
    //         <video id="videoId" :controls="this.controls" @fullscreenchange="this.fullscreenchange" :src="this.videoUrl" @play="this.playVideo"></video>
    //         <view class="close-btn" @click="this.closeVideo">
    //           <u-icon name="close-circle" size="24"></u-icon>
    //         </view>
    //       </view>
    //       <view class="modal-overlay" @click="this.closeVideo"></view>
    //     </view>
    //   );
    // }
  }
};
