// 此vm参数为页面的实例，可以通过它引用vuex中的变量
import envcfg from '@/common/config/index.js'
import { handle401Error, is401Error } from '@/common/utils/auth.js'

import {
  refreshToken
} from './token.js'


function tryRefreshToken(config) {
  if (!config) {
    return Promise.reject()
  }
  if (config.custom?.dontRefreshToken) {
    console.log('401 config dontRefreshToken')
    return Promise.reject()
  }
  if (config.custom) {
    config.custom.dontRefreshToken = true
  } else {
    config.custom = {
      dontRefreshToken: true
    }
  }
  return new Promise((resolve, reject) => {
    const ok = refreshToken(token => {
      if (token) {
        if (config?.data?.token && 'bearer ' + config.data.token == config.header['anxun-auth']) {
          config.data.token = token
        }
        // 更新请求头中的token
        config.header['Authorization'] = token
        config.header['anxun-auth'] = token
        resolve(uni.$u.http.request(config))
      } else {
        // token刷新失败，跳转到登录页
        handle401Error('登录已过期，请重新登录')
        reject()
      }
    })
    if (!ok) {
      // 无法刷新token，直接跳转登录页
      handle401Error('登录已过期，请重新登录')
      return reject()
    }
  })
}

function toast(msg) {
  setTimeout(() => {
    uni.showToast({
      title: msg,
      icon: 'none'
    })
  }, 20)
}

module.exports = (vm) => {
  uni.$u.http.setConfig((config) => {
    /* config 为默认全局配置*/
    config.baseURL = envcfg.baseUrl; /* 根域名  测试环境*/
    if (envcfg.env != 'prod') {
      envcfg.baseUrlChange = () => {
        config.baseURL = envcfg.baseUrl
      }
    }
    return config
  })

  // 请求拦截
  uni.$u.http.interceptors.request.use((config) => {
    let token = vm.$store.state.token
    config.header['Authorization'] = token ? token : ''
    config.header['anxun-auth'] = token ? token : ''
    return config
  }, config => { // 可使用async await 做异步操作
    return Promise.reject(config)
  })

  // 响应拦截
  uni.$u.http.interceptors.response.use((response) => {
    /* 对响应成功做点什么 可使用async await 做异步操作*/
    const {
      code,
      data,
      msg
    } = response.data
    // 自定义参数
    const custom = response.config?.custom
    if (code !== 200) {
      if (is401Error(code)) {
        return tryRefreshToken(response.config).catch(() => {
          // token刷新失败，确保跳转到登录页
          handle401Error('登录已过期，请重新登录')
          return Promise.reject(response.data)
        })
      }

      if (code == 500) {
        toast('系统错误,请联系管理员')
      } else if (custom.toast !== false) {
        // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
        toast(msg)
      }

      return Promise.reject(response.data)
    }

    //返回数据
    return data
  }, (err) => {

    console.log('request error', err);

    if (!err.data) {
      toast('网络开小差')
      return Promise.reject({
        code: err.statusCode
      })
    }

    const res = {}
    if (err.data.code) {
      res.code = err.data.code
      res.msg = err.data.msg
      res.data = err.data.data
    } else {
      res.code = err.statusCode
    }

    if (is401Error(res.code)) {
      return tryRefreshToken(err.config).catch(() => {
        // token刷新失败，确保跳转到登录页
        handle401Error('登录已过期，请重新登录')
        return Promise.reject(res)
      })
    }

    if (res.code == 500) {
      toast('服务器错误，请联系管理员');
      return Promise.reject(res)
    }

    if (!res.msg) {
      res.msg = `网络错误${res.code}`
    }

    if (err.config?.custom?.toast !== false) {
      toast(res.msg)
    }

    return Promise.reject(res)
  })
}