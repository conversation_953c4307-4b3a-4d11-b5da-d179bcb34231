import loginApi from '@/api/login.js'
import envcfg from '@/common/config/index.js'

import store from '@/store/index.js'

const debug = true
const log = debug ? console.log : () => {}

let loginRequested = false // 正在跳转登录页
let loginCallbacks = []

let ticket = '' // 外部跳转登录凭证
let ticketState = 'none' // 凭证状态： none refresh fail success

/**
 * 跳转到登录页，且记下登录成功后的返回页面
 */
function navToLogin(fromLaunch = false, callback) {
  const pages = getCurrentPages()
  if (pages?.length) {
    const path = pages[0].$page.fullPath
    if (path.includes('pages/login/login')) {
      for (let i = 1; i < pages.length; i++) {
        uni.navigateBack()
      }
      log('already in login page')
      callback && callback()
      return
    }
  }
  if (callback) {
    loginCallbacks.push(callback)
  }
  if (fromLaunch) {
    const options = uni.getLaunchOptionsSync()
    if (options?.path && !options.path.includes('pages/login/login')) {
      let query = ''
      if (options?.query) {
        query = Object.keys(options.query).map(it => `${it}=${options.query[it]}`).join('&')
        if (query) {
          query = '?' + query
        }
      }
      store.commit('SET_LOGIN_BACK_PATH', options.path[0] == '/' ? options.path : '/' + options.path + query)
    }
  }
  if (loginRequested) {
    return
  }
  loginRequested = true
  if (!fromLaunch && pages?.length) {
    store.commit('SET_LOGIN_BACK_PATH', pages[pages.length - 1].$page.fullPath)
  }

  log('fromLaunch', fromLaunch, 'loginBackPath', store.state.loginBackPath)

  uni.redirectTo({
    url: '/pages/login/login',
    complete: () => {
      loginCallbacks.forEach(it => it())
      loginCallbacks.length = 0
      loginRequested = false
    }
  })
}

/**
 * token无效时当前页面是否需要返回登录
 */
function isLoginNeeded(fromLaunch = false) {
  if (fromLaunch) {
    const options = uni.getLaunchOptionsSync()
    if (options?.path) {
      // 启动页不在白名单中
      return !envcfg.pageWhiteList.includes('/' + options.path)
    }
  }
  const pages = getCurrentPages()
  // 没有页面或存在非白名单页面时
  return !pages?.length || pages.some(it => !envcfg.pageWhiteList.includes(it.$page.fullPath.split('?')[0]))
}

function navToLoginIfNeeded(fromLaunch = false, reason = null) {
  store.commit('CLEAR_STORAGE')
  if (isLoginNeeded(fromLaunch)) {
    navToLogin(fromLaunch, () => {
      // #ifdef APP-PLUS
      fromLaunch && plus.navigator.closeSplashscreen()
      //#endif
      if (reason) {
        setTimeout(() => uni.$u.toast(reason), 200)
      }
    })
    return true
  } else {
    // #ifdef APP-PLUS
    fromLaunch && plus.navigator.closeSplashscreen()
    //#endif
    return false
  }
}

/**
 * 当前是否为假期托管页
 */
function isInVacationPage(fromLaunch = false) {
  if (fromLaunch) {
    const options = uni.getLaunchOptionsSync()
    if (options?.path) {
      return options.path.startsWith('pages_vacation/')
    }
  }
  const pages = getCurrentPages()
  return pages?.length && pages[pages.length - 1].$page.fullPath.startsWith('/pages_vacation/')
}


// token订阅
let subscriber = null

// 订阅token
function subscribeToken(subscription) {
  if (subscription && subscriber && !subscriber.includes(subscription)) {
    subscriber.push(subscription)
    return true
  }
  return false
}

function dispatchToken(token) {
  if (subscriber) {
    subscriber.forEach(it => {
      it(token)
    })
    subscriber = null
  }
}

/**
 * 刷新一次token
 */
export function refreshToken(subscription, fromLaunch) {
  if (ticketState == 'refresh') {
    // 凭证登录时不允许刷新
    return false
  }
  if (subscriber) {
    subscribeToken(subscription)
    return true
  }
  
  const userInfo = store.state.userInfo

  if (!userInfo?.userId) {
    navToLoginIfNeeded(fromLaunch, userInfo ? '未绑定孩子' : null)
    return false
  }
  subscriber = []
  subscribeToken(subscription)

  let handled = false
  let timeId = null

  // 刷新结果处理
  function handleResult(userInfo) {
    if (handled) {
      log('refresh token already handled')
      return
    }
    handled = true
    if (timeId) {
      clearTimeout(timeId)
      timeId = null
    }
    if (userInfo) {
      store.commit('SET_TOKEN', userInfo.accessToken)
      store.commit('SET_USERINFO', userInfo)
      dispatchToken(userInfo.accessToken)
    } else {
      dispatchToken(null)
      navToLoginIfNeeded(fromLaunch)
    }
  }

  // 刷新超时
  timeId = setTimeout(() => {
    log('refresh token timeout')
    timeId = null
    handleResult(null)
  }, 10000)

  let req = null
  if (store.state.loginType == 'wechat') {
    log('refresh wechat token')
    // 微信登录刷新
    req = new Promise((resolve, reject) => {
      uni.login({
        success: res => {
          loginApi.getWxToken({
            grantType: 'wechat',
            type: 1, // 1华云趣学, 2华云在校
            jsCode: res.code,
            sysBlongs: '10007',
          }).then(resolve).catch(reject)
        },
        fail: reject
      })
    })
  } else {
    log(`refresh ${store.state.loginType} token`)
    // 手机号登录刷新
    req = loginApi.getSsoToken({
      sysBlongs: "10007",
      token: userInfo.accessToken,
      roleId: userInfo.roleId,
      studentId: userInfo.studentId,
    })
  }

  req.then(res => {
    log('refresh token ok')
    handleResult(res)
    // #ifdef APP-PLUS
    fromLaunch && plus.navigator.closeSplashscreen()
    //#endif
  }).catch(err => {
    log('refresh token fail', err)
    handleResult(null)
  })
  return true
}

function refreshTicketToken(fromLaunch = false) {
  const tk = ticket
  if (!tk) {
    return false
  }
  let req = null

  // #ifdef BRAND_HUAYUN
  // 华云跳转token登录
  req = loginApi.getSsoToken({
    sysBlongs: '10007',
    token: tk
  })
  // #endif

  // #ifdef BRAND_LONGYAN
  // 龙岩跳token登录
  req = loginApi.getLongyanToken({
    token: ticket
  })
  // #endif

  if (!req) {
    return false
  }

  log('refresh ticket token')

  ticketState = 'refresh'
  req.then(res => {
    if (tk != ticket) {
      return
    }
    log('refresh ticket token success')
    ticketState = 'success'
    store.commit('SET_LOGIN_TYPE', 'ticket')
    store.commit('SET_TOKEN', res.accessToken)
    store.commit('SET_USERINFO', res)
    store.commit('SET_APP_MODE', 'normal')

    const pages = getCurrentPages()
    const autoRefresh = pages?.length == 1 && pages[0].$page.fullPath.includes('pages/home/<USER>')
    if (!autoRefresh) {
      log('current page can not auto refresh for new token, relaunch')
      uni.reLaunch({
        url: '/pages/home/<USER>'
      })
    }

    // #ifdef MP-WEIXIN
    wx.login({
      success: (res) => {
        if (res && res.code) {
          loginApi.getWxOpenId({
              type: 1, // 1华云趣学，2华云在线
              token: store.state.token,
              code: res.code
            })
            .then(res => {
              store.commit('SET_WX_OPENID', res.openid)
            })
        }
      }
    })
    // #endif

  }).catch(() => {
    if (tk != ticket) {
      return
    }
    log('refresh ticket token fail')
    ticketState = 'fail'
    navToLoginIfNeeded(fromLaunch)
  })

  return true
}

export function checkOnLaunch(options) {
  const tk = options?.query?.token
  if (tk) {
    log('app onLaunch with ticket')
    ticket = tk
    if (refreshTicketToken(true)) {
      return
    }
  }
  if (store.state.loginType == 'phone') {
    refreshToken(null, true)
    return
  }
  // #ifdef APP-PLUS
  plus.navigator.closeSplashscreen()
  //#endif
}

export function checkOnShow(options) {
  const tk = options?.query?.token
  if (tk && tk != ticket) {
    log('app onShow ticket change')
    ticket = tk
    refreshTicketToken(false)
  }
}