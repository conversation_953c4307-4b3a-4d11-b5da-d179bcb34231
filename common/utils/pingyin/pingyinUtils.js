import toPinyin from "./toPingyin";

/**
 * 根据拼音首字母筛选排序分组
 * @param {Array} arr 原数组
 * @param {String} key 原数组需要筛选的字段
 * @returns {Array} 返回一个[{name: A,value: []}] 格式的二维数组
 */
export function getGroupByPinyin(arr, key = 'name') {
  if (!arr) return

  // 获取A-Z字母数组
  let keys = [...Array(26).keys()].map((i) => String.fromCharCode(i + 65));

  arr = arr.map((n) => ({
    ...n,
    py: toPinyin.chineseToInitials(
      toPinyin.chineseToPinYin(n[key].substr(0, 1))
    ),
  }));

  let group = [];
  for (const i of keys) {
    // 新数组一级结构，可自行修改
    let item = {
      name: i,
      value: [],
    };
    for (const j of arr) {
      if (j.py === i) {
        item.value.push(j);
      }
    }
    if (item.value.length > 0) {
      item.value.sort((a, b) => a[key].localeCompare(b[key]));
      group.push(item);
    }
  }
  return group;
}

export function sortByPinyin(list, options) {
  if (!list?.length) {
    return list
  }

  let getter = null
  let name = null
  let map = null
  let pre = null
  let post = null
  if (typeof options == 'function') {
    getter = options
  } else if (typeof options == 'string') {
    name = options
  } else if (typeof options == 'object') {
    getter = options.getter
    name = options.name
    map = options.map
    pre = options.pre
    post = options.post
  }

  list.map(it => {
    const value = getter ? getter(it) : (name ? it[name] : it)
    const pylist = []
    const py = (map && map[value]) || toPinyin.chineseToPinYin(value)
    const re = /[A-Z][^A-Z]/g
    let m
    while ((m = re.exec(py))) {
      pylist.push(m[0])
    }
    return {
      e: it,
      value,
      pylist,
    }
  }).sort((l, r) => {
    let c
    if (pre) {
      c = pre(l.e, r.e)
      if (c) {
        return c
      }
    }
    for (let i = 0; i < l.pylist.length && i < r.pylist.length; i++) {
      c = l.pylist[i].localeCompare(r.pylist[i])
      if (c) {
        return c
      }
      c = l.value[i].localeCompare(r.value[i])
      if (c) {
        return
      }
    }
    c = l.pylist.length - r.pylist.length
    if (c) {
      return c
    }
    if (post) {
      c = post(l.e, r.e)
    }
    return c
  }).forEach((v, i) => {
    list[i] = v.e
  })

  return list
}