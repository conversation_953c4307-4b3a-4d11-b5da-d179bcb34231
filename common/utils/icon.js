// utils.js
export const ImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
export const VideoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'];
export const AudioExtensions = ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a', '.wma'];

export const isTheTypeFile = (file, extensions, types) => {
  if (typeof file === 'string') {
    return extensions.includes(file.substring(file.lastIndexOf('.')).toLowerCase());
  } else {
    return types.some((type) => file.type.startsWith(type));
  }
};

export const isImageFile = (file) => isTheTypeFile(file, ImageExtensions, ['image']);
export const isVideoFile = (file) => isTheTypeFile(file, VideoExtensions, ['video']);
export const isAudioFile = (file) => isTheTypeFile(file, AudioExtensions, ['audio']);

const file2IconMap = {
  txt: 'file2Txt',
  md: 'file2Md',
  csv: 'file2Csv',
  pdf: 'file2Pdf',
  doc: 'file2Doc',
  docx: 'file2Doc',
  xls: 'file2Xlsx',
  xlsx: 'file2Xlsx',
  ppt: 'file2Ppt',
  pptx: 'file2Ppt',
  mp3: 'file2Mp3',
  wav: 'file2Wav',
  mp4: 'file2Mp4',
  avi: 'file2Avi',
  zip: 'file2Zip',
  rar: 'file2Rar'
};

export function getFile2SvgIcon(name) {
  const type = name.substring(name.lastIndexOf('.') + 1).toLowerCase();
  return file2IconMap[type] || 'file2Unknown';
}
