/**
 * 统一的401处理工具函数
 */

// 防止重复跳转的标志和时间戳
let isNavigatingToLogin = false
let lastNavigateTime = 0

/**
 * 处理401未授权错误，清除用户信息并跳转到登录页
 * @param {string} message - 可选的错误提示信息
 */
export function handle401Error(message = '登录已过期，请重新登录') {
  console.log('处理401错误:', message)
  
  const now = Date.now()
  
  // 防止短时间内重复处理401错误（3秒内只处理一次）
  if (isNavigatingToLogin || (now - lastNavigateTime < 3000)) {
    console.log('已在处理401错误或短时间内重复调用，跳过处理')
    return
  }
  
  isNavigatingToLogin = true
  lastNavigateTime = now
  
  // 清除本地存储的用户信息
  try {
    const app = getApp()
    if (app && app.$store) {
      app.$store.commit('CLEAR_STORAGE')
    } else {
      // 如果无法访问 store，手动清除存储
      clearLocalStorage()
    }
  } catch (error) {
    console.error('清除存储失败:', error)
    // 手动清除存储作为备选方案
    clearLocalStorage()
  }
  
  // 跳转到登录页
  navigateToLoginPage()
  
  // 显示提示信息（只显示一次）
  if (message && !uni.getStorageSync('401_toast_shown')) {
    uni.setStorageSync('401_toast_shown', true)
    setTimeout(() => {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
      // 清除提示标志，允许下次显示
      setTimeout(() => {
        uni.removeStorageSync('401_toast_shown')
      }, 3000)
    }, 500)
  }
  
  // 重置标志，允许下次处理
  setTimeout(() => {
    isNavigatingToLogin = false
  }, 3000)
}

/**
 * 手动清除本地存储
 */
function clearLocalStorage() {
  const keysToRemove = [
    'token',
    'userInfo', 
    'loginType',
    'userRole',
    'wxOpenId',
    'flowList',
    'roleLevel'
  ]
  
  keysToRemove.forEach(key => {
    uni.removeStorageSync(key)
  })
}

/**
 * 跳转到登录页
 */
function navigateToLoginPage() {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  // 如果当前不在登录页，则跳转
  if (currentPage && !currentPage.route.includes('login')) {
    uni.reLaunch({
      url: '/pages/login/login?from401=true'
    })
  }
}

/**
 * 检查是否为401相关的错误码
 * @param {number} code - 错误码
 * @returns {boolean}
 */
export function is401Error(code) {
  return [401, 403, 406].includes(code)
}