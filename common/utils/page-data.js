/**
 * 分页数据请求管理
 * 参数
 * size               Number         每页大小
 * single             Boolean        单页列表数据，true时size忽略
 * url                String         接口url，可使用request替换
 * method             String         请求方法
 * request            Function       自定义请求函数，可使用url替换
 * api                Function       同request
 * params             Object         请求参数
 * idName             String Array   去重字段名，支持单个或多个字段比较，所有字段比较使用*号
 * comparator         Function       自定义比较函数，idName无法满足需求时使用
 * onRefreshStart     Function       开始刷新回调
 * onRefreshFinish    Function       刷新成功回调
 * onRefreshCancel    Function       刷新取消回调
 * onRefreshError     Function       刷新错误回调
 * onRefreshFinally   Function       刷新结束回调
 * onLoadMoreStart    Function       加载开始回调
 * onLoadMoreFinish   Function       加载成功回调
 * onLoadMoreCancel   Function       加载取消回调
 * onLoadMoreError    Function       加载错误回调
 * onLoadMoreFinally  Function       加载结束回调
 * onPageDataLoad     Function       页面数据请求原始结果回调
 * 
 * 属性
 * list       Array   已加载的分页数据列表
 * status     String  刷新加载状态 none-无，refresh-刷新中，loadmore-加载中
 * total      Number  数据总数量
 * current    Number  当前页码1...N
 * size       Number  每页大小
 * more       Boolean 是否有更多数据
 * used       Boolean 初始化或调用clear后是否调用过refresh或loadMore
 * failed     Boolean 请求失败
 * empty      Boolean 是否为空
 * moreStatus String  更多状态 none-无，loading-加载中，loadmore-加载更多，nomore-没有更多了
 * params     Object  请求参数
 * 
 * 方法
 * refresh()          刷新列表，清除数据加载第一页数据
 * loadMore(number)   加载指定页或下一页数据，number页码 > 0时加载指定页并删除后面的数据
 * pageOf(item)       数据所在页，list中的item数据
 * clear()            清除数据
 * 
 */

const http = uni.$u.http

export default function(configs) {
  let pageData = {} // 分页数据对象

  let list = [] // 当前已下载数据
  let total = 0 // 服务端数据总数
  let current = 1 // 当前页码
  let size = configs.size || 10 // 每页数目
  let single = configs.single // 单页列表数据
  let more = true // 有更多数据
  let used = false // 是否使请求过
  let failed = false // 请求失败

  let url = configs.url // 数据url
  let method = configs.method || 'GET' // 请求方法
  let request = configs.request || configs.api // 请求接口

  let params = configs.params ? {
    ...configs.params
  } : {} // 额外参数
  let idName = configs.idName // 去重比较id字段名，可为数组，*表示全部字段
  let comparator = configs.comparator // 自定义比较函数
  if (!comparator && idName != null) {
    if (idName == '*') {
      comparator = function(lhs, rhs) {
        for (let k in lhs) {
          if (lhs[k] != rhs[k]) {
            return false
          }
        }
        return true
      }
    } else if (Array.isArray(idName)) {
      if (idName.length != 0) {
        comparator = function(lhs, rhs) {
          for (let name of idName) {
            if (lhs[name] != rhs[name]) {
              return false
            }
          }
          return true
        }
      }
    } else {
      comparator = function(lhs, rhs) {
        return lhs[idName] == rhs[idName]
      }
    }
  }

  function stub() {}

  let onPageDataLoad = configs.onPageDataLoad

  let requestResult = null // 请求结果Promise
  let requestStatus = 'none' // 请求状态: none refresh loadmore
  let requestRegistry = {
    'refresh': {
      start: configs.onRefreshStart || stub,
      finish: configs.onRefreshFinish || stub,
      cancel: configs.onRefreshCancel || stub,
      error: configs.onRefreshError || stub,
      finally: configs.onRefreshFinally || stub,
      consumer: function(data) {
        list.splice(0, list.length, ...data)
        return data
      }
    },
    'loadmore': {
      start: configs.onLoadMoreStart || stub,
      finish: configs.onLoadMoreFinish || stub,
      cancel: configs.onLoadMoreCancel || stub,
      error: configs.onLoadMoreError || stub,
      finally: configs.onLoadMoreFinally || stub,
      consumer: function(data) {
        if (data.length == 0) {
          return data
        }
        if (!comparator) {
          list.splice(list.length, 0, ...data)
          return data
        }
        // 以第一条记录作为去重位置
        let index = list.length
        for (let i = list.length - 1; i >= 0; i--) {
          if (comparator(data[0], list[i])) {
            index = i
            break
          }
        }
        list.splice(index, list.length - index, ...data)
        return data
      }
    }
  }

  function updateEmpty() {
    pageData.empty = !used || (!more && total == 0)
  }

  function updateMoreStatus() {
    if (requestStatus == 'loadmore') {
      // 加载中
      pageData.moreStatus = 'loading'
    } else if (failed) {
      // 失败
      pageData.moreStatus = 'none'
    } else if (more) {
      // 更多
      pageData.moreStatus = 'loadmore'
    } else if (!used || total == 0) {
      // 未请求或空
      pageData.moreStatus = 'none'
    } else {
      pageData.moreStatus = 'nomore'
    }
  }

  function requestPage(status, number, debounce = false) {
    if (!used) {
      used = true
      pageData.used = true
      updateEmpty()
    }

    let reqNumber
    let reqSize
    let startNumber

    if (single) {
      reqNumber = null
      reqSize = null
    } else if (debounce) {
      // 防抖动时至少需要请求当前页和下一页
      if (number % 2 == 1) {
        // 奇数时2倍速对齐
        reqNumber = Math.ceil(number / 2)
        reqSize = size * 2
        startNumber = reqNumber * 2 - 1
      } else {
        // 偶数时3倍对齐
        reqNumber = Math.ceil(number / 3)
        reqSize = size * 3
        startNumber = reqNumber * 3 - 2
      }
    } else {
      reqNumber = number
      reqSize = size
      startNumber = number
    }

    params.current = reqNumber
    params.size = reqSize

    let reqRes = null
    if (request) {
      reqRes = request(params)
    } else if (method == 'GET') {
      reqRes = http.get(url, {
        params
      })
    } else {
      reqRes = http.post(url, params)
    }
    requestResult = reqRes

    requestStatus = status
    pageData.status = status
    updateMoreStatus()

    let registry = requestRegistry[status]
    registry.start()
    reqRes
      .then(res => {
        if (reqRes != requestResult) {
          // 已取消
          return
        }
        requestResult = null
        requestStatus = 'none'
        pageData.status = 'none'

        let data
        if (single) {
          // 单页数据每次都清除
          list.splice(0, list.length)
          data = res || []
          total = data.length
          current = 1
          more = false
        } else {
          // 清除请求页及之后的数据
          const overflow = list.length - (reqNumber - 1) * reqSize
          if (overflow > 0) {
            list.splice(list.length - overflow, overflow)
          }
          data = res?.records || []
          total = res?.total || 0
          current = data.length == 0 ? startNumber : startNumber + Math.ceil(data.length / size) - 1
          more = current * size < total
        }

        pageData.total = total
        pageData.current = current
        pageData.more = more

        failed = false
        pageData.failed = false

        if (onPageDataLoad) {
          onPageDataLoad(data, {
			  total,
		  })
        }

        if (!single && list.length > current * size) {
          list.splice(current * size, list.length - current * size)
        }

        registry.consumer(data)
        registry.finish()
        registry.finally()
      })
      .catch(err => {
        console.log('page-data request error', err)
        if (reqRes != requestResult) {
          // 已取消
          return
        }
        requestResult = null
        requestStatus = 'none'
        pageData.status = 'none'

        failed = total == 0
        pageData.failed = failed

        registry.error()
        registry.finally()
      })
      .finally(() => {
        updateEmpty()
        updateMoreStatus()
      })
  }

  function cancelRequest() {
    if (requestStatus == 'none') {
      return
    }
    let registry = requestRegistry[requestStatus]
    requestResult = null
    requestStatus = 'none'
    pageData.status = 'none'
    registry.cancel()
    registry.finally()
  }

  pageData.refresh = function() {
    if (requestStatus != 'refresh') {
      cancelRequest()
      requestPage('refresh', 1)
    }
    return true
  }

  pageData.loadMore = function(number) {
    if (requestStatus == 'refresh') {
      return false
    }
    if (number && number > 0) {
      // 加载指定页
      cancelRequest()
      requestPage('loadmore', number, true)
    } else if (requestStatus == 'none' && more) {
      // 加载最后一页
      requestPage('loadmore', list.length == 0 ? 1 : current + 1)
      return true
    }
    return false
  }

  pageData.pageOf = function(item, index) {
    if (index == null) {
      index = list.indexOf(item)
      if (index < 0 && comparator) {
        index = list.findIndex(it => comparator(it, item))
      }
    }
    if (index < 0) {
      return -1
    }
    return single ? 1 : Math.floor(index / size) + 1
  }

  pageData.clear = function() {
    cancelRequest()
    current = 1
    total = 0
    more = true
    used = false
    failed = false
    list.splice(0, list.length)
    pageData.total = 0
    pageData.current = 1
    pageData.more = true
    pageData.used = false
    pageData.failed = false
    updateEmpty()
    updateMoreStatus()
  }

  Object.defineProperty(pageData, 'params', {
    set: val => {
      pageData.clear()
      params = {
        ...val
      }
    },
    get: () => {
      return {
        ...params
      }
    }
  })

  pageData.list = list

  // 方便数据绑定变量，外部改变不影响内部状态
  pageData.status = 'none'
  pageData.total = 0
  pageData.current = current
  pageData.size = size
  pageData.more = true
  pageData.used = false
  pageData.failed = false
  pageData.empty = true
  pageData.moreStatus = 'none'

  return pageData
}
