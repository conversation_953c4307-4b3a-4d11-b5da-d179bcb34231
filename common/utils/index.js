export function buildUrlWithParams(baseUrl, params) {
  const queryString = objectToQueryString(params);
  if (baseUrl.includes("?")) {
    return `${baseUrl}&${queryString}`;
  } else {
    return `${baseUrl}?${queryString}`;
  }
}
function objectToQueryString(obj) {
  return Object.keys(obj)
    .map((key) => `${key}=${obj[key]}`)
    .join("&");
}

// 没有new URL 和 URLSearchParams 的浏览器上使用
export const accessFileUrlWithFilename = (fileUrl ,filename) => {

  
  if (!fileUrl.includes('filename')) {
    if (fileUrl.includes('?')) {
      fileUrl = fileUrl + `&filename=${filename.replaceAll(' ', '')}`;
    } else {
      fileUrl = fileUrl + `?filename=${filename.replaceAll(' ', '')}`;
    }
  }

  return fileUrl;
};

/**
 * 从URL中提取文件名
 * @param {String} url 包含文件名参数的URL
 * @param {String} paramName 参数名，默认为'filename'
 * @return {String} 提取的文件名，如果未找到则返回默认值
 */
export function getFileNameFromUrl(url, paramName = 'filename', defaultName = '未知文件') {
  try {
    // 尝试使用 URL API
    if (typeof URL !== 'undefined') {
      const urlObj = new URL(url);
      const fileName = urlObj.searchParams.get(paramName);
      return fileName || defaultName;
    }
    
    // 降级方案：使用正则表达式
    const regex = new RegExp(`[?&]${paramName}=([^&]+)`);
    const match = regex.exec(url);
    return match ? decodeURIComponent(match[1]) : defaultName;
  } catch (e) {
    console.error('提取文件名失败:', e);
    return defaultName;
  }
}