import store from '@/store'

// 定时检测ID
let intervalId = null
// 最后一次交互时间
let lastTime = Date.now()
// 当前是否显示登录提示框
let showingModal = false
// 无操作时长
const hour = 2

// 检查是否无操作超过指定时长
function check() {
  if (showingModal) {
    // 正在显示提示框
    return
  }
  const duration = Date.now() - lastTime
  if (duration < hour * 3600 * 1000) {
    // 未超时不作处理
    return
  }
  const pages = getCurrentPages()
  if (pages?.length) {
    const path = pages[pages.length - 1].$page.fullPath
    if (path.includes('pages/login/login')) {
      // 当前已是登录页不作处理
      return
    }
  }
  showingModal = true
  uni.showModal({
    title: '提示',
    content: `您已超过${hour}小时未操作，请重新登录`,
    showCancel: false,
    complete: () => {
      showingModal = false
      store.commit('CLEAR_STORAGE')
      uni.navigateTo({
        url: '/pages/login/login'
      })
    }
  })
}

const interceptor = {
  invoke: () => {
    // 用户操作触发API后记录最近一次操作时间
    lastTime = Date.now()
  }
}

// 用户交互时会触发的API名称列表
['request', 'navigateTo', 'navigateBack'].forEach(name => {
  // 添加拦截
  uni.addInterceptor(name, interceptor)
})

// 开始
function observe() {
  if (!intervalId) {
    // 每间隔一段时间检查一次时间
    intervalId = setInterval(check, 10000)
    lastTime = Date.now()
  }
}

function unobserve() {
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }
}

export default {
  observe,
  unobserve
}
