import CryptoJS from 'crypto-js'

function aesStringify(input, key) {
  if (!input) {
    return null
  }
  return CryptoJS.AES.encrypt(
    CryptoJS.enc.Utf8.parse(input),
    CryptoJS.enc.Utf8.parse(key), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }).toString()
}

function aesParse(input, key) {
  try {
    return CryptoJS.AES.decrypt(
      input,
      CryptoJS.enc.Utf8.parse(key), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      }).toString(CryptoJS.enc.Utf8)
  } catch {
    return null
  }
}

function generateKey() {
  const arr = []
  for (let i = 0; i < 16; i++) {
    arr.push(String.fromCharCode(Math.floor(Math.random() * (127 - 33 + 1)) + 33))
  }
  return arr.join('')
}

function stringify(value) {
  const key = generateKey()
  const holder = {
    key: key,
    value: aesStringify(JSON.stringify(value), key)
  }
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(JSON.stringify(holder)))
}

function parse(data) {
  if (!data) {
    return null
  }
  let holder = null
  try {
    holder = JSON.parse(CryptoJS.enc.Base64.parse(data).toString(CryptoJS.enc.Utf8))
  } catch {
    return null
  }
  if (!holder.key) {
    return null
  }
  return JSON.parse(aesParse(holder.value, holder.key))
}

function encryptPassword(password) {
  return aesStringify(password, 'BEAUSEFJDKFHAKFJ')
}

const accountName = 'usr-ac'
const accountVersion = 'v1'
const accountExpire = 5 * 24 * 3600 * 1000

function storeAccount(account) {
  uni.setStorageSync(accountName, stringify({
    version: accountVersion,
    time: new Date().getTime(),
    account: account
  }))
}

function loadAccount() {
  const data = parse(uni.getStorageSync(accountName))
  if (!data) {
    return null
  }
  if (data.version != accountVersion ||
    Math.abs(new Date().getTime() - data.time) > accountExpire) {
      uni.removeStorageSync(accountName)
    return null
  }
  return data.account
}

export default {
  encryptPassword,
  storeAccount,
  loadAccount,
}
