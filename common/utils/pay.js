import orderApi from '@/api/order.js'
import loginApi from '@/api/login.js'

import store from '@/store/index.js'

// #ifdef MP-WEIXIN
async function wxOpenIdPay(order) {
  return new Promise((resolve, reject) => {
    let infoOk = false
    let req
    // 课程收费
    req = orderApi.getCoursePayInfo({
      id: order.id,
      openId: store.state.wxOpenId,
      paymentMethod: 1,
    })
    req.then(res => {
        infoOk = true
        uni.requestPayment({
          nonceStr: res.nonceStr,
          package: res.package,
          paySign: res.paySign,
          signType: res.signType,
          timeStamp: res.timeStamp,
          success: () => {
            resolve()
          },
          fail: res => {
            if (res.errMsg == 'requestPayment:fail cancel') {
              reject('pay:cancel')
            } else {
              reject('pay:fail')
            }
          }
        })
      })
      .finally(res => {
        if (!infoOk) {
          reject('info:fail')
        }
      })

  })
}

async function mpWxPay(order) {
  return new Promise((resolve, reject) => {
    if (!store.state.wxOpenId) {
      uni.login({
        success: res => {
          loginApi.getWxOpenId({
            type: 1,
            token: store.state.token,
            code: res.code
          }).then(res => {
            store.commit('SET_WX_OPENID', res.openid)
          }).finally(() => {
            if (!store.state.wxOpenId) {
              reject()
            } else {
              wxOpenIdPay(order)
                .then(res => {
                  resolve(res)
                })
                .catch(res => {
                  reject(res)
                })
            }
          })
        },
        fail: res => {
          uni.showToast({
            title: '登录小程序失败'
          })
          reject(res)
        }
      })
    } else {
      wxOpenIdPay(order)
        .then(res => {
          resolve(res)
        })
        .catch(res => {
          reject(res)
        })
    }
  })
}
// #endif

let paying = false

function pay(options) {
  let fn = null
  // #ifdef MP-WEIXIN
  fn = mpWxPay
  // #endif 
  if (!fn) {
    return
  }
  if (paying) {
    return
  }
  paying = true
  uni.showLoading({
    title: '支付中',
    mask: true
  })
  fn(options.order)
    .then(res => {
      options.success?.(options.order)
    }).catch(res => {
      if (res == 'pay:cancel') {
        uni.showToast({
          title: '支付已取消',
          icon: 'error'
        })
      } else if (res == 'pay:fail') {
        uni.showToast({
          title: '支付失败',
          icon: 'error'
        })
      }
      options.fail?.(res)
    }).finally(() => {
      paying = false
      uni.hideLoading()
      options?.complete?.()
    })
}


export default pay