// #ifdef MP-WEIXIN

/**
 * 小程序通知订阅事项
 * 1.必须由用户tap触发，否则api会失败
 * 2.每次订阅数量有限制 https://developers.weixin.qq.com/appprogram/dev/api/open-api/subscribe-message/wx.requestSubscribeMessage.html
 * 实现方案
 * 1.获取后端保存的订阅数，更新订阅数到后端
 * 2.检查通知总开关，检查被用户拒绝的通知
 * 3.根据后端保存的订阅数量来判断是否需要订阅
 * 4.用户接受时勾选不再询问的消息按批次在用户点击页面元素时静默订阅，直到预定的订阅数
 * 5.用户未勾选不再询问的是需要拉起弹窗的，此时需要一次性按批次进行，下批次前通过wx.showModal获取用户tap条件
 */

import store from '@/store'

// 默认消息模板
const templateList = [{
    id: '3igoYcraFL-meUl7AClyUuP04MmjUmni0SzKTah0IfE',
    code: 'school-notice',
    name: '学校公告通知	'
  },
  {
    id: 'XMZ_4QvupyRcjNljMnap6e6TysfAgIU_uf95OUTWyvA',
    code: 'order-refund',
    name: '退款申请通过通知	'
  },
  {
    id: 'wzuW2HbXskShEXnsJNSlq6EtqhDbOJqNcOGCpp5acGc',
    code: 'order-reject',
    name: '退费申请驳回通知	'
  },
  {
    id: 'D3AqjHA3NmsXR_F1VZTcOGErpfjA5F-s7wO5se72gko',
    code: 'order-pay',
    name: '待支付提醒'
  },
  {
    id: 'KfUSSFFfIuuhYhamIZLV7QsiO6iIb7jNR42MJ-nzyNc',
    code: 'course-apply',
    name: '课程报名提醒	'
  },
  {
    id: 'AIcAreShkMkbbrnekevEPn2U5XH_iEf17jO-Y1Z2XI0',
    code: 'questionnaire',
    name: '调查问卷通知'
  }
]

// 消息模板状态
const templateMap = {}

templateList.forEach(it => {
  it.status = '' // accept reject ban
  it.count = 0 // 订阅计数
  it.inc = 0 // 增加数量
  it.absInc = false // inc是否为非增加值
  it.synced = false // 是否已同步到后端
  it.dataId = '' // 远程数据ID
  templateMap[it.id] = it
  templateMap[it.code] = it
})

function compareVersion(ver1, ver2) {
  const arr1 = ver1.split('.')
  const arr2 = ver2.split('.')
  for (let i = 0; i < arr1.length; i++) {
    const v1 = parseInt(arr1[i])
    const v2 = parseInt(arr2[i])
    if (v1 < v2) {
      return -1
    }
    if (v1 > v2) {
      return 1
    }
  }
  return 0
}
// 微信版本
const VERSION = wx.getSystemInfoSync().version

// 单批次最大订阅消息个数
const BATCH_SIZE = compareVersion(VERSION, '7.0.7') < 0 ? 1 : 3

// 需要询问的每个模板订阅最大计数
const ASK_SUBSCRIPTION_COUNT = 50
// 已接受的每个模板订阅最大计数
const ACCEPT_SUBSCRIPTION_COUNT = 50

const SAVE_DELAY = 5000

const getSubscriptionCountList = params => uni.$u.http.get('/huayun-parentai/applets/message/list', {
  params
})

const updateSubscriptionCountList = data => uni.$u.http.post('/huayun-parentai/applets/message/submit', data)

function createBatchList(list) {
  const batchList = []
  for (let i = 0; i < list.length; i += BATCH_SIZE) {
    batchList.push(list.slice(i, i + BATCH_SIZE))
  }
  return batchList
}


let buzy = false
let waiting = null
let fetchNeeded = true

async function fetchCounts() {
  if (!store.state.wxOpenId) {
    console.log('notice fetchCounts no openid')
    return Promise.reject()
  }
  return getSubscriptionCountList({
      openid: store.state.wxOpenId
    })
    .then(res => {
      fetchNeeded = false
      templateList.forEach(it => {
        it.count = 0
        it.inc = 0
        it.absInc = false
        it.synced = false
        it.dataId = ''
      })
      res?.forEach(it => {
        const template = templateMap[it.templateId]
        if (template && !template.synced) {
          template.count = it.subscribeNum > 0 ? it.subscribeNum : 0
          template.synced = true
          template.dataId = it.id
        }
      })
    })
}

function saveCounts() {
  if (!store.state.wxOpenId) {
    console.log('notice saveCounts no openid')
    return
  }

  const list = templateList.filter(it => !it.synced)
  if (!list.length) {
    return
  }

  // 每次都获取已保存数据
  getSubscriptionCountList({
      openid: store.state.wxOpenId
    })
    .then(res => {
      list.forEach(it => {
        const old = res.find(old => old.templateId == it.id)
        it.count = it.absInc ? it.inc : (old?.subscribeNum > 0 ? old.subscribeNum : 0) + it.inc
        it.dataId = old?.id
        it.inc = 0
        it.absInc = false
        it.synced = true
      })
      // 
      updateSubscriptionCountList({
        appletsMessages: list.map(it => ({
          id: it.dataId,
          openid: store.state.wxOpenId,
          templateId: it.id,
          subscribeNum: it.count,
        }))
      })
    })

}

let saveCountsId = null

function saveCountsLater(time) {
  if (saveCountsId) {
    clearTimeout(saveCountsId)
    saveCountsId = null
  }
  saveCountsId = setTimeout(() => {
    saveCountsId = null
    saveCounts()
  }, time || SAVE_DELAY)
}

function resetCounts() {
  templateList.forEach(it => {
    it.count = 0
    it.inc = 0
    it.absInc = false
    it.synced = false
    it.dataId = ''
  })
  fetchNeeded = true
}

function resetReject() {
  templateList.forEach(it => {
    if (it.status == 'reject') {
      it.status = ''
    }
  })
}

/**
 * 消息订阅
 * @param options
 * {
 *  codes: Array 消息代码
 *  type: String  ask订阅需要询问用户的消息
 *                accept订阅不需要询问用户的消息,必须在用户tap时调用,用于静默订阅
 *                both先订阅不需要询问的消息,后订阅需要询问的消息,用于不知道某消息是否需要询问
 *                默认为accept
 *  askModal: Boolean 询问是是否显示建议弹框，如果是在tap时订阅可取false，否则订阅失败
 *  askAlways: Boolean ask类型时,是否包含之前用户拒绝的消息
 *  askLimit: Number 订阅次数上限，ask类型时小于此值的通知将被拉起弹窗口，<= 0时使用默认值ASK_SUBSCRIPTION_COUNT
 *  saveDelay: Number 保存延时单位毫秒，避免频繁提交
 *  success: Function 订阅成功
 *  fail: Function订阅失败
 *  complete: Function订阅结束成功或失败
 * }
 * @link https://developers.weixin.qq.com/appgame/dev/api/open-api/setting/wx.getSetting.html
 * @link https://developers.weixin.qq.com/appgame/dev/api/open-api/setting/SubscriptionsSetting.html
 */
function subscribe(options = {}) {
  let list = [] // 消息列表
  let chain = [] // 执行链
  let setting = {} // 微信设置结果
  let result = []
  let hadOpenSetting = false

  if (typeof options == 'string') {
    options = {
      codes: options
    }
  }
  
  function doNext() {
    const next = waiting
    if (next) {
      console.log('notice doNext')
      waiting = null
      subscribe(next)
    }
  }
  
  function doPreFail() {
    console.log('notice fail')
    if (options.fail) {
      options.fail()
    }
    if (options.complete) {
      options.complete(false)
    }
  }

  function doFail() {
    console.log('notice fail')
    buzy = false
    saveCountsLater(options.saveDelay || SAVE_DELAY)
    if (options.fail) {
      options.fail()
    }
    if (options.complete) {
      options.complete(false)
    }
    doNext()
  }

  function doSuccess() {
    console.log('notice success ' + result.length)
    buzy = false
    saveCountsLater(options.saveDelay || SAVE_DELAY)
    if (options.success) {
      options.success(result)
    }
    if (options.complete) {
      options.complete(true, result)
    }
    doNext()
  }
  
  if (!store.state.userInfo?.studentId) {
    console.log('notice subscribe no child')
    doPreFail()
    return false
  }

  if (!store.state.wxOpenId) {
    console.log('notice subscribe no openid')
    doPreFail()
    return false
  }

  if (fetchNeeded) {
    if (!options.askModal) {
      fetchCounts()
      console.log('notice subscribe askModal false without fetch')
      doPreFail()
      return
    }
    console.log('notice subscribe askModal true without fetch, will subscribe again after fetch')
    fetchCounts()
      .then(() => {
        subscribe(options)
      }).catch(() => {
        doPreFail()
      })
    return
  }

  console.log('notice subscribe', options, templateList)

  if (buzy) {
    if (options.askModal && !waiting) {
      waiting = options
      return true
    }
    console.log('notice subscribe buzy')
    doPreFail()
    return false
  }
  buzy = true

  if (options.codes?.length) {
    let codeList = null
    if (Array.isArray(options.codes)) {
      codeList = options.codes
    } else {
      codeList = options.codes.split(',')
    }
    codeList.forEach(it => {
      const template = templateMap[it]
      if (template) {
        list.push(templateMap[it])
      }
    })
  } else {
    list = templateList.slice()
  }

  if (list.length == 0) {
    console.log('notice subscribe empty')
    doFail()
    return false
  }

  list.sort((l, r) => l.count - r.count)

  console.log('notice subscribe list', list)

  // 需要拉起授权窗口消息列表
  function getAskList() {
    const itemSettings = setting?.subscriptionsSetting?.itemSettings
    return list.filter(it => (!it.status || options.askAlways) &&
      it.count < (options.askLimit > 0 ? options.askLimit : ASK_SUBSCRIPTION_COUNT) &&
      (it.count == 0 || !itemSettings || !itemSettings[it.id]))
  }

  // 不需要拉起授权窗口的消息列表（用户接受且不再询问）
  function getAcceptList() {
    const itemSettings = setting?.subscriptionsSetting?.itemSettings
    if (!itemSettings) {
      return []
    }
    return list.filter(it => it.count < ACCEPT_SUBSCRIPTION_COUNT &&
      itemSettings[it.id] == 'accept')
  }

  // 执行下一步
  function chainNext() {
    const next = chain.shift()
    if (next) {
      next()
    } else {
      doSuccess()
    }
  }

  // 查询微信设置
  function doGetSetting() {
    console.log('notice doGetSetting')
    wx.getSetting({
      withSubscriptions: true, // 是否同时获取用户订阅消息的订阅状态，默认不获取。
      success: res => {
        console.log('notice getSetting:success', res)
        setting = res
        chainNext()
      },
      fail: err => {
        console.log('notice getSetting:fail', err)
        doFail()
      }
    })
  }

  // 打开微信设置
  function doOpenSetting() {
    console.log('notice doOpenSetting')
    hadOpenSetting = true
    wx.openSetting({
      success: res => {
        console.log('notice openSetting:success')
        chainNext()
      },
      fail: err => {
        console.log('notice openSetting:fail', err)
        doFail()
      }
    })
  }

  // 检查主开关
  function doCheckMainSwitch() {
    console.log('notice doCheckMainSwitch')
    // 订阅消息总开关，true为开启，false为关闭
    if (setting.subscriptionsSetting.mainSwitch) {
      chainNext()
      return
    }

    templateList.forEach(it => {
      if (it.status == 'accept') {
        it.status = ''
      }
      it.count = 0
      it.inc = 0
      it.absInc = true
      it.synced = false
    })
    saveCounts()

    wx.showModal({
      title: '提示',
      content: '检测到您未打开接收通知，是否去设置打开？',
      success: res => {
        if (res.confirm) {
          chain.unshift(doCheckMainSwitch)
          chain.unshift(doGetSetting)
          doOpenSetting()
        } else if (res.cancel) {
          console.log('notice openSetting:cancel')
          doFail()
        }
      }
    })
  }

  // 检查被拒绝且不再询问的消息
  function doCheckReject() {
    console.log('notice doCheckReject')
    const itemSettings = setting?.subscriptionsSetting?.itemSettings
    if (!itemSettings) {
      chainNext()
      return
    }
    const rejectList = list.filter(it => itemSettings[it.id] == 'reject')
    if (!rejectList.length) {
      chainNext()
      return
    }
    rejectList.forEach(it => {
      if (it.status == 'accept') {
        it.status = ''
      }
      it.count = 0
      it.inc = 0
      it.absInc = true
      it.synced = false
    })
    saveCounts()

    // 提示打开通知
    wx.showModal({
      title: '提示',
      content: `检测到您未打开接收${rejectList.map(it => it.name).join('、')}，是否去设置打开？`,
      success: res => {
        if (res.confirm) {
          chain.unshift(doCheckReject)
          chain.unshift(doGetSetting)
          doOpenSetting()
        } else if (res.cancel) {
          console.log('notice openSetting:cancel')
          // 继续订阅未拒绝的
          chainNext()
        }
      }
    })
  }

  // 订阅需要担起授权窗口的消息
  function doSubscribeAskList() {
    console.log('notice doSubscribeAskList')
    const list = getAskList()
    if (list.length == 0) {
      console.log('notice doSubscribeAskList empty')
      chainNext()
      return
    }

    const batchList = createBatchList(list)

    function nextBatch() {
      const batch = batchList.shift()
      if (!batch) {
        chainNext()
        return
      }
      if (options.askModal || hadOpenSetting) {
        wx.showModal({
          title: '提示',
          content: `确保能接收${batch.map(it => it.name).join('、')}，请勾选【总是保持以上选择，不在询问】`,
          showCancel: false,
          success: () => {
            subscribeBatch(batch)
          },
          fail: () => {
            subscribeBatch(batch)
          }
        })
      } else {
        subscribeBatch(batch)
      }
    }

    function subscribeBatch(batch) {
      const ids = batch.map(it => it.id)
      wx.requestSubscribeMessage({
        tmplIds: ids,
        success: res => {
          console.log('notice subscribeIdList:success', res)
          ids.forEach(id => {
            const status = res[id]
            if (!status) {
              return
            }
            const template = templateMap[id]
            if (status.includes('accept')) {
              template.status = 'accept'
              template.count++
              template.inc++
              template.synced = false
              result.push(template.code)
            } else {
              template.status = status
            }
          })
          nextBatch()
        },
        fail: err => {
          console.log('notice subscribeIdList:fail', err)
          nextBatch()
        },
      })

    }

    nextBatch()
  }

  // 订阅无需拉起授权窗口的消息
  function doSubscribeAcceptList() {
    console.log('notice doSubscribeAcceptList')
    const list = getAcceptList().slice(0, BATCH_SIZE)
    if (list.length == 0) {
      console.log('notice doSubscribeAcceptList empty')
      chainNext()
      return
    }
    const ids = list.map(it => it.id)
    wx.requestSubscribeMessage({
      tmplIds: ids,
      success: res => {
        console.log('notice subscribeIdList:success', res)
        ids.forEach(id => {
          const status = res[id]
          if (!status) {
            return
          }
          const template = templateMap[id]
          if (status.includes('accept')) {
            template.status = 'accept'
            template.count++
            template.inc++
            template.synced = false
            result.push(template.code)
          } else {
            template.status = status
          }
        })
        chainNext()
      },
      fail: err => {
        console.log('notice subscribeIdList:fail', err)
        chainNext()
      },
    })

  }

  // 初始设置为正常流程，非正常流程在执行时往前面添加
  chain.push(doGetSetting)
  if (options.type == 'ask') {
    chain.push(doCheckMainSwitch)
    chain.push(doCheckReject)
    chain.push(doSubscribeAskList)
  } else if (options.type == 'accept' || !options.type) {
    chain.push(doSubscribeAcceptList)
  } else if (options.type == 'both') {
    chain.push(doCheckMainSwitch)
    chain.push(doCheckReject)
    chain.push(doSubscribeAcceptList)
    chain.push(doSubscribeAskList)
  } else {
    console.log('notice unsupported type ' + options.type)
  }
  chainNext()
  return true
}

// #endif

// #ifndef MP-WEIXIN
async function fetchCounts() {
  return Promise.reject()
}
function saveCounts() {
}
function resetCounts() {
}
function subscribe(options) {
  if (options?.fail) {
    options.fail()
  }
  if (options?.complete) {
    options.complete(false)
  }
  return true
}

function resetReject() {}
// #endif

export default {
  fetchCounts,
  saveCounts,
  resetCounts,
  resetReject,
  subscribe,
}



/*

小程序通知订阅限制
1、必须在用户点击事件中才能订阅
2、一次最多订阅3个不同的模板
3、一次订阅中每个模板订阅计数加一

家长小程序通知订阅方案

进入首页
1、满足以下条件的通知将弹出授权
     （1）当次进入小程序未被用户接受或拒绝过
     （2）需要询问用户的通知（授权接受时未勾选不再询问），订阅次数为0的通知（不管是否需要询问）
     （3）订阅次数少于50的通知
       小程序进入后台后被拒绝授权的将重新获得授权机会
2、弹出提示对话引导用户点击
3、弹出授权窗口（如果多于3个通知将分批授权，每批前弹出提示框）

进入报名页
1、课程报名提醒通知满足以下条件将弹出授权
    （1）需要询问用户且订阅数小于2，或订阅数为0时（不管是否需要询问）

进入通知页
1、学校通知提醒满足以下条件将弹出授权
    （1）需要询问用户且订阅数小于2，或订阅数为0时（不管是否需要询问）

点击报名按钮
1、待支付提醒通知满足以下条件将弹出授权
    （1）需要询问用户且订阅数小于50
2、待支付提醒通知满足以下条件时无弹框授权
    （2）用户已经接受过且勾选不再询问且订阅数小于50

点击退款按钮
1、退费申请驳回通知和退款申请通过通知满足以下条件将弹出授权
    （1）需要询问用户且订阅数小于50
2、退费申请驳回通知和退款申请通过通知满足以下条件时无弹框授权
    （2）用户已经接受过且勾选不再询问且订阅数小于50

对于用户已经接受且勾选不再询问的通知
在首页、课程、报名、用户等tabbar页面中，点击列表或部分按钮时触发订阅



*/