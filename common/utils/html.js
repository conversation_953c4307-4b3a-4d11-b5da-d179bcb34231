
function processImg(html) {
  // 图片宽度溢出片
  return html.replace(/<(img).*?(\/>|<\/img>)/g, m => {
    if (m.includes('style')) {
      return m.replace(/style=("|')/, 'style=$1max-width:100%;height:auto;')
    } else {
      return m.replace(/<\s*img/, '<img style="max-width:100%;height:auto;"')
    }
  })
}

function processTable(html) {
  // 表格宽度溢出处理
  html = html.replace(/<table[^>]*>/g, m => {
    if (m.includes('style')) {
      m = m.replace(/style=("|')/, 'style=$1max-width:100%;height:auto;')
      m = m.replace(/style=("|')[^"']+("|')/, m => {
        return m.replace(/width:[^;]+;/g, '')
      })
    }
    m = m.replace(/width=".*"/, '')
    return m
  })

  html = html.replace(/<td[^>]*>/g, m => {
    if (m.includes('style')) {
      m = m.replace(/style=("|')/, 'style=$1max-width:100%;height:auto;')
      m = m.replace(/style=("|')[^"']+("|')/, m => {
        return m.replace(/width:[^;]+;/g, '')
      })
    }
    m = m.replace(/width=".*"/, '')
    return m
  })

  return html
}

function process(html) {
  if (html) {
    html = processImg(html)
    html = processTable(html)
  }
  return html
}

export default {
  process
}
