// #ifdef MP-WEIXIN

function ChunkSource({
  url,
  header,
  data,
  method = 'GET',
  timeout = 90000,
}) {
  const chunkSource = {
    onOpen: null,
    onChunk: null,
    onComplete: null,
    onClose: null,
    onError: null,
    onEnd: null,
    close: null
  }

  let task = wx.request({
    url,
    header,
    data,
    enableChunked: true,
    responseType: 'arraybuffer',
    method,
    timeout,
    success: res => {
      if (!task) {
        return
      }
      task = null
      console.log('ChunkSource success', res)
      chunkSource.onComplete?.()
      chunkSource.onEnd?.()
    },
    fail: err => {
      if (!task) {
        return
      }
      task = null
      console.log('ChunkSource fail', err)
      chunkSource.onError?.(err)
      chunkSource.onEnd?.()
    }
  })

  task.onHeadersReceived(res => {
    chunkSource.onOpen?.(res)
  })

  task.onChunkReceived(res => {
    task && chunkSource.onChunk?.(res.data instanceof Uint8Array ? res.data : new Uint8Array(res.data))
  })

  chunkSource.close = () => {
    if (!task) {
      return
    }
    console.log('ChunkSource close')
    task.abort()
    task = null
    chunkSource.onClose?.()
    chunkSource.onEnd?.()
  }

  return chunkSource
}

export default ChunkSource

// #endif