import ChunkSource from '@/common/network/chunk-source'
import {
  getLines,
  getMessages
} from './fetch-event-source/parse'

function EventSource({
  url,
  header,
  data,
  method = 'GET',
  timeout = 90000,
}) {
  const eventSource = {
    onOpen: null,
    onMessage: null,
    onComplete: null,
    onClose: null,
    onError: null,
    onEnd: null,
    close: null
  }

  const chunkSource = new ChunkSource({
    url,
    header: {
      ...header,
      accept: 'text/event-stream'
    },
    data,
    method,
    timeout
  })

  const onChunk = getLines(getMessages((message) => {
    eventSource.onMessage?.(message)
  }, id => {}, retry => {}))

  chunkSource.onOpen = (res) => eventSource.onOpen?.(res)

  chunkSource.onChunk = (data) => onChunk(data)

  chunkSource.onComplete = () => eventSource.onComplete?.()

  chunkSource.onError = err => eventSource.onError?.(err)

  chunkSource.onClose = () => eventSource.onClose?.()

  chunkSource.onEnd = () => eventSource.onEnd?.()

  eventSource.close = chunkSource.close

  return eventSource
}

export default EventSource