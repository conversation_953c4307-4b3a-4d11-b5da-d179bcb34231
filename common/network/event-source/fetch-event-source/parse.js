class Utf8Decoder {

  constructor() {
    this.buffer = [];
  }

  decode(buffer) {
    this.buffer.push(...buffer);

    let out = '';
    let i = 0;

    while (i < this.buffer.length) {
      const c = this.buffer[i++];
      const ct = c >> 4
      if (ct <= 7) {
        // 0xxxxxxx
        out += String.fromCharCode(c);
      } else if (ct === 12 || ct === 13) {
        // 110x xxxx   10xx xxxx
        if (i >= this.buffer.length) {
          this.buffer = this.buffer.slice(i - 1);
          return out;
        }
        const char2 = this.buffer[i++];
        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
      } else if (ct === 14) {
        // 1110 xxxx  10xx xxxx  10xx xxxx
        if (i + 1 >= this.buffer.length) {
          this.buffer = this.buffer.slice(i - 1);
          return out;
        }
        const char2 = this.buffer[i++];
        const char3 = this.buffer[i++];
        out += String.fromCharCode(((c & 0x0F) << 12) |
          ((char2 & 0x3F) << 6) |
          (char3 & 0x3F));
      }
    }
    this.buffer = []
    return out;
  }
}

export async function getBytes(stream, onChunk) {
  const reader = stream.getReader();
  let result;
  while (!(result = await reader.read()).done) {
    onChunk(result.value);
  }
}
export function getLines(onLine) {
  let buffer;
  let position;
  let fieldLength;
  let discardTrailingNewline = false;
  return function onChunk(arr) {
    if (buffer === undefined) {
      buffer = arr;
      position = 0;
      fieldLength = -1;
    } else {
      buffer = concat(buffer, arr);
    }
    const bufLength = buffer.length;
    let lineStart = 0;
    while (position < bufLength) {
      if (discardTrailingNewline) {
        if (buffer[position] === 10) {
          lineStart = ++position;
        }
        discardTrailingNewline = false;
      }
      let lineEnd = -1;
      for (; position < bufLength && lineEnd === -1; ++position) {
        switch (buffer[position]) {
          case 58:
            if (fieldLength === -1) {
              fieldLength = position - lineStart;
            }
            break;
          case 13:
            discardTrailingNewline = true;
          case 10:
            lineEnd = position;
            break;
        }
      }
      if (lineEnd === -1) {
        break;
      }
      onLine(buffer.subarray(lineStart, lineEnd), fieldLength);
      lineStart = position;
      fieldLength = -1;
    }
    if (lineStart === bufLength) {
      buffer = undefined;
    } else if (lineStart !== 0) {
      buffer = buffer.subarray(lineStart);
      position -= lineStart;
    }
  };
}
export function getMessages(onMessage, onId, onRetry) {
  let message = newMessage();
  const decoder = typeof TextDecoder != 'undefined' ? new TextDecoder('utf-8') : new Utf8Decoder();
  return function onLine(line, fieldLength) {
    if (line.length === 0) {
      onMessage === null || onMessage === void 0 ? void 0 : onMessage(message);
      message = newMessage();
    } else if (fieldLength > 0) {
      const field = decoder.decode(line.subarray(0, fieldLength));
      const valueOffset = fieldLength + (line[fieldLength + 1] === 32 ? 2 : 1);
      const value = decoder.decode(line.subarray(valueOffset));
      switch (field) {
        case 'data':
          message.data = message.data ?
            message.data + '\n' + value :
            value;
          break;
        case 'event':
          message.event = value;
          break;
        case 'id':
          onId === null || onId === void 0 ? void 0 : onId(message.id = value);
          break;
        case 'retry':
          const retry = parseInt(value, 10);
          if (!isNaN(retry)) {
            onRetry === null || onRetry === void 0 ? void 0 : onRetry(message.retry = retry);
          }
          break;
      }
    }
  };
}

function concat(a, b) {
  const res = new Uint8Array(a.length + b.length);
  res.set(a);
  res.set(b, a.length);
  return res;
}

function newMessage() {
  return {
    data: '',
    event: '',
    id: '',
    retry: undefined,
  };
}
//# sourceMappingURL=parse.js.map