// 华云

const cfg = {}

// 非生产环境
// #ifndef ENV_PROD

const baseUrlList = [{
		env: 'dev',
		name: '开发环境',
		url: 'https://matchat-pre.hwzxs.com'
		// url: 'https://gpt-pre.hwzxs.com'
		// url: 'https://ai.huayuntiantu.com'
	},
	{
		env: 'test',
		name: '测试环境',
		url: 'https://matchat-pre.hwzxs.com'
	},
	{
		env: 'pre',
		name: '预生产环境',
		url: 'https://matchat-pre.hwzxs.com'
	},
	{
		env: 'cloud',
		name: '演示环境',
		url: 'https://gpt-pre.hwzxs.com'
	},
	{
		env: 'apaas',
		name: 'apaas环境',
		url: 'http://apaasshow-api.hwzxs.com'
	},
]

// #ifdef ENV_DEV
cfg.env = 'dev'
// #endif

// #ifdef ENV_TEST
cfg.env = 'test'
// #endif

// #ifdef ENV_PRE
cfg.env = 'pre'
// #endif

// #ifdef ENV_CLOUD
cfg.env = 'cloud'
// #endif

// #ifdef ENV_APAAS
cfg.env = 'apaas'
// #endif

cfg.baseUrl = baseUrlList.find(it => it.env == cfg.env).url
cfg.baseUrlList = baseUrlList

function getUrlEnv(url) {
	return baseUrlList.find(it => it.url == url)?.env
}

// 非预生产环境才恢复
if (cfg.env != 'pre') {
	const baseUrl = uni.getStorageSync('cfgBaseUrl')
	if (baseUrl) {
		cfg.env = getUrlEnv(baseUrl)
		cfg.baseUrl = baseUrl
	}
}

cfg.baseUrlChange = null
cfg.setBaseUrl = baseUrl => {
	cfg.env = getUrlEnv(baseUrl)
	cfg.baseUrl = baseUrl
	uni.setStorage({
		key: 'cfgBaseUrl',
		data: baseUrl
	})
	cfg.baseUrlChange && cfg.baseUrlChange(baseUrl)
}
// #endif

// 生产环境
// #ifdef ENV_PROD
cfg.env = 'prod'
cfg.baseUrl = 'https://ai.huayuntiantu.com'
cfg.baseUrlList = []
cfg.baseUrlChange = null
cfg.setBaseUrl = () => {}
// #endif

cfg.brand = 'huayun'

export default cfg