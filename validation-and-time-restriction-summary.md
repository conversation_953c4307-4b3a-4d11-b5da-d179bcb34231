# 需求订单校验和时间限制功能实现总结

## 1. 需求订单点击确定的必填校验

### 修改位置：`pages/instrument/reserve.vue`

#### 原有校验逻辑
- ✅ 实验名称（共享字段）
- ✅ 实验类型（共享字段）
- ✅ 预约时间（预约检测特有）
- ✅ 支付方式（非需求订单模式）

#### 新增校验逻辑
- ✅ 操作形式（预约检测特有）- 新增

#### 完整的必填校验列表
1. **实验名称**：`formData.name` - 必填
2. **实验类型**：`formData.type` - 必填
3. **操作形式**：`formData.operationMode`（仅预约检测）- 必填
4. **预约时间**：`formData.appointmentTimes`（仅预约检测）- 必填
5. **支付方式**：`paymentMethodIndex`（仅非需求订单模式）- 必填

#### 不需要校验的字段
- **样品来源**：`sampleMailed` - 布尔值，默认有值
- **样品寄回**：`returnSample` - 布尔值，默认有值
- **备注**：`remarks` - 非必填字段

### 校验方法实现
```javascript
validateForm() {
  // 共享字段验证
  if (!this.formData.name) {
    return { isValid: false, message: "请输入实验名称" };
  }

  if (!this.formData.type) {
    return { isValid: false, message: "请选择实验类型" };
  }

  // 预约检测特有验证
  if (this.tabIndex === 1) {
    // 操作形式验证
    if (!this.formData.operationMode) {
      return { isValid: false, message: "请选择操作形式" };
    }

    // 预约时间验证
    if (this.formData.appointmentTimes.length === 0) {
      return { isValid: false, message: "请选择预约时间" };
    }
  }

  // 支付方式验证（需求订单模式下不验证）
  if (!this.fromDemand) {
    if (
      this.formData.paymentMethodIndex === -1 ||
      this.paymentMethods.length === 0
    ) {
      return { isValid: false, message: "请选择支付方式" };
    }
  }

  return { isValid: true };
}
```

## 2. 时间选择页面的时间限制

### 修改位置：`pages/instrument/timeSelect.vue`

#### 新增功能
1. **提前预约时间限制**：根据仪器的 `leadTime` 字段限制可预约时间
2. **当前时间限制**：不能预约已过去的时间

#### 数据结构修改
```javascript
data() {
  return {
    leadTime: 0, // 提前预约时间（小时）
    // ... 其他字段
  }
}
```

#### 获取 leadTime
在 `fetchInstrumentSchedules` 方法中从仪器详情获取：
```javascript
if (instrumentDetail) {
  // 获取提前预约时间
  this.leadTime = instrumentDetail.leadTime || 0;
  console.log("获取到的提前预约时间:", this.leadTime, "小时");
  // ... 其他逻辑
}
```

#### 时间限制逻辑
修改 `isTimeSlotUnavailable` 方法，添加时间检查：
```javascript
isTimeSlotUnavailable(date, timeString, totalMinutes) {
  // 1. 检查是否超过当前时间或小于提前预约时间
  if (this.isTimeSlotTooEarly(date, timeString)) {
    return true;
  }
  
  // ... 其他检查逻辑
}
```

#### 新增时间检查方法
```javascript
isTimeSlotTooEarly(date, timeString) {
  const now = new Date();
  const slotDateTime = new Date(`${date} ${timeString}:00`);
  
  // 计算最早可预约时间（当前时间 + 提前预约时间）
  const earliestBookingTime = new Date(now.getTime() + this.leadTime * 60 * 60 * 1000);
  
  // 如果时间段早于最早可预约时间，则不可用
  if (slotDateTime <= earliestBookingTime) {
    console.log(`时间段 ${date} ${timeString} 太早，最早可预约时间：${earliestBookingTime.toLocaleString()}`);
    return true;
  }
  
  return false;
}
```

## 3. 时间限制规则说明

### 限制条件
1. **当前时间限制**：不能预约已经过去的时间段
2. **提前预约时间限制**：不能预约距离当前时间小于 `leadTime` 小时的时间段

### 计算公式
```
最早可预约时间 = 当前时间 + leadTime（小时）
```

### 示例
- 当前时间：2025-01-15 14:00
- leadTime：2小时
- 最早可预约时间：2025-01-15 16:00
- 结果：16:00之前的所有时间段都不可预约

## 4. 测试要点

### 需求订单校验测试
1. 进入需求订单模式的预约页面
2. 尝试不填写必填字段直接点击"确定"
3. 验证是否显示相应的错误提示
4. 填写完整信息后确认能正常提交

### 时间限制测试
1. 进入时间选择页面
2. 检查当前时间之前的时间段是否显示为不可用
3. 检查距离当前时间小于 leadTime 的时间段是否不可用
4. 验证控制台是否输出相关的调试信息

### 测试场景
- leadTime = 0：只限制过去时间
- leadTime = 2：限制过去时间 + 未来2小时内的时间
- leadTime = 24：限制过去时间 + 未来24小时内的时间

## 5. 注意事项

1. **时区问题**：确保时间计算使用正确的时区
2. **边界情况**：注意处理跨天、跨月的时间计算
3. **用户体验**：不可用时间段应该有明确的视觉提示
4. **性能考虑**：时间检查逻辑应该高效，避免影响页面渲染性能
