import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

const state = {
  loginType: uni.getStorageSync('loginType') || '', // phone wechat
  userInfo: uni.getStorageSync('userInfo') || null,
  token: uni.getStorageSync('token') || '',
  wxOpenId: uni.getStorageSync('wxOpenId') || '',
  roleLevel: uni.getStorageSync('roleLevel') || 0,
  carBadge: 0,
  messageBadge: 0,
  loginBackPath: '', // 登录返回页面
  flowList:uni.getStorageSync('flowList') || '',
  hiddenContent:'',
  userRole: uni.getStorageSync('userRole') || 'user', // 用户角色：user或admin
}
const mutations = {
  // 登录方式
  SET_LOGIN_TYPE(state, loginType) {
    state.loginType = loginType
    uni.setStorageSync('loginType', loginType)
  },
  // 设置用户角色
  SET_USER_ROLE(state, role) {
    state.userRole = role
    uni.setStorageSync('userRole', role)
  },
  // 设置用户信息
  SET_USERINFO(state, userInfo) {
    if (userInfo?.studentId == 'null') {
      userInfo.studentId = ''
    }
    state.userInfo = userInfo
    uni.setStorageSync('userInfo', userInfo)
  },
  // 清除用户信息
  REMOVE_USERINFO(state, userInfo) {
    state.userInfo = null
    uni.removeStorageSync('userInfo')
  },
  // 设置token
  SET_TOKEN(state, token) {
    state.token = token
    uni.setStorageSync('token', token)
  },
  // 清除token
  REMOVE_TOKEN(state, token) {
    state.token = ''
    uni.removeStorageSync('token')
  },
  // 设置flowList
  SET_FLOWLIST(state, flowList) {
    state.flowList = flowList
    uni.setStorageSync('flowList', flowList)
  },
  // 清除flowList
  REMOVE_FLOWLIST(state, flowList) {
    state.flowList = ''
    uni.removeStorageSync('flowList')
  },
  // 设置hiddenContent
  SET_HIDDENCONTENT(state, hiddenContent) {
    state.hiddenContent = hiddenContent
  },
  // 清除hiddenContent
  REMOVE_HIDDENCONTENT(state, hiddenContent) {
    state.hiddenContent = ''
  },
  // 设置responseData
  SET_RESPONSEDATA(state, responseData) {
    state.responseData = responseData
    uni.setStorageSync('responseData', responseData)
  },
  // 清除responseData
  REMOVE_RESPONSEDATA(state, responseData) {
    state.responseData = ''
    uni.removeStorageSync('responseData')
  },
  SET_WX_OPENID(state, openId) {
    state.wxOpenId = openId
    uni.setStorageSync('wxOpenId', openId)
  },
  REMOVE_WX_OPENID(state) {
    state.wxOpenId = ''
    uni.removeStorageSync('wxOpenId')
  },
  SET_LOGIN_BACK_PATH(state, path) {
    state.loginBackPath = path
  },
  // 清空
  CLEAR_STORAGE(state) {
    state.loginType = ''
    state.userInfo = null
    state.token = ''
    state.wxOpenId = ''
	state.flowList = null
	state.hiddenContent = ''
    state.userRole = 'user'
	
    uni.clearStorageSync()
  },
}
const actions = {}

const store = new Vuex.Store({
  state,
  mutations,
  actions
})

export default store